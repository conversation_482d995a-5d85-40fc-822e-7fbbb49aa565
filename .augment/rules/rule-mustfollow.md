---
type: "always_apply"
---

你要把你的回答分成两个部分，第一部分是工作计划，第二部分是具体执行。
你要在工作计划部分，详细得描述你接下去要如何执行你的工作。
你要在完成第一部分的工作计划的输出后，调用interactiveloop等待我的审核与反馈。
你要在接收到用户的反馈后，重新修改你的工作计划。然后再调用interactiveloop再次等待用户的反馈。
当我在反馈中明确说明你可以继续当前工作后，你才可以进入第二部分，开始执行具体的工作。
你要在完成第二部分的工作后，再次调用interactiveloop等待我对你工作的审核与反馈。
不要运行flutter run命令，用户会自己运行测试，然后给你反馈。
这是一个手机app，你的任何决策都要考虑是否使符合主流手机app上的最佳实践。
没有用户允许，不要随便添加测试页面。
没有用户允许，不要生成md格式的总结文档。