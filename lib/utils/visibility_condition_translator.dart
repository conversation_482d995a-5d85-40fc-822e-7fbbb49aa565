import '../models/spot_visibility.dart';

/// 可见性条件转义工具类
/// 
/// 将技术性的可见性条件转换为用户友好的文案
class VisibilityConditionTranslator {
  VisibilityConditionTranslator._();

  /// 转换可见性条件为友好文案
  /// 
  /// [visibility] 可见性级别
  /// [conditions] 可见性条件（JSON格式）
  /// 
  /// 返回用户友好的文案描述
  static String translateConditions(
    SpotVisibility visibility,
    Map<String, dynamic>? conditions,
  ) {
    switch (visibility) {
      case SpotVisibility.public:
        return '公开可见';
        
      case SpotVisibility.private:
        return '仅自己可见';
        
      case SpotVisibility.friendsOnly:
        return '仅关注者可见';
        
      case SpotVisibility.conditional:
        return _translateConditionalAccess(conditions);
    }
  }

  /// 转换条件访问的具体条件
  static String _translateConditionalAccess(Map<String, dynamic>? conditions) {
    if (conditions == null || conditions.isEmpty) {
      return '条件访问（条件未设置）';
    }

    final conditionType = conditions['type'] as String?;
    
    switch (conditionType) {
      case 'POINTS_DONATED':
        final minPoints = conditions['minPoints'] as int? ?? 0;
        return '需要向作者赠送 $minPoints 积分';
        
      case 'LEVEL_REQUIRED':
        final minLevel = conditions['minLevel'] as int? ?? 1;
        return '需要达到 $minLevel 级';
        
      case 'PAY_TO_VIEW':
        final price = conditions['price'] as int? ?? 0;
        return '付费查看（$price 积分）';
        
      default:
        return '条件访问（未知条件类型）';
    }
  }

  /// 获取可见性图标
  /// 
  /// [visibility] 可见性级别
  /// 
  /// 返回对应的图标名称
  static String getVisibilityIcon(SpotVisibility visibility) {
    switch (visibility) {
      case SpotVisibility.public:
        return 'public';
        
      case SpotVisibility.private:
        return 'lock';
        
      case SpotVisibility.friendsOnly:
        return 'group';
        
      case SpotVisibility.conditional:
        return 'star';
    }
  }

  /// 检查当前用户是否可以查看钓点
  /// 
  /// [visibility] 可见性级别
  /// [conditions] 可见性条件
  /// [currentUserId] 当前用户ID
  /// [spotOwnerId] 钓点创建者ID
  /// 
  /// 返回是否可以查看（简单判断，复杂逻辑需要调用SpotVisibilityService）
  static bool canCurrentUserView(
    SpotVisibility visibility,
    Map<String, dynamic>? conditions,
    String? currentUserId,
    String spotOwnerId,
  ) {
    // 未登录用户只能查看公开钓点
    if (currentUserId == null) {
      return visibility == SpotVisibility.public;
    }

    // 钓点创建者总是可以查看
    if (currentUserId == spotOwnerId) {
      return true;
    }

    // 公开钓点所有人都可以查看
    if (visibility == SpotVisibility.public) {
      return true;
    }

    // 私有钓点只有创建者可以查看
    if (visibility == SpotVisibility.private) {
      return false;
    }

    // 好友可见和条件访问需要更复杂的判断
    // 这里返回false，实际应该调用SpotVisibilityService进行判断
    return false;
  }
}
