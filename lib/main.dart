import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'pages/splash_screen.dart';
import 'pages/login_page.dart';
import 'pages/main_screen.dart';
import 'pages/user_agreement_page.dart';
import 'pages/activity_detail_page.dart';
import 'models/fishing_activity.dart';
import 'config/app_config.dart';
import 'config/pocketbase_config.dart';
import 'services/service_locator.dart';
import 'services/filter_config_service.dart';
import 'utils/database_migration_helper.dart';
import 'theme/app_theme_manager.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化 PocketBase 客户端（允许失败）
  try {
    await PocketBaseConfig.instance.initialize();
  } catch (e) {
    // PocketBase 初始化失败时，记录错误但不阻止应用启动
    debugPrint('PocketBase 初始化失败: $e');
    debugPrint('应用将在离线模式下启动');
  }

  // 初始化服务定位器
  try {
    debugPrint('开始初始化服务架构...');
    await serviceLocator.registerServices();
    await serviceLocator.initializeServices();
    serviceLocator.printServiceStatus();
    debugPrint('服务架构初始化完成');
  } catch (e) {
    debugPrint('服务架构初始化失败: $e');
    debugPrint('应用将使用降级模式启动');
  }

  // 初始化主题管理器
  try {
    await AppThemeManager.instance.initialize();
    debugPrint('主题管理器初始化完成');
  } catch (e) {
    debugPrint('主题管理器初始化失败: $e');
  }

  // 执行数据库健康检查
  try {
    debugPrint('开始数据库健康检查...');
    final healthCheck = await DatabaseMigrationHelper.performHealthCheck();

    if (!healthCheck['structure']) {
      debugPrint('⚠️ 数据库结构不完整，付费查看功能可能无法正常工作');
      DatabaseMigrationHelper.showDatabaseFixGuide();
    }
  } catch (e) {
    debugPrint('数据库健康检查失败: $e');
  }

  // 初始化过滤配置服务
  try {
    await FilterConfigService.instance.initialize();
    debugPrint('✅ 过滤配置服务初始化成功');
  } catch (e) {
    debugPrint('⚠️ 过滤配置服务初始化失败: $e');
  }

  // 打印配置信息（仅在开发模式下）
  AppConfig.instance.printConfigInfo();

  runApp(MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

// 全局访问器，用于搜索栏通知主应用焦点状态
_MyAppState? _globalAppState;

/// 全局函数：设置搜索栏焦点状态
void setGlobalSearchBarFocusState(bool focused) {
  _globalAppState?.setSearchBarFocusState(focused);
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();

  // 剪贴板检测防抖
  DateTime? _lastClipboardCheck;
  bool _isSearchBarFocused = false;

  @override
  void initState() {
    super.initState();
    _globalAppState = this; // 设置全局引用
    WidgetsBinding.instance.addObserver(this);

    // 延迟检测剪贴板，确保应用完全启动
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(seconds: 3), () {
        debugPrint('🔍 [主应用] 应用启动完成，开始检测剪贴板');
        _checkClipboardForShareLink();
      });
    });
  }

  @override
  void dispose() {
    _globalAppState = null; // 清理全局引用
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // 应用从后台恢复时检测剪贴板，但要避免在搜索交互时触发
    if (state == AppLifecycleState.resumed) {
      final now = DateTime.now();

      // 防抖机制：5秒内不重复检测
      if (_lastClipboardCheck == null ||
          now.difference(_lastClipboardCheck!).inSeconds > 5) {
        // 如果搜索栏正在使用，延迟检测
        if (_isSearchBarFocused) {
          debugPrint('🔍 [主应用] 搜索栏正在使用，延迟剪贴板检测');
          Future.delayed(const Duration(seconds: 2), () {
            if (!_isSearchBarFocused) {
              _lastClipboardCheck = DateTime.now();
              _checkClipboardForShareLink();
            }
          });
        } else {
          _lastClipboardCheck = now;
          debugPrint('🔍 [主应用] 应用恢复，开始检测剪贴板');
          _checkClipboardForShareLink();
        }
      } else {
        debugPrint('🔍 [主应用] 剪贴板检测防抖，跳过此次检测');
      }
    }
  }

  /// 设置搜索栏焦点状态
  void setSearchBarFocusState(bool focused) {
    _isSearchBarFocused = focused;
    debugPrint('🔍 [主应用] 搜索栏焦点状态更新: ${focused ? "获得焦点" : "失去焦点"}');
  }

  /// 检测剪贴板中的分享链接
  Future<void> _checkClipboardForShareLink() async {
    try {
      if (!mounted) return;

      debugPrint('🔍 [主应用] 开始检测剪贴板分享链接');
      final spotId = await Services.share.detectShareLink();
      debugPrint(
        '🔍 [主应用] 检测结果: ${spotId != null ? "发现钓点ID $spotId" : "未发现分享链接"}',
      );

      if (spotId != null && mounted) {
        debugPrint('🔍 [主应用] 准备显示确认对话框');
        // 使用调度器确保在下一帧执行，避免BuildContext问题
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          if (!mounted) return;

          final context = _navigatorKey.currentContext;
          debugPrint(
            '🔍 [主应用] NavigatorKey context: ${context != null ? "有效" : "无效"}',
          );

          if (context != null) {
            try {
              debugPrint('🔍 [主应用] 开始显示确认对话框');
              // 显示确认对话框
              final shouldOpen = await Services.share.showShareLinkDialog(
                context,
                spotId,
              );
              debugPrint('🔍 [主应用] 用户选择: ${shouldOpen ? "查看" : "取消"}');

              if (shouldOpen && mounted && context.mounted) {
                debugPrint('🔍 [主应用] 开始处理分享链接跳转');
                // 处理分享链接跳转
                await Services.share.handleShareLink(context, spotId);
              } else {
                debugPrint('🔍 [主应用] 用户取消或context无效，跳过跳转');
              }
            } catch (e) {
              debugPrint('❌ [分享链接处理] 处理失败: $e');
            }
          } else {
            debugPrint('❌ [主应用] NavigatorKey context为空，无法显示对话框');
          }
        });
      }
    } catch (e) {
      debugPrint('❌ [应用启动] 检测剪贴板失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: AppThemeManager.instance,
      builder: (context, child) {
        final themeManager = AppThemeManager.instance;

        return MaterialApp(
          navigatorKey: _navigatorKey,
          title: '钓鱼了么',
          theme: themeManager.getLightThemeData(),
          darkTheme: themeManager.getDarkThemeData(),
          themeMode: themeManager.currentThemeMode,
          localizationsDelegates: [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('zh', 'CN'), // 中文简体
            Locale('en', 'US'), // 英文
          ],
          locale: const Locale('zh', 'CN'), // 默认使用中文
          initialRoute: '/splash',
          routes: {
            '/splash': (context) => const SplashScreen(),
            '/login': (context) => const LoginPage(),
            '/main': (context) => const MainScreen(),
            '/user-agreement': (context) => const UserAgreementPage(),
          },
          onGenerateRoute: (settings) {
            // 处理带参数的路由
            if (settings.name == '/activity-detail') {
              final activity = settings.arguments as FishingActivity;
              return MaterialPageRoute(
                builder: (context) => ActivityDetailPage(activity: activity),
              );
            }
            return null;
          },
        );
      },
    );
  }
}
