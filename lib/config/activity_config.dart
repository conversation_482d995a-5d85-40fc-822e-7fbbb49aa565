/// 活动相关配置常量
class ActivityConfig {
  // 私有构造函数，防止实例化
  ActivityConfig._();

  /// 活动名称配置
  static const int maxTitleLength = 100;
  static const int minTitleLength = 2;
  
  /// 活动描述配置
  static const int maxDescriptionLength = 500;
  
  /// 时间配置
  static const int maxFutureDays = 30; // 最多可以创建30天后的活动
  static const double minDuration = 0.5; // 最短0.5小时
  static const double maxDuration = 24.0; // 最长24小时
  static const double defaultDuration = 2.0; // 默认2小时
  
  /// 参与人数配置
  static const int minParticipants = 2;
  static const int maxParticipants = 50;
  static const int defaultMaxParticipants = 10;
  
  /// 图片配置
  static const int maxImages = 9;
  static const int maxImageSizeMB = 10;
  
  /// 位置配置
  static const double onSiteRangeMeters = 50.0; // 实地发布范围50米
  
  /// 缓存配置
  static const Duration formCacheExpiry = Duration(hours: 24);
  static const Duration autoSaveInterval = Duration(seconds: 30);
  
  /// UI配置
  static const Duration submitTimeout = Duration(seconds: 30);
  static const Duration imageUploadTimeout = Duration(minutes: 5);
  
  /// 拖拽配置
  static const double minSheetSize = 0.3;
  static const double defaultSheetSize = 0.5;
  static const double maxSheetSize = 0.85;
  static const List<double> snapSizes = [0.3, 0.5, 0.85];
  static const double snapVelocityThreshold = 500.0;
  
  /// 默认鱼种
  static const String defaultFishType = 'carp';
  
  /// 时间建议配置
  static const List<Duration> suggestedTimeOffsets = [
    Duration(hours: 1),   // 1小时后
    Duration(hours: 2),   // 2小时后
    Duration(hours: 4),   // 4小时后
    Duration(days: 1),    // 明天同一时间
    Duration(days: 2),    // 后天同一时间
  ];
  
  /// 持续时间选项
  static const List<double> durationOptions = [
    0.5,  // 30分钟
    1.0,  // 1小时
    1.5,  // 1.5小时
    2.0,  // 2小时
    3.0,  // 3小时
    4.0,  // 4小时
    6.0,  // 6小时
    8.0,  // 8小时
    12.0, // 12小时
    24.0, // 24小时
  ];
  
  /// 活动模板
  static const List<ActivityTemplate> activityTemplates = [
    ActivityTemplate(
      name: '晨钓',
      description: '清晨钓鱼，享受宁静时光',
      suggestedDuration: 3.0,
      suggestedTime: TimeOfDay(hour: 6, minute: 0),
      fishType: 'carp',
    ),
    ActivityTemplate(
      name: '夜钓',
      description: '夜晚钓鱼，体验不同乐趣',
      suggestedDuration: 4.0,
      suggestedTime: TimeOfDay(hour: 19, minute: 0),
      fishType: 'catfish',
    ),
    ActivityTemplate(
      name: '周末钓鱼',
      description: '周末休闲钓鱼活动',
      suggestedDuration: 6.0,
      suggestedTime: TimeOfDay(hour: 8, minute: 0),
      fishType: 'carp',
    ),
    ActivityTemplate(
      name: '野钓探索',
      description: '探索新钓点，寻找野生鱼类',
      suggestedDuration: 8.0,
      suggestedTime: TimeOfDay(hour: 7, minute: 0),
      fishType: 'wild_fish',
    ),
  ];
  
  /// 错误重试配置
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  
  /// 验证规则
  static bool isValidTitle(String title) {
    final trimmed = title.trim();
    return trimmed.length >= minTitleLength && trimmed.length <= maxTitleLength;
  }
  
  static bool isValidDescription(String description) {
    return description.length <= maxDescriptionLength;
  }
  
  static bool isValidDuration(double duration) {
    return duration >= minDuration && duration <= maxDuration;
  }
  
  static bool isValidParticipants(int participants) {
    return participants >= minParticipants && participants <= maxParticipants;
  }
  
  static bool isValidFutureTime(DateTime time) {
    final now = DateTime.now();
    final maxFuture = now.add(Duration(days: maxFutureDays));
    return time.isAfter(now) && time.isBefore(maxFuture);
  }
  
  /// 获取建议的钓鱼时间
  static List<DateTime> getSuggestedTimes() {
    final now = DateTime.now();
    return suggestedTimeOffsets.map((offset) => now.add(offset)).toList();
  }
  
  /// 获取持续时间显示文本
  static String getDurationText(double duration) {
    if (duration < 1.0) {
      return '${(duration * 60).toInt()}分钟';
    } else if (duration == duration.toInt()) {
      return '${duration.toInt()}小时';
    } else {
      final hours = duration.toInt();
      final minutes = ((duration - hours) * 60).toInt();
      return '$hours小时$minutes分钟';
    }
  }
  
  /// 格式化时间显示
  static String formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final targetDate = DateTime(dateTime.year, dateTime.month, dateTime.day);
    
    String dateStr;
    if (targetDate == today) {
      dateStr = '今天';
    } else if (targetDate == today.add(const Duration(days: 1))) {
      dateStr = '明天';
    } else if (targetDate == today.add(const Duration(days: 2))) {
      dateStr = '后天';
    } else {
      dateStr = '${dateTime.month}月${dateTime.day}日';
    }
    
    final timeStr = '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    return '$dateStr $timeStr';
  }
}

/// 活动模板类
class ActivityTemplate {
  final String name;
  final String description;
  final double suggestedDuration;
  final TimeOfDay suggestedTime;
  final String fishType;
  
  const ActivityTemplate({
    required this.name,
    required this.description,
    required this.suggestedDuration,
    required this.suggestedTime,
    required this.fishType,
  });
  
  /// 应用模板到指定日期
  DateTime applyToDate(DateTime date) {
    return DateTime(
      date.year,
      date.month,
      date.day,
      suggestedTime.hour,
      suggestedTime.minute,
    );
  }
}

/// 时间选择器辅助类
class TimeOfDay {
  final int hour;
  final int minute;
  
  const TimeOfDay({required this.hour, required this.minute});
}
