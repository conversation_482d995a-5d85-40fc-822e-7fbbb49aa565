/// Cloudflare R2 配置
///
/// 安全注意事项：
/// 1. 生产环境中，这些配置应该从环境变量或安全配置服务获取
/// 2. 不要将敏感信息硬编码在客户端代码中
/// 3. 考虑使用PocketBase后端代理上传请求
class R2Config {
  // 生产环境建议：通过PocketBase后端获取临时凭证
  static const String bucketName = 'fishing-app';
  static const String region = 'auto';
  static const String endpoint =
      'https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com';

  // 安全建议：这些密钥应该在后端管理，客户端通过API获取临时凭证
  // 当前为演示目的，实际部署时应移除
  // static const String accessKeyId = '2975b9f0b5ed91f29bec884c3fdcd4c8';
  // static const String secretAccessKey =
  //     '****************************************************************';

  // 图片配置
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'];
  static const int imageQuality = 85; // 压缩质量
  static const int maxImageWidth = 1920; // 16:9比例的宽度
  static const int maxImageHeight = 1080; // 16:9比例的高度

  // 视频配置
  static const int maxVideoSize = 100 * 1024 * 1024; // 100MB
  static const List<String> allowedVideoExtensions = [
    'mp4',
    'mov',
    'avi',
    'webm',
  ];
  static const int videoQuality = 70; // 视频压缩质量
  static const int maxVideoDuration = 60; // 最大视频时长（秒）

  // 缩略图配置
  static const int thumbnailQuality = 80; // 缩略图质量
  static const int thumbnailMaxWidth = 300; // 缩略图最大宽度
  static const int thumbnailMaxHeight = 300; // 缩略图最大高度

  // 智能压缩策略配置
  static const Map<String, int> imageQualityBySize = {
    'large': 70, // > 5MB
    'medium': 80, // > 2MB
    'small': 85, // <= 2MB
  };

  static const Map<String, int> videoQualityBySize = {
    'large': 60, // > 50MB
    'medium': 65, // > 20MB
    'small': 70, // <= 20MB
  };

  // 文件大小阈值（字节）
  static const int imageLargeThreshold = 5 * 1024 * 1024; // 5MB
  static const int imageMediumThreshold = 2 * 1024 * 1024; // 2MB
  static const int videoLargeThreshold = 50 * 1024 * 1024; // 50MB
  static const int videoMediumThreshold = 20 * 1024 * 1024; // 20MB

  // URL配置
  static const String cdnDomain =
      'https://fishing-app.your-domain.com'; // 可选：自定义域名

  /// 生成图片存储路径
  /// 格式：fishing_app/{userId}/{timestamp}_{uuid}.{ext}
  static String generateImagePath(
    String userId,
    String spotId,
    String extension,
  ) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final uuid = _generateShortUuid();
    return 'fishing_app/$userId/${timestamp}_$uuid.$extension';
  }

  /// 生成缩略图路径
  static String generateThumbnailPath(String originalPath) {
    final parts = originalPath.split('.');
    final extension = parts.last;
    final pathWithoutExt = parts.sublist(0, parts.length - 1).join('.');
    return '${pathWithoutExt}_thumb.$extension';
  }

  /// 生成短UUID（用于文件名）
  static String _generateShortUuid() {
    return DateTime.now().millisecondsSinceEpoch.toRadixString(36) +
        (DateTime.now().microsecond % 1000).toRadixString(36);
  }

  /// 获取完整的图片URL
  static String getImageUrl(String path) {
    if (cdnDomain.isNotEmpty) {
      return '$cdnDomain/$path';
    }
    return '$endpoint/$bucketName/$path';
  }

  /// 根据文件大小获取智能图片质量
  static int getSmartImageQuality(int fileSize) {
    if (fileSize > imageLargeThreshold) {
      return imageQualityBySize['large']!;
    } else if (fileSize > imageMediumThreshold) {
      return imageQualityBySize['medium']!;
    } else {
      return imageQualityBySize['small']!;
    }
  }

  /// 根据文件大小获取智能视频质量
  static int getSmartVideoQuality(int fileSize) {
    if (fileSize > videoLargeThreshold) {
      return videoQualityBySize['large']!;
    } else if (fileSize > videoMediumThreshold) {
      return videoQualityBySize['medium']!;
    } else {
      return videoQualityBySize['small']!;
    }
  }

  /// 检查文件大小是否超过限制
  static bool isFileSizeValid(int fileSize, bool isVideo) {
    return isVideo ? fileSize <= maxVideoSize : fileSize <= maxImageSize;
  }

  /// 获取文件大小描述
  static String getFileSizeCategory(int fileSize, bool isVideo) {
    if (isVideo) {
      if (fileSize > videoLargeThreshold) return 'large';
      if (fileSize > videoMediumThreshold) return 'medium';
      return 'small';
    } else {
      if (fileSize > imageLargeThreshold) return 'large';
      if (fileSize > imageMediumThreshold) return 'medium';
      return 'small';
    }
  }
}

/// 图片上传结果
class ImageUploadResult {
  final String url;
  final String path;
  final String thumbnailUrl;
  final String thumbnailPath;
  final int fileSize;
  final String mimeType;

  ImageUploadResult({
    required this.url,
    required this.path,
    required this.thumbnailUrl,
    required this.thumbnailPath,
    required this.fileSize,
    required this.mimeType,
  });

  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'path': path,
      'thumbnail_url': thumbnailUrl,
      'thumbnail_path': thumbnailPath,
      'file_size': fileSize,
      'mime_type': mimeType,
    };
  }

  factory ImageUploadResult.fromJson(Map<String, dynamic> json) {
    return ImageUploadResult(
      url: json['url'],
      path: json['path'],
      thumbnailUrl: json['thumbnail_url'],
      thumbnailPath: json['thumbnail_path'],
      fileSize: json['file_size'],
      mimeType: json['mime_type'],
    );
  }
}
