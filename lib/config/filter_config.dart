import 'package:flutter/foundation.dart';
import '../models/unified_marker.dart';

/// 过滤配置类
///
/// 定义所有过滤条件的配置字段
/// 支持序列化、反序列化、验证和默认值设置
class FilterConfig {
  // ==================== 基础条件 ====================

  /// 是否显示过期的标记
  final bool showExpired;

  /// 是否要求有实地标签
  final bool requireOnSite;

  /// 是否要求有实拍标签
  final bool requireRealPhotos;

  /// 是否要求有照片
  final bool requirePhotos;

  // ==================== 社交条件 ====================

  /// 是否要求点赞数大于不喜欢数
  final bool requirePositiveLikes;

  /// 是否只显示收藏的标记
  final bool onlyFavorites;

  /// 最小点赞数要求
  final int minLikesCount;

  // ==================== 个人关联条件 ====================

  /// 是否只显示我发布的钓点
  final bool onlyMySpots;

  /// 是否只显示我发布的活动
  final bool onlyMyActivities;

  /// 是否只显示我加入的活动
  final bool onlyJoinedActivities;

  // ==================== 类型过滤 ====================

  /// 启用的标记类型
  final Set<MarkerType> enabledTypes;

  /// 启用的钓点类型
  final Set<String> enabledSpotTypes;

  /// 启用的活动类型
  final Set<String> enabledActivityTypes;

  // ==================== 钓点类型过滤 ====================

  /// 是否显示路亚标点
  final bool showLureSpots;

  /// 是否显示传统钓点
  final bool showTraditionalSpots;

  /// 是否显示收费钓点
  final bool showPaidSpots;

  // ==================== 显示设置 ====================

  /// 最大显示数量
  final int maxDisplayCount;

  /// 是否启用距离排序
  final bool enableDistanceSorting;

  /// 最大显示距离（公里）
  final double? maxDisplayDistance;

  // ==================== 优先级权重 ====================

  /// 个人关联度权重 (0.0-1.0)
  final double personalWeight;

  /// 内容质量权重 (0.0-1.0)
  final double qualityWeight;

  /// 社交热度权重 (0.0-1.0)
  final double socialWeight;

  /// 时间新鲜度权重 (0.0-1.0)
  final double freshnessWeight;

  const FilterConfig({
    // 基础条件
    this.showExpired = false,
    this.requireOnSite = false,
    this.requireRealPhotos = false,
    this.requirePhotos = false,

    // 社交条件
    this.requirePositiveLikes = false,
    this.onlyFavorites = false,
    this.minLikesCount = 0,

    // 个人关联条件
    this.onlyMySpots = false,
    this.onlyMyActivities = false,
    this.onlyJoinedActivities = false,

    // 类型过滤
    this.enabledTypes = const {MarkerType.spot, MarkerType.activity},
    this.enabledSpotTypes = const {},
    this.enabledActivityTypes = const {},

    // 钓点类型过滤
    this.showLureSpots = true,
    this.showTraditionalSpots = true,
    this.showPaidSpots = true,

    // 显示设置
    this.maxDisplayCount = 200,
    this.enableDistanceSorting = false,
    this.maxDisplayDistance,

    // 优先级权重
    this.personalWeight = 0.4,
    this.qualityWeight = 0.3,
    this.socialWeight = 0.2,
    this.freshnessWeight = 0.1,
  });

  /// 创建默认配置
  factory FilterConfig.defaultConfig() {
    return const FilterConfig();
  }

  /// 创建宽松配置（显示更多内容）
  factory FilterConfig.relaxedConfig() {
    return const FilterConfig(
      showExpired: true,
      requireOnSite: false,
      requireRealPhotos: false,
      requirePhotos: false,
      requirePositiveLikes: false,
      maxDisplayCount: 500,
    );
  }

  /// 创建严格配置（只显示高质量内容）
  factory FilterConfig.strictConfig() {
    return const FilterConfig(
      showExpired: false,
      requireOnSite: true,
      requireRealPhotos: true,
      requirePhotos: true,
      requirePositiveLikes: true,
      minLikesCount: 5,
      maxDisplayCount: 100,
      qualityWeight: 0.5,
      socialWeight: 0.3,
      personalWeight: 0.2,
      freshnessWeight: 0.0,
    );
  }

  /// 创建个人化配置（优先显示个人相关内容）
  factory FilterConfig.personalizedConfig() {
    return const FilterConfig(
      onlyFavorites: false,
      personalWeight: 0.6,
      qualityWeight: 0.2,
      socialWeight: 0.1,
      freshnessWeight: 0.1,
      maxDisplayCount: 300,
    );
  }

  // ==================== 复制和修改 ====================

  /// 复制并修改配置
  FilterConfig copyWith({
    bool? showExpired,
    bool? requireOnSite,
    bool? requireRealPhotos,
    bool? requirePhotos,
    bool? requirePositiveLikes,
    bool? onlyFavorites,
    int? minLikesCount,
    bool? onlyMySpots,
    bool? onlyMyActivities,
    bool? onlyJoinedActivities,
    Set<MarkerType>? enabledTypes,
    Set<String>? enabledSpotTypes,
    Set<String>? enabledActivityTypes,
    bool? showLureSpots,
    bool? showTraditionalSpots,
    bool? showPaidSpots,
    int? maxDisplayCount,
    bool? enableDistanceSorting,
    double? maxDisplayDistance,
    double? personalWeight,
    double? qualityWeight,
    double? socialWeight,
    double? freshnessWeight,
  }) {
    return FilterConfig(
      showExpired: showExpired ?? this.showExpired,
      requireOnSite: requireOnSite ?? this.requireOnSite,
      requireRealPhotos: requireRealPhotos ?? this.requireRealPhotos,
      requirePhotos: requirePhotos ?? this.requirePhotos,
      requirePositiveLikes: requirePositiveLikes ?? this.requirePositiveLikes,
      onlyFavorites: onlyFavorites ?? this.onlyFavorites,
      minLikesCount: minLikesCount ?? this.minLikesCount,
      onlyMySpots: onlyMySpots ?? this.onlyMySpots,
      onlyMyActivities: onlyMyActivities ?? this.onlyMyActivities,
      onlyJoinedActivities: onlyJoinedActivities ?? this.onlyJoinedActivities,
      enabledTypes: enabledTypes ?? this.enabledTypes,
      enabledSpotTypes: enabledSpotTypes ?? this.enabledSpotTypes,
      enabledActivityTypes: enabledActivityTypes ?? this.enabledActivityTypes,
      showLureSpots: showLureSpots ?? this.showLureSpots,
      showTraditionalSpots: showTraditionalSpots ?? this.showTraditionalSpots,
      showPaidSpots: showPaidSpots ?? this.showPaidSpots,
      maxDisplayCount: maxDisplayCount ?? this.maxDisplayCount,
      enableDistanceSorting:
          enableDistanceSorting ?? this.enableDistanceSorting,
      maxDisplayDistance: maxDisplayDistance ?? this.maxDisplayDistance,
      personalWeight: personalWeight ?? this.personalWeight,
      qualityWeight: qualityWeight ?? this.qualityWeight,
      socialWeight: socialWeight ?? this.socialWeight,
      freshnessWeight: freshnessWeight ?? this.freshnessWeight,
    );
  }

  // ==================== 验证 ====================

  /// 验证配置的有效性
  List<String> validate() {
    final errors = <String>[];

    // 验证权重总和
    final totalWeight =
        personalWeight + qualityWeight + socialWeight + freshnessWeight;
    if ((totalWeight - 1.0).abs() > 0.01) {
      errors.add('权重总和必须等于1.0，当前为: ${totalWeight.toStringAsFixed(2)}');
    }

    // 验证权重范围
    if (personalWeight < 0.0 || personalWeight > 1.0) {
      errors.add('个人关联度权重必须在0.0-1.0之间');
    }
    if (qualityWeight < 0.0 || qualityWeight > 1.0) {
      errors.add('内容质量权重必须在0.0-1.0之间');
    }
    if (socialWeight < 0.0 || socialWeight > 1.0) {
      errors.add('社交热度权重必须在0.0-1.0之间');
    }
    if (freshnessWeight < 0.0 || freshnessWeight > 1.0) {
      errors.add('时间新鲜度权重必须在0.0-1.0之间');
    }

    // 验证显示数量
    if (maxDisplayCount <= 0) {
      errors.add('最大显示数量必须大于0');
    }
    if (maxDisplayCount > 1000) {
      errors.add('最大显示数量不能超过1000');
    }

    // 验证最小点赞数
    if (minLikesCount < 0) {
      errors.add('最小点赞数不能为负数');
    }

    // 验证最大显示距离
    if (maxDisplayDistance != null && maxDisplayDistance! <= 0) {
      errors.add('最大显示距离必须大于0');
    }

    // 验证启用的类型
    if (enabledTypes.isEmpty) {
      errors.add('至少需要启用一种标记类型');
    }

    return errors;
  }

  /// 检查配置是否有效
  bool get isValid => validate().isEmpty;

  // ==================== 序列化 ====================

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'show_expired': showExpired,
      'require_on_site': requireOnSite,
      'require_real_photos': requireRealPhotos,
      'require_photos': requirePhotos,
      'require_positive_likes': requirePositiveLikes,
      'only_favorites': onlyFavorites,
      'min_likes_count': minLikesCount,
      'only_my_spots': onlyMySpots,
      'only_my_activities': onlyMyActivities,
      'only_joined_activities': onlyJoinedActivities,
      'enabled_types': enabledTypes.map((type) => type.value).toList(),
      'enabled_spot_types': enabledSpotTypes.toList(),
      'enabled_activity_types': enabledActivityTypes.toList(),
      'max_display_count': maxDisplayCount,
      'enable_distance_sorting': enableDistanceSorting,
      'max_display_distance': maxDisplayDistance,
      'personal_weight': personalWeight,
      'quality_weight': qualityWeight,
      'social_weight': socialWeight,
      'freshness_weight': freshnessWeight,
    };
  }

  /// 从JSON创建实例
  factory FilterConfig.fromJson(Map<String, dynamic> json) {
    return FilterConfig(
      showExpired: json['show_expired'] ?? false,
      requireOnSite: json['require_on_site'] ?? false,
      requireRealPhotos: json['require_real_photos'] ?? false,
      requirePhotos: json['require_photos'] ?? false,
      requirePositiveLikes: json['require_positive_likes'] ?? false,
      onlyFavorites: json['only_favorites'] ?? false,
      minLikesCount: json['min_likes_count'] ?? 0,
      onlyMySpots: json['only_my_spots'] ?? false,
      onlyMyActivities: json['only_my_activities'] ?? false,
      onlyJoinedActivities: json['only_joined_activities'] ?? false,
      enabledTypes:
          (json['enabled_types'] as List<dynamic>?)
              ?.map((type) => MarkerType.fromString(type))
              .toSet() ??
          {MarkerType.spot, MarkerType.activity},
      enabledSpotTypes:
          (json['enabled_spot_types'] as List<dynamic>?)
              ?.map((type) => type.toString())
              .toSet() ??
          {},
      enabledActivityTypes:
          (json['enabled_activity_types'] as List<dynamic>?)
              ?.map((type) => type.toString())
              .toSet() ??
          {},
      maxDisplayCount: json['max_display_count'] ?? 200,
      enableDistanceSorting: json['enable_distance_sorting'] ?? false,
      maxDisplayDistance: json['max_display_distance']?.toDouble(),
      personalWeight: json['personal_weight']?.toDouble() ?? 0.4,
      qualityWeight: json['quality_weight']?.toDouble() ?? 0.3,
      socialWeight: json['social_weight']?.toDouble() ?? 0.2,
      freshnessWeight: json['freshness_weight']?.toDouble() ?? 0.1,
    );
  }

  // ==================== 比较和哈希 ====================

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FilterConfig &&
        other.showExpired == showExpired &&
        other.requireOnSite == requireOnSite &&
        other.requireRealPhotos == requireRealPhotos &&
        other.requirePhotos == requirePhotos &&
        other.requirePositiveLikes == requirePositiveLikes &&
        other.onlyFavorites == onlyFavorites &&
        other.minLikesCount == minLikesCount &&
        other.onlyMySpots == onlyMySpots &&
        other.onlyMyActivities == onlyMyActivities &&
        other.onlyJoinedActivities == onlyJoinedActivities &&
        other.enabledTypes.length == enabledTypes.length &&
        other.enabledTypes.containsAll(enabledTypes) &&
        other.enabledSpotTypes.length == enabledSpotTypes.length &&
        other.enabledSpotTypes.containsAll(enabledSpotTypes) &&
        other.enabledActivityTypes.length == enabledActivityTypes.length &&
        other.enabledActivityTypes.containsAll(enabledActivityTypes) &&
        other.maxDisplayCount == maxDisplayCount &&
        other.enableDistanceSorting == enableDistanceSorting &&
        other.maxDisplayDistance == maxDisplayDistance &&
        other.personalWeight == personalWeight &&
        other.qualityWeight == qualityWeight &&
        other.socialWeight == socialWeight &&
        other.freshnessWeight == freshnessWeight;
  }

  @override
  int get hashCode {
    return Object.hashAll([
      showExpired,
      requireOnSite,
      requireRealPhotos,
      requirePhotos,
      requirePositiveLikes,
      onlyFavorites,
      minLikesCount,
      onlyMySpots,
      onlyMyActivities,
      onlyJoinedActivities,
      enabledTypes,
      enabledSpotTypes,
      enabledActivityTypes,
      maxDisplayCount,
      enableDistanceSorting,
      maxDisplayDistance,
      personalWeight,
      qualityWeight,
      socialWeight,
      freshnessWeight,
    ]);
  }

  @override
  String toString() {
    return 'FilterConfig(maxDisplay: $maxDisplayCount, weights: P${personalWeight}/Q${qualityWeight}/S${socialWeight}/F${freshnessWeight})';
  }

  // ==================== 辅助方法 ====================

  /// 获取配置摘要
  String getSummary() {
    final conditions = <String>[];

    // ✅ 添加类型过滤信息
    final typeInfo = <String>[];
    if (showSpots) typeInfo.add('钓点');
    if (showActivities) typeInfo.add('活动');
    if (typeInfo.isNotEmpty) {
      conditions.add('显示${typeInfo.join('+')}');
    } else {
      conditions.add('隐藏所有类型');
    }

    if (!showExpired) conditions.add('未过期');
    if (requireOnSite) conditions.add('实地');
    if (requireRealPhotos) conditions.add('实拍');
    if (requirePhotos) conditions.add('有照片');
    if (requirePositiveLikes) conditions.add('正面评价');
    if (onlyFavorites) conditions.add('仅收藏');
    if (onlyMySpots) conditions.add('我的钓点');
    if (onlyMyActivities) conditions.add('我的活动');
    if (onlyJoinedActivities) conditions.add('已加入活动');

    return '${conditions.join('、')} (最多${maxDisplayCount}个)';
  }

  /// 检查是否为默认配置
  bool get isDefault => this == FilterConfig.defaultConfig();

  /// 检查是否启用了任何过滤条件
  bool get hasActiveFilters {
    return !showExpired ||
        requireOnSite ||
        requireRealPhotos ||
        requirePhotos ||
        requirePositiveLikes ||
        onlyFavorites ||
        minLikesCount > 0 ||
        onlyMySpots ||
        onlyMyActivities ||
        onlyJoinedActivities ||
        enabledTypes.length < 2 ||
        enabledSpotTypes.isNotEmpty ||
        enabledActivityTypes.isNotEmpty ||
        maxDisplayDistance != null ||
        !showLureSpots ||
        !showTraditionalSpots ||
        !showPaidSpots;
  }

  // ==================== 类型过滤辅助方法 ====================

  /// 是否显示钓点分享（基于 enabledTypes）
  bool get showSpots => enabledTypes.contains(MarkerType.spot);

  /// 是否显示活动分享（基于 enabledTypes）
  bool get showActivities => enabledTypes.contains(MarkerType.activity);

  /// 是否要求实地标签（映射到 requireOnSite）
  bool get requireFieldTag => requireOnSite;

  /// 是否要求照片标签（映射到 requireRealPhotos）
  bool get requirePhotoTag => requireRealPhotos;

  /// 是否要求较多点赞（映射到 requirePositiveLikes）
  bool get requireHighLikes => requirePositiveLikes;

  /// 创建新配置，更新钓点显示状态
  FilterConfig copyWithShowSpots(bool value) {
    debugPrint('🔧 [配置更新] copyWithShowSpots: $value');
    debugPrint(
      '🔧 [配置更新] 当前enabledTypes: ${enabledTypes.map((t) => t.value).toList()}',
    );

    final newTypes = Set<MarkerType>.from(enabledTypes);
    if (value) {
      newTypes.add(MarkerType.spot);
      debugPrint('🔧 [配置更新] 添加钓点类型');
    } else {
      newTypes.remove(MarkerType.spot);
      debugPrint('🔧 [配置更新] 移除钓点类型');
    }

    debugPrint(
      '🔧 [配置更新] 新enabledTypes: ${newTypes.map((t) => t.value).toList()}',
    );
    final newConfig = copyWith(enabledTypes: newTypes);
    debugPrint('🔧 [配置更新] 新配置摘要: ${newConfig.getSummary()}');

    return newConfig;
  }

  /// 创建新配置，更新活动显示状态
  FilterConfig copyWithShowActivities(bool value) {
    final newTypes = Set<MarkerType>.from(enabledTypes);
    if (value) {
      newTypes.add(MarkerType.activity);
    } else {
      newTypes.remove(MarkerType.activity);
    }
    return copyWith(enabledTypes: newTypes);
  }

  /// 创建新配置，更新实地标签要求
  FilterConfig copyWithRequireFieldTag(bool value) {
    return copyWith(requireOnSite: value);
  }

  /// 创建新配置，更新照片标签要求
  FilterConfig copyWithRequirePhotoTag(bool value) {
    return copyWith(requireRealPhotos: value);
  }

  /// 创建新配置，更新点赞要求
  FilterConfig copyWithRequireHighLikes(bool value) {
    return copyWith(requirePositiveLikes: value);
  }
}
