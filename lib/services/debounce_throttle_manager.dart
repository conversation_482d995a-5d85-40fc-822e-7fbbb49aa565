import 'dart:async';
import 'package:flutter/foundation.dart';

/// 防抖器
/// 
/// 在指定时间内，只有最后一次调用会被执行
class Debouncer {
  /// 防抖延迟时间
  final Duration delay;
  
  /// 定时器
  Timer? _timer;
  
  /// 是否已激活
  bool get isActive => _timer?.isActive ?? false;

  Debouncer({required this.delay});

  /// 执行防抖操作
  void call(VoidCallback action) {
    _timer?.cancel();
    _timer = Timer(delay, action);
  }

  /// 立即执行并取消防抖
  void executeNow(VoidCallback action) {
    _timer?.cancel();
    action();
  }

  /// 取消防抖
  void cancel() {
    _timer?.cancel();
    _timer = null;
  }

  /// 清理资源
  void dispose() {
    cancel();
  }
}

/// 节流器
/// 
/// 在指定时间内，最多只执行一次操作
class Throttler {
  /// 节流间隔时间
  final Duration interval;
  
  /// 上次执行时间
  DateTime? _lastExecuted;
  
  /// 定时器
  Timer? _timer;
  
  /// 是否已激活
  bool get isActive => _timer?.isActive ?? false;
  
  /// 距离上次执行的时间
  Duration get timeSinceLastExecution {
    if (_lastExecuted == null) return Duration.zero;
    return DateTime.now().difference(_lastExecuted!);
  }

  Throttler({required this.interval});

  /// 执行节流操作
  void call(VoidCallback action) {
    final now = DateTime.now();
    
    if (_lastExecuted == null || now.difference(_lastExecuted!) >= interval) {
      // 可以立即执行
      _lastExecuted = now;
      action();
    } else {
      // 需要等待，设置定时器
      _timer?.cancel();
      final remainingTime = interval - now.difference(_lastExecuted!);
      _timer = Timer(remainingTime, () {
        _lastExecuted = DateTime.now();
        action();
      });
    }
  }

  /// 立即执行并重置节流
  void executeNow(VoidCallback action) {
    _timer?.cancel();
    _lastExecuted = DateTime.now();
    action();
  }

  /// 取消节流
  void cancel() {
    _timer?.cancel();
    _timer = null;
  }

  /// 重置节流状态
  void reset() {
    cancel();
    _lastExecuted = null;
  }

  /// 清理资源
  void dispose() {
    cancel();
  }
}

/// 防抖和节流管理器
/// 
/// 专门用于地图快速移动时的请求优化
/// 避免重复和无效的网络请求
class DebounceThrottleManager {
  /// 地图移动防抖器（用于最终确定位置）
  late final Debouncer _mapMoveDebouncer;
  
  /// 地图移动节流器（用于中间状态更新）
  late final Throttler _mapMoveThrottler;
  
  /// 数据加载防抖器
  late final Debouncer _dataLoadDebouncer;
  
  /// 搜索防抖器
  late final Debouncer _searchDebouncer;
  
  /// 过滤防抖器
  late final Debouncer _filterDebouncer;
  
  /// 缓存清理节流器
  late final Throttler _cacheCleanupThrottler;
  
  /// 统计信息
  int _debouncedCalls = 0;
  int _throttledCalls = 0;
  int _executedCalls = 0;
  DateTime? _lastActivity;

  DebounceThrottleManager({
    Duration mapMoveDebounceDelay = const Duration(milliseconds: 500),
    Duration mapMoveThrottleInterval = const Duration(milliseconds: 100),
    Duration dataLoadDebounceDelay = const Duration(milliseconds: 300),
    Duration searchDebounceDelay = const Duration(milliseconds: 800),
    Duration filterDebounceDelay = const Duration(milliseconds: 200),
    Duration cacheCleanupThrottleInterval = const Duration(seconds: 5),
  }) {
    _mapMoveDebouncer = Debouncer(delay: mapMoveDebounceDelay);
    _mapMoveThrottler = Throttler(interval: mapMoveThrottleInterval);
    _dataLoadDebouncer = Debouncer(delay: dataLoadDebounceDelay);
    _searchDebouncer = Debouncer(delay: searchDebounceDelay);
    _filterDebouncer = Debouncer(delay: filterDebounceDelay);
    _cacheCleanupThrottler = Throttler(interval: cacheCleanupThrottleInterval);
  }

  // ==================== 地图移动优化 ====================

  /// 地图移动防抖（用于最终数据加载）
  void debounceMapMove(VoidCallback onMapMoveEnd) {
    _debouncedCalls++;
    _lastActivity = DateTime.now();
    
    debugPrint('🎯 [防抖] 地图移动防抖: ${_mapMoveDebouncer.delay.inMilliseconds}ms');
    
    _mapMoveDebouncer.call(() {
      _executedCalls++;
      debugPrint('✅ [防抖] 执行地图移动结束回调');
      onMapMoveEnd();
    });
  }

  /// 地图移动节流（用于中间状态更新）
  void throttleMapMove(VoidCallback onMapMoveUpdate) {
    _throttledCalls++;
    _lastActivity = DateTime.now();
    
    debugPrint('⏱️ [节流] 地图移动节流: ${_mapMoveThrottler.interval.inMilliseconds}ms');
    
    _mapMoveThrottler.call(() {
      _executedCalls++;
      debugPrint('✅ [节流] 执行地图移动更新回调');
      onMapMoveUpdate();
    });
  }

  /// 立即执行地图移动（紧急情况）
  void executeMapMoveNow(VoidCallback onMapMoveEnd) {
    debugPrint('🚀 [立即执行] 立即执行地图移动');
    _mapMoveDebouncer.executeNow(() {
      _executedCalls++;
      onMapMoveEnd();
    });
  }

  // ==================== 数据加载优化 ====================

  /// 数据加载防抖
  void debounceDataLoad(VoidCallback onDataLoad) {
    _debouncedCalls++;
    _lastActivity = DateTime.now();
    
    debugPrint('🎯 [防抖] 数据加载防抖: ${_dataLoadDebouncer.delay.inMilliseconds}ms');
    
    _dataLoadDebouncer.call(() {
      _executedCalls++;
      debugPrint('✅ [防抖] 执行数据加载回调');
      onDataLoad();
    });
  }

  /// 立即执行数据加载
  void executeDataLoadNow(VoidCallback onDataLoad) {
    debugPrint('🚀 [立即执行] 立即执行数据加载');
    _dataLoadDebouncer.executeNow(() {
      _executedCalls++;
      onDataLoad();
    });
  }

  // ==================== 搜索优化 ====================

  /// 搜索防抖
  void debounceSearch(VoidCallback onSearch) {
    _debouncedCalls++;
    _lastActivity = DateTime.now();
    
    debugPrint('🎯 [防抖] 搜索防抖: ${_searchDebouncer.delay.inMilliseconds}ms');
    
    _searchDebouncer.call(() {
      _executedCalls++;
      debugPrint('✅ [防抖] 执行搜索回调');
      onSearch();
    });
  }

  /// 取消搜索防抖
  void cancelSearch() {
    debugPrint('❌ [取消] 取消搜索防抖');
    _searchDebouncer.cancel();
  }

  // ==================== 过滤优化 ====================

  /// 过滤防抖
  void debounceFilter(VoidCallback onFilter) {
    _debouncedCalls++;
    _lastActivity = DateTime.now();
    
    debugPrint('🎯 [防抖] 过滤防抖: ${_filterDebouncer.delay.inMilliseconds}ms');
    
    _filterDebouncer.call(() {
      _executedCalls++;
      debugPrint('✅ [防抖] 执行过滤回调');
      onFilter();
    });
  }

  /// 立即执行过滤
  void executeFilterNow(VoidCallback onFilter) {
    debugPrint('🚀 [立即执行] 立即执行过滤');
    _filterDebouncer.executeNow(() {
      _executedCalls++;
      onFilter();
    });
  }

  // ==================== 缓存清理优化 ====================

  /// 缓存清理节流
  void throttleCacheCleanup(VoidCallback onCacheCleanup) {
    _throttledCalls++;
    _lastActivity = DateTime.now();
    
    debugPrint('⏱️ [节流] 缓存清理节流: ${_cacheCleanupThrottler.interval.inSeconds}s');
    
    _cacheCleanupThrottler.call(() {
      _executedCalls++;
      debugPrint('✅ [节流] 执行缓存清理回调');
      onCacheCleanup();
    });
  }

  // ==================== 状态管理 ====================

  /// 取消所有防抖和节流
  void cancelAll() {
    debugPrint('❌ [取消全部] 取消所有防抖和节流操作');
    
    _mapMoveDebouncer.cancel();
    _mapMoveThrottler.cancel();
    _dataLoadDebouncer.cancel();
    _searchDebouncer.cancel();
    _filterDebouncer.cancel();
    _cacheCleanupThrottler.cancel();
  }

  /// 重置所有状态
  void resetAll() {
    debugPrint('🔄 [重置] 重置所有防抖和节流状态');
    
    cancelAll();
    _mapMoveThrottler.reset();
    _cacheCleanupThrottler.reset();
    
    _debouncedCalls = 0;
    _throttledCalls = 0;
    _executedCalls = 0;
    _lastActivity = null;
  }

  /// 检查是否有活跃的操作
  bool get hasActiveOperations {
    return _mapMoveDebouncer.isActive ||
           _mapMoveThrottler.isActive ||
           _dataLoadDebouncer.isActive ||
           _searchDebouncer.isActive ||
           _filterDebouncer.isActive ||
           _cacheCleanupThrottler.isActive;
  }

  /// 获取活跃操作数量
  int get activeOperationsCount {
    int count = 0;
    if (_mapMoveDebouncer.isActive) count++;
    if (_mapMoveThrottler.isActive) count++;
    if (_dataLoadDebouncer.isActive) count++;
    if (_searchDebouncer.isActive) count++;
    if (_filterDebouncer.isActive) count++;
    if (_cacheCleanupThrottler.isActive) count++;
    return count;
  }

  // ==================== 统计和调试 ====================

  /// 获取统计信息
  Map<String, dynamic> getStats() {
    return {
      'debounced_calls': _debouncedCalls,
      'throttled_calls': _throttledCalls,
      'executed_calls': _executedCalls,
      'active_operations': activeOperationsCount,
      'last_activity': _lastActivity?.toIso8601String(),
      'efficiency_ratio': _executedCalls > 0 
          ? (_debouncedCalls + _throttledCalls) / _executedCalls 
          : 0.0,
    };
  }

  /// 打印统计信息
  void printStats() {
    final stats = getStats();
    debugPrint('📊 [防抖节流统计] ========== 防抖节流管理器统计 ==========');
    debugPrint('📊 [防抖节流统计] 防抖调用: ${stats['debounced_calls']}');
    debugPrint('📊 [防抖节流统计] 节流调用: ${stats['throttled_calls']}');
    debugPrint('📊 [防抖节流统计] 实际执行: ${stats['executed_calls']}');
    debugPrint('📊 [防抖节流统计] 活跃操作: ${stats['active_operations']}');
    debugPrint('📊 [防抖节流统计] 效率比率: ${(stats['efficiency_ratio'] as double).toStringAsFixed(2)}');
    debugPrint('📊 [防抖节流统计] 最后活动: ${stats['last_activity'] ?? '无'}');
    debugPrint('📊 [防抖节流统计] =============================================');
  }

  /// 清理资源
  void dispose() {
    debugPrint('🧹 [防抖节流] 开始清理资源');
    
    _mapMoveDebouncer.dispose();
    _mapMoveThrottler.dispose();
    _dataLoadDebouncer.dispose();
    _searchDebouncer.dispose();
    _filterDebouncer.dispose();
    _cacheCleanupThrottler.dispose();
    
    debugPrint('✅ [防抖节流] 资源清理完成');
  }
}
