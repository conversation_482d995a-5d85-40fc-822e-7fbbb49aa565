import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import '../models/unified_marker.dart';
import '../config/filter_config.dart';

/// 排序结果
class SortResult {
  /// 排序后的标记列表
  final List<UnifiedMarker> sortedMarkers;
  
  /// 排序统计信息
  final SortStats stats;

  const SortResult({
    required this.sortedMarkers,
    required this.stats,
  });

  @override
  String toString() {
    return 'SortResult(sorted: ${sortedMarkers.length}, stats: $stats)';
  }
}

/// 排序统计信息
class SortStats {
  /// 原始标记数量
  final int originalCount;
  
  /// 排序后数量
  final int sortedCount;
  
  /// 各维度平均分数
  final Map<String, double> averageScores;
  
  /// 排序耗时（毫秒）
  final int processingTimeMs;
  
  /// 使用的排序算法
  final String sortAlgorithm;

  const SortStats({
    required this.originalCount,
    required this.sortedCount,
    required this.averageScores,
    required this.processingTimeMs,
    required this.sortAlgorithm,
  });

  @override
  String toString() {
    return 'SortStats(${originalCount} markers, ${processingTimeMs}ms, algorithm: $sortAlgorithm)';
  }
}

/// 标记评分详情
class MarkerScore {
  /// 标记ID
  final String markerId;
  
  /// 总分
  final double totalScore;
  
  /// 个人关联度分数
  final double personalScore;
  
  /// 内容质量分数
  final double qualityScore;
  
  /// 社交热度分数
  final double socialScore;
  
  /// 时间新鲜度分数
  final double freshnessScore;
  
  /// 距离分数（如果启用距离排序）
  final double? distanceScore;

  const MarkerScore({
    required this.markerId,
    required this.totalScore,
    required this.personalScore,
    required this.qualityScore,
    required this.socialScore,
    required this.freshnessScore,
    this.distanceScore,
  });

  @override
  String toString() {
    return 'MarkerScore($markerId: ${totalScore.toStringAsFixed(3)} = P${personalScore.toStringAsFixed(2)} + Q${qualityScore.toStringAsFixed(2)} + S${socialScore.toStringAsFixed(2)} + F${freshnessScore.toStringAsFixed(2)})';
  }
}

/// 标记优先级计算器
/// 
/// 实现多维度权重算法，支持用户自定义权重配置
/// 提供排序性能优化和详细的评分分析
class MarkerPriorityCalculator {
  /// 当前用户位置（用于距离计算）
  LatLng? _currentLocation;
  
  /// 评分缓存（避免重复计算）
  final Map<String, MarkerScore> _scoreCache = {};
  
  /// 缓存过期时间
  final Duration _cacheExpiry = const Duration(minutes: 10);
  
  /// 上次清理缓存的时间
  DateTime _lastCacheCleanup = DateTime.now();

  /// 设置当前用户位置
  void setCurrentLocation(LatLng? location) {
    if (_currentLocation != location) {
      _currentLocation = location;
      _clearScoreCache(); // 位置变更时清理缓存
    }
  }

  // ==================== 主要排序接口 ====================

  /// 对标记列表进行排序
  SortResult sortMarkers(
    List<UnifiedMarker> markers,
    FilterConfig config,
  ) {
    final stopwatch = Stopwatch()..start();
    
    debugPrint('📊 [标记排序] 开始排序 ${markers.length} 个标记');
    
    // 定期清理评分缓存
    _cleanupCacheIfNeeded();
    
    if (markers.isEmpty) {
      return SortResult(
        sortedMarkers: [],
        stats: SortStats(
          originalCount: 0,
          sortedCount: 0,
          averageScores: {},
          processingTimeMs: 0,
          sortAlgorithm: 'none',
        ),
      );
    }
    
    // 计算所有标记的评分
    final markerScores = <MarkerScore>[];
    final scoreBreakdown = <String, List<double>>{
      'personal': [],
      'quality': [],
      'social': [],
      'freshness': [],
      'distance': [],
    };
    
    for (final marker in markers) {
      final score = calculateMarkerScore(marker, config);
      markerScores.add(score);
      
      // 收集统计数据
      scoreBreakdown['personal']!.add(score.personalScore);
      scoreBreakdown['quality']!.add(score.qualityScore);
      scoreBreakdown['social']!.add(score.socialScore);
      scoreBreakdown['freshness']!.add(score.freshnessScore);
      if (score.distanceScore != null) {
        scoreBreakdown['distance']!.add(score.distanceScore!);
      }
    }
    
    // 根据总分排序
    markerScores.sort((a, b) => b.totalScore.compareTo(a.totalScore));
    
    // 重新排列标记
    final sortedMarkers = markerScores.map((score) {
      return markers.firstWhere((marker) => marker.id == score.markerId);
    }).toList();
    
    stopwatch.stop();
    
    // 计算平均分数
    final averageScores = <String, double>{};
    for (final entry in scoreBreakdown.entries) {
      if (entry.value.isNotEmpty) {
        averageScores[entry.key] = entry.value.reduce((a, b) => a + b) / entry.value.length;
      }
    }
    
    final stats = SortStats(
      originalCount: markers.length,
      sortedCount: sortedMarkers.length,
      averageScores: averageScores,
      processingTimeMs: stopwatch.elapsedMilliseconds,
      sortAlgorithm: config.enableDistanceSorting ? 'weighted_with_distance' : 'weighted_priority',
    );
    
    debugPrint('📊 [标记排序] 排序完成: $stats');
    
    return SortResult(
      sortedMarkers: sortedMarkers,
      stats: stats,
    );
  }

  /// 计算单个标记的评分
  MarkerScore calculateMarkerScore(
    UnifiedMarker marker,
    FilterConfig config,
  ) {
    // 使用缓存提高性能
    final cacheKey = '${marker.id}_${config.hashCode}';
    final cachedScore = _scoreCache[cacheKey];
    if (cachedScore != null) {
      return cachedScore;
    }
    
    final score = _calculateMarkerScoreInternal(marker, config);
    _scoreCache[cacheKey] = score;
    
    return score;
  }

  // ==================== 评分计算实现 ====================

  /// 内部评分计算方法
  MarkerScore _calculateMarkerScoreInternal(
    UnifiedMarker marker,
    FilterConfig config,
  ) {
    // 计算各维度分数
    final personalScore = _calculatePersonalScore(marker, config);
    final qualityScore = _calculateQualityScore(marker, config);
    final socialScore = _calculateSocialScore(marker, config);
    final freshnessScore = _calculateFreshnessScore(marker, config);
    
    // 计算距离分数（如果启用）
    double? distanceScore;
    if (config.enableDistanceSorting && _currentLocation != null) {
      distanceScore = _calculateDistanceScore(marker, config);
    }
    
    // 计算加权总分
    double totalScore = personalScore * config.personalWeight +
                       qualityScore * config.qualityWeight +
                       socialScore * config.socialWeight +
                       freshnessScore * config.freshnessWeight;
    
    // 如果启用距离排序，距离分数作为额外的加权因子
    if (distanceScore != null) {
      totalScore *= (1.0 + distanceScore * 0.2); // 距离最多增加20%的权重
    }
    
    return MarkerScore(
      markerId: marker.id,
      totalScore: totalScore,
      personalScore: personalScore,
      qualityScore: qualityScore,
      socialScore: socialScore,
      freshnessScore: freshnessScore,
      distanceScore: distanceScore,
    );
  }

  /// 计算个人关联度分数 (0.0-1.0)
  double _calculatePersonalScore(UnifiedMarker marker, FilterConfig config) {
    double score = 0.0;
    
    // 我发布的内容
    if (marker.isMine) {
      score += 0.5;
    }
    
    // 我收藏的内容
    if (marker.isFavorited) {
      score += 0.3;
    }
    
    // 活动特有：我加入的活动
    if (marker is ActivityMarker && marker.isJoined) {
      score += 0.2;
    }
    
    return score.clamp(0.0, 1.0);
  }

  /// 计算内容质量分数 (0.0-1.0)
  double _calculateQualityScore(UnifiedMarker marker, FilterConfig config) {
    double score = 0.0;
    
    if (marker is SpotMarker) {
      // 实地标签
      if (marker.isOnSite) {
        score += 0.4;
      }
      
      // 实拍标签
      if (marker.hasRealPhotos) {
        score += 0.3;
      }
      
      // 有照片
      if (marker.hasPhotos) {
        score += 0.2;
      }
      
      // 钓点类型完整性
      if (marker.spotType != null && marker.spotType!.isNotEmpty) {
        score += 0.1;
      }
    } else if (marker is ActivityMarker) {
      // 有图片
      if (marker.hasImages) {
        score += 0.3;
      }
      
      // 参与度（作为质量指标）
      if (marker.maxParticipants > 0) {
        final participationRate = marker.currentParticipants / marker.maxParticipants;
        score += participationRate * 0.4;
      }
      
      // 活动类型完整性
      if (marker.activityType.isNotEmpty) {
        score += 0.1;
      }
      
      // 未来活动优先
      if (marker.startTime.isAfter(DateTime.now())) {
        score += 0.2;
      }
    }
    
    return score.clamp(0.0, 1.0);
  }

  /// 计算社交热度分数 (0.0-1.0)
  double _calculateSocialScore(UnifiedMarker marker, FilterConfig config) {
    double score = 0.0;
    
    if (marker is SpotMarker) {
      // 点赞数（使用对数缩放避免极值影响）
      if (marker.likesCount > 0) {
        score = math.log(marker.likesCount + 1) / math.log(101); // 100个赞为满分
      }
    } else if (marker is ActivityMarker) {
      // 参与人数（使用对数缩放）
      if (marker.currentParticipants > 0) {
        score = math.log(marker.currentParticipants + 1) / math.log(21); // 20人参与为满分
      }
    }
    
    return score.clamp(0.0, 1.0);
  }

  /// 计算时间新鲜度分数 (0.0-1.0)
  double _calculateFreshnessScore(UnifiedMarker marker, FilterConfig config) {
    final now = DateTime.now();
    
    if (marker is SpotMarker) {
      // 钓点：基于创建时间和更新时间
      final daysSinceCreated = now.difference(marker.created).inDays;
      final daysSinceUpdated = now.difference(marker.updated).inDays;
      
      // 使用更新时间和创建时间的较小值
      final daysSinceActivity = math.min(daysSinceCreated, daysSinceUpdated);
      
      // 30天内为满分，之后逐渐衰减
      return math.exp(-daysSinceActivity / 30.0);
    } else if (marker is ActivityMarker) {
      // 活动：基于开始时间
      if (marker.startTime.isAfter(now)) {
        // 未来活动：越近优先级越高
        final hoursUntilStart = marker.startTime.difference(now).inHours;
        if (hoursUntilStart <= 24) {
          return 1.0; // 24小时内的活动满分
        } else if (hoursUntilStart <= 168) { // 7天内
          return 1.0 - (hoursUntilStart - 24) / 144; // 线性衰减
        } else {
          return 0.2; // 7天后的活动基础分
        }
      } else {
        // 已开始的活动：时间越久分数越低
        final hoursSinceStart = now.difference(marker.startTime).inHours;
        if (hoursSinceStart <= 2) {
          return 0.8; // 刚开始的活动
        } else if (hoursSinceStart <= 24) {
          return 0.5; // 当天的活动
        } else {
          return 0.1; // 过期活动
        }
      }
    }
    
    return 0.0;
  }

  /// 计算距离分数 (0.0-1.0)
  double _calculateDistanceScore(UnifiedMarker marker, FilterConfig config) {
    if (_currentLocation == null) return 0.5; // 无位置信息时返回中性分数
    
    const Distance distance = Distance();
    final distanceKm = distance.as(
      LengthUnit.Kilometer,
      _currentLocation!,
      marker.location,
    );
    
    // 距离越近分数越高
    if (distanceKm <= 1.0) {
      return 1.0; // 1公里内满分
    } else if (distanceKm <= 5.0) {
      return 1.0 - (distanceKm - 1.0) / 4.0; // 1-5公里线性衰减
    } else if (distanceKm <= 20.0) {
      return 0.5 - (distanceKm - 5.0) / 30.0; // 5-20公里缓慢衰减
    } else {
      return math.max(0.1, 0.5 - distanceKm / 100.0); // 20公里以上快速衰减
    }
  }

  // ==================== 高级排序功能 ====================

  /// 获取标记的详细评分分析
  List<MarkerScore> getDetailedScores(
    List<UnifiedMarker> markers,
    FilterConfig config,
  ) {
    return markers.map((marker) => calculateMarkerScore(marker, config)).toList();
  }

  /// 按指定维度排序
  List<UnifiedMarker> sortByDimension(
    List<UnifiedMarker> markers,
    FilterConfig config,
    String dimension,
  ) {
    final markerScores = markers.map((marker) => 
      MapEntry(marker, calculateMarkerScore(marker, config))
    ).toList();
    
    switch (dimension.toLowerCase()) {
      case 'personal':
        markerScores.sort((a, b) => b.value.personalScore.compareTo(a.value.personalScore));
        break;
      case 'quality':
        markerScores.sort((a, b) => b.value.qualityScore.compareTo(a.value.qualityScore));
        break;
      case 'social':
        markerScores.sort((a, b) => b.value.socialScore.compareTo(a.value.socialScore));
        break;
      case 'freshness':
        markerScores.sort((a, b) => b.value.freshnessScore.compareTo(a.value.freshnessScore));
        break;
      case 'distance':
        markerScores.sort((a, b) {
          final aDistance = a.value.distanceScore ?? 0.0;
          final bDistance = b.value.distanceScore ?? 0.0;
          return bDistance.compareTo(aDistance);
        });
        break;
      default:
        markerScores.sort((a, b) => b.value.totalScore.compareTo(a.value.totalScore));
    }
    
    return markerScores.map((entry) => entry.key).toList();
  }

  /// 获取排序建议
  String getSortingRecommendation(List<UnifiedMarker> markers, FilterConfig config) {
    if (markers.isEmpty) return '无标记需要排序';
    
    final scores = getDetailedScores(markers, config);
    final avgPersonal = scores.map((s) => s.personalScore).reduce((a, b) => a + b) / scores.length;
    final avgQuality = scores.map((s) => s.qualityScore).reduce((a, b) => a + b) / scores.length;
    final avgSocial = scores.map((s) => s.socialScore).reduce((a, b) => a + b) / scores.length;
    final avgFreshness = scores.map((s) => s.freshnessScore).reduce((a, b) => a + b) / scores.length;
    
    if (avgPersonal > 0.7) {
      return '个人相关内容较多，当前权重配置合适';
    } else if (avgQuality > 0.6) {
      return '内容质量较高，建议增加质量权重';
    } else if (avgSocial > 0.5) {
      return '社交活跃度较高，建议增加社交权重';
    } else if (avgFreshness > 0.6) {
      return '内容较新鲜，建议增加时间权重';
    } else {
      return '内容分布均匀，建议使用平衡权重';
    }
  }

  // ==================== 缓存管理 ====================

  /// 清理评分缓存
  void _clearScoreCache() {
    _scoreCache.clear();
    debugPrint('🧹 [评分缓存] 缓存已清理');
  }

  /// 定期清理缓存
  void _cleanupCacheIfNeeded() {
    final now = DateTime.now();
    if (now.difference(_lastCacheCleanup) > _cacheExpiry) {
      _clearScoreCache();
      _lastCacheCleanup = now;
    }
  }

  // ==================== 统计和调试 ====================

  /// 获取计算器统计信息
  Map<String, dynamic> getCalculatorStats() {
    return {
      'has_current_location': _currentLocation != null,
      'cache_size': _scoreCache.length,
      'last_cache_cleanup': _lastCacheCleanup.toIso8601String(),
    };
  }

  /// 打印计算器统计信息
  void printCalculatorStats() {
    final stats = getCalculatorStats();
    debugPrint('📊 [排序统计] 位置信息: ${stats['has_current_location'] ? '已设置' : '未设置'}');
    debugPrint('📊 [排序统计] 缓存大小: ${stats['cache_size']}');
  }

  /// 清理资源
  void dispose() {
    _clearScoreCache();
    debugPrint('🧹 [排序计算器] 资源已清理');
  }
}
