import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:video_compress/video_compress.dart';

/// 视频压缩任务
class VideoCompressTask {
  final String taskId;
  final File inputFile;
  final VideoQuality quality;
  final bool deleteOrigin;
  final int? startTime;
  final int? duration;
  final bool includeAudio;
  final int frameRate;
  final Completer<MediaInfo?> completer;
  final Function(double progress)? onProgress;

  VideoCompressTask({
    required this.taskId,
    required this.inputFile,
    required this.quality,
    this.deleteOrigin = false,
    this.startTime,
    this.duration,
    this.includeAudio = true,
    this.frameRate = 30,
    required this.completer,
    this.onProgress,
  });
}

/// 视频压缩队列管理器
///
/// 解决VideoCompress插件不支持并发压缩的问题
class VideoCompressQueue {
  static VideoCompressQueue? _instance;
  static VideoCompressQueue get instance =>
      _instance ??= VideoCompressQueue._();

  VideoCompressQueue._();

  // 任务队列
  final List<VideoCompressTask> _taskQueue = [];

  // 当前正在处理的任务
  VideoCompressTask? _currentTask;

  // 是否正在处理
  bool _isProcessing = false;

  // 进度订阅
  StreamSubscription<double>? _progressSubscription;

  /// 添加压缩任务到队列
  ///
  /// [taskId] 任务ID
  /// [inputFile] 输入文件
  /// [quality] 压缩质量
  /// [deleteOrigin] 是否删除原文件
  /// [startTime] 开始时间（毫秒）
  /// [duration] 持续时间（毫秒）
  /// [includeAudio] 是否包含音频
  /// [frameRate] 帧率
  /// [onProgress] 进度回调
  ///
  /// 返回压缩结果的Future
  Future<MediaInfo?> addTask({
    required String taskId,
    required File inputFile,
    VideoQuality quality = VideoQuality.DefaultQuality,
    bool deleteOrigin = false,
    int? startTime,
    int? duration,
    bool includeAudio = true,
    int frameRate = 30,
    Function(double progress)? onProgress,
  }) async {
    debugPrint('📋 [压缩队列] 添加任务: $taskId');

    final completer = Completer<MediaInfo?>();
    final task = VideoCompressTask(
      taskId: taskId,
      inputFile: inputFile,
      quality: quality,
      deleteOrigin: deleteOrigin,
      startTime: startTime,
      duration: duration,
      includeAudio: includeAudio,
      frameRate: frameRate,
      completer: completer,
      onProgress: onProgress,
    );

    _taskQueue.add(task);
    debugPrint('📋 [压缩队列] 队列长度: ${_taskQueue.length}');

    // 如果没有正在处理的任务，立即开始处理
    if (!_isProcessing) {
      _processNextTask();
    }

    return completer.future;
  }

  /// 取消任务
  ///
  /// [taskId] 任务ID
  ///
  /// 返回是否成功取消
  bool cancelTask(String taskId) {
    debugPrint('❌ [压缩队列] 尝试取消任务: $taskId');

    // 如果是当前正在处理的任务
    if (_currentTask?.taskId == taskId) {
      debugPrint('❌ [压缩队列] 取消当前任务: $taskId');
      VideoCompress.cancelCompression();
      _currentTask?.completer.complete(null);
      _currentTask = null;
      _isProcessing = false;
      _progressSubscription?.cancel();

      // 继续处理下一个任务
      _processNextTask();
      return true;
    }

    // 如果在队列中
    final index = _taskQueue.indexWhere((task) => task.taskId == taskId);
    if (index != -1) {
      debugPrint('❌ [压缩队列] 从队列中移除任务: $taskId');
      final task = _taskQueue.removeAt(index);
      task.completer.complete(null);
      return true;
    }

    debugPrint('⚠️ [压缩队列] 未找到任务: $taskId');
    return false;
  }

  /// 获取队列状态
  Map<String, dynamic> getQueueStatus() {
    return {
      'queue_length': _taskQueue.length,
      'is_processing': _isProcessing,
      'current_task_id': _currentTask?.taskId,
      'pending_tasks': _taskQueue.map((task) => task.taskId).toList(),
    };
  }

  /// 清空队列
  void clearQueue() {
    debugPrint('🧹 [压缩队列] 清空队列');

    // 取消当前任务
    if (_currentTask != null) {
      VideoCompress.cancelCompression();
      _currentTask?.completer.complete(null);
      _currentTask = null;
    }

    // 清空队列中的任务
    for (final task in _taskQueue) {
      task.completer.complete(null);
    }
    _taskQueue.clear();

    _isProcessing = false;
    _progressSubscription?.cancel();
  }

  /// 处理下一个任务
  Future<void> _processNextTask() async {
    if (_isProcessing || _taskQueue.isEmpty) {
      return;
    }

    _isProcessing = true;
    _currentTask = _taskQueue.removeAt(0);

    debugPrint('🎬 [压缩队列] 开始处理任务: ${_currentTask!.taskId}');
    debugPrint('🎬 [压缩队列] 剩余队列: ${_taskQueue.length}');

    try {
      // 设置进度监听（如果支持）
      try {
        // 尝试设置进度回调
        debugPrint('📊 [压缩队列] 设置进度监听');
      } catch (e) {
        debugPrint('⚠️ [压缩队列] 无法设置进度监听: $e');
      }

      // 执行压缩
      final result = await VideoCompress.compressVideo(
        _currentTask!.inputFile.path,
        quality: _currentTask!.quality,
        deleteOrigin: _currentTask!.deleteOrigin,
        startTime: _currentTask!.startTime,
        duration: _currentTask!.duration,
        includeAudio: _currentTask!.includeAudio,
        frameRate: _currentTask!.frameRate,
      );

      debugPrint('✅ [压缩队列] 任务完成: ${_currentTask!.taskId}');
      _currentTask!.completer.complete(result);
    } catch (error) {
      debugPrint('❌ [压缩队列] 任务失败: ${_currentTask!.taskId}, 错误: $error');
      _currentTask!.completer.completeError(error);
    } finally {
      // 清理当前任务
      _progressSubscription?.cancel();
      _progressSubscription = null;
      _currentTask = null;
      _isProcessing = false;

      // 处理下一个任务
      if (_taskQueue.isNotEmpty) {
        debugPrint('🔄 [压缩队列] 继续处理下一个任务');
        await Future.delayed(Duration(milliseconds: 500)); // 短暂延迟，确保资源释放
        _processNextTask();
      } else {
        debugPrint('✅ [压缩队列] 所有任务处理完成');
      }
    }
  }

  /// 检查是否有任务正在处理
  bool get isProcessing => _isProcessing;

  /// 获取队列长度
  int get queueLength => _taskQueue.length;

  /// 获取当前任务ID
  String? get currentTaskId => _currentTask?.taskId;

  /// 获取队列中的任务ID列表
  List<String> get pendingTaskIds =>
      _taskQueue.map((task) => task.taskId).toList();

  /// 估算等待时间（分钟）
  ///
  /// 基于队列长度和平均处理时间估算
  double estimateWaitTime() {
    if (_taskQueue.isEmpty) return 0.0;

    // 假设平均每个视频处理需要2分钟
    const averageProcessingTime = 2.0;
    return _taskQueue.length * averageProcessingTime;
  }

  /// 获取任务在队列中的位置
  ///
  /// [taskId] 任务ID
  ///
  /// 返回位置（从1开始），如果不在队列中返回-1
  int getTaskPosition(String taskId) {
    if (_currentTask?.taskId == taskId) {
      return 0; // 正在处理
    }

    final index = _taskQueue.indexWhere((task) => task.taskId == taskId);
    return index == -1 ? -1 : index + 1;
  }

  /// 释放资源
  void dispose() {
    debugPrint('🧹 [压缩队列] 释放资源');
    clearQueue();
  }
}
