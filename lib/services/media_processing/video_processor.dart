import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:video_compress/video_compress.dart';

import 'media_processor.dart';
import 'video_compress_queue.dart';
import '../secure_image_upload_service.dart';
import '../../config/r2_config.dart';

/// 视频处理器
///
/// 集成VideoCompress插件，实现统一的MediaProcessor接口
class VideoProcessor extends MediaProcessor {
  @override
  MediaType get mediaType => MediaType.video;

  @override
  List<String> get supportedExtensions => R2Config.allowedVideoExtensions;

  @override
  Future<MediaProcessingResult?> processMedia({
    required File file,
    required String userId,
    required MediaProcessingConfig config,
    String? spotId,
    MediaProcessingProgressCallback? progressCallback,
  }) async {
    final stopwatch = Stopwatch()..start();

    try {
      debugPrint('🎥 [视频处理器] 开始处理视频: ${file.path}');

      // 1. 验证文件
      progressCallback?.call(0.1, '验证视频文件...');
      if (!await validateFile(file)) {
        debugPrint('❌ [视频处理器] 视频验证失败');
        return null;
      }

      // 2. 获取视频信息
      progressCallback?.call(0.2, '读取视频信息...');
      final fileInfo = await getFileInfo(file);
      final originalSize = fileInfo['size'] as int;
      final duration = fileInfo['duration'] as Duration?;

      // 3. 压缩视频（使用队列管理器）
      progressCallback?.call(0.3, '压缩视频...');

      // 生成任务ID
      final taskId = 'video_compress_${DateTime.now().millisecondsSinceEpoch}';
      final compressQueue = VideoCompressQueue.instance;

      // 检查队列状态
      final queueStatus = compressQueue.getQueueStatus();
      if (queueStatus['queue_length'] > 0 || queueStatus['is_processing']) {
        final waitTime = compressQueue.estimateWaitTime();
        progressCallback?.call(0.3, '排队等待处理... (预计等待 ${waitTime.toInt()} 分钟)');
        debugPrint('📋 [视频处理器] 任务加入队列，当前队列长度: ${queueStatus['queue_length']}');
      }

      final compressedInfo = await compressQueue.addTask(
        taskId: taskId,
        inputFile: file,
        quality: _getVideoQuality(config.quality),
        deleteOrigin: false,
        includeAudio: config.customParams['includeAudio'] ?? true,
        onProgress: (progress) {
          // 将压缩进度映射到总进度的30%-60%区间
          final mappedProgress = 0.3 + (progress / 100.0) * 0.3;
          progressCallback?.call(
            mappedProgress,
            '压缩视频... ${progress.toInt()}%',
          );
        },
      );

      if (compressedInfo == null || compressedInfo.file == null) {
        debugPrint('❌ [视频处理器] 视频压缩失败');
        return null;
      }

      progressCallback?.call(0.6, '生成缩略图...');

      // 4. 生成缩略图（如果需要）
      String? thumbnailUrl;
      if (config.generateThumbnail) {
        final thumbnailFile = await generateThumbnail(
          file: file,
          config: config,
        );
        if (thumbnailFile != null) {
          // 上传缩略图
          final secureUploadService = SecureImageUploadService();
          final thumbnailUploadResult = await secureUploadService
              .uploadImageIndependent(
                imageFile: thumbnailFile,
                userId: userId,
                customFileName:
                    '${DateTime.now().millisecondsSinceEpoch}_thumb',
              );

          thumbnailUrl = thumbnailUploadResult?.thumbnailUrl;

          // 清理缩略图临时文件
          await cleanupTempFiles([thumbnailFile]);
        }
      }

      progressCallback?.call(0.8, '上传视频...');

      // 5. 上传压缩后的视频
      final secureUploadService = SecureImageUploadService();
      final uploadResult =
          spotId != null
              ? await secureUploadService.uploadVideoIndependent(
                videoFile: compressedInfo.file!,
                userId: userId,
                customFileName:
                    '${DateTime.now().millisecondsSinceEpoch}_video',
              )
              : await secureUploadService.uploadVideoIndependent(
                videoFile: compressedInfo.file!,
                userId: userId,
              );

      if (uploadResult == null) {
        debugPrint('❌ [视频处理器] 视频上传失败');
        return null;
      }

      progressCallback?.call(1.0, '处理完成');
      stopwatch.stop();

      // 6. 清理临时文件
      if (!config.keepOriginal) {
        await cleanupTempFiles([compressedInfo.file!]);
      }

      // 7. 返回处理结果
      final compressedSize = compressedInfo.filesize ?? originalSize;
      final compressionRatio =
          originalSize > 0
              ? (originalSize - compressedSize) / originalSize
              : 0.0;

      debugPrint('✅ [视频处理器] 视频处理完成');
      debugPrint('🔍 [视频处理器] 原始大小: ${originalSize} bytes');
      debugPrint('🔍 [视频处理器] 压缩后大小: ${compressedSize} bytes');
      debugPrint(
        '🔍 [视频处理器] 压缩率: ${(compressionRatio * 100).toStringAsFixed(1)}%',
      );

      return MediaProcessingResult(
        originalUrl: uploadResult.originalUrl,
        thumbnailUrl: thumbnailUrl ?? uploadResult.thumbnailUrl,
        fileName: uploadResult.fileName,
        fileSize: compressedSize,
        mediaType: MediaType.video,
        width: compressedInfo.width,
        height: compressedInfo.height,
        duration: duration,
        compressionRatio: compressionRatio,
        processingTime: stopwatch.elapsed,
        metadata: {
          'original_size': originalSize,
          'compressed_size': compressedSize,
          'quality': config.quality,
          'include_audio': config.customParams['includeAudio'] ?? true,
        },
      );
    } catch (e) {
      debugPrint('❌ [视频处理器] 处理异常: $e');
      stopwatch.stop();

      // 检查是否是VideoCompress并发错误，如果是则自动重试
      if (e.toString().contains('Already have a compression process')) {
        debugPrint('🔄 [视频处理器] 检测到并发错误，准备自动重试...');
        return await _retryProcessMedia(
          file,
          userId,
          config,
          progressCallback,
          spotId,
        );
      }

      return null;
    }
  }

  /// 自动重试处理媒体文件
  ///
  /// 专门处理VideoCompress并发错误的重试逻辑
  Future<MediaProcessingResult?> _retryProcessMedia(
    File file,
    String userId,
    MediaProcessingConfig config,
    MediaProcessingProgressCallback? progressCallback,
    String? spotId,
  ) async {
    const maxRetries = 3;
    const baseDelay = Duration(seconds: 2);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint('🔄 [视频处理器] 自动重试 $attempt/$maxRetries');
        progressCallback?.call(0.1, '自动重试中... ($attempt/$maxRetries)');

        // 等待一段时间再重试，使用指数退避策略
        final delay = Duration(seconds: baseDelay.inSeconds * attempt);
        await Future.delayed(delay);

        // 等待队列空闲后重新尝试处理
        final compressQueue = VideoCompressQueue.instance;
        while (compressQueue.isProcessing || compressQueue.queueLength > 0) {
          debugPrint('⏰ [视频处理器] 等待压缩队列空闲...');
          await Future.delayed(Duration(seconds: 1));
        }

        // 重新尝试处理
        return await processMedia(
          file: file,
          userId: userId,
          config: config,
          progressCallback: progressCallback,
          spotId: spotId,
        );
      } catch (e) {
        debugPrint('❌ [视频处理器] 重试 $attempt 失败: $e');

        // 如果不是并发错误，直接失败
        if (!e.toString().contains('Already have a compression process')) {
          debugPrint('❌ [视频处理器] 非并发错误，停止重试');
          break;
        }

        // 如果是最后一次重试，失败
        if (attempt == maxRetries) {
          debugPrint('❌ [视频处理器] 达到最大重试次数，处理失败');
          progressCallback?.call(0.0, '处理失败，请稍后重试');
        }
      }
    }

    return null;
  }

  @override
  Future<File?> generateThumbnail({
    required File file,
    required MediaProcessingConfig config,
  }) async {
    try {
      debugPrint('🎥 [视频处理器] 生成缩略图: ${file.path}');

      final thumbnailFile = await VideoCompress.getFileThumbnail(
        file.path,
        quality: config.thumbnailQuality,
        position: 1000, // 从1秒处提取缩略图
      );

      debugPrint('✅ [视频处理器] 缩略图生成成功: ${thumbnailFile.path}');
      return thumbnailFile;
    } catch (e) {
      debugPrint('❌ [视频处理器] 生成缩略图异常: $e');
      return null;
    }
  }

  @override
  Future<bool> validateFile(File file) async {
    try {
      // 检查文件是否存在
      if (!await file.exists()) {
        debugPrint('❌ [视频处理器] 文件不存在: ${file.path}');
        return false;
      }

      // 检查文件大小
      final fileSize = await file.length();
      if (!R2Config.isFileSizeValid(fileSize, true)) {
        debugPrint('❌ [视频处理器] 文件大小超过限制: ${fileSize} bytes');
        return false;
      }

      // 检查文件扩展名
      if (!isSupported(file)) {
        debugPrint('❌ [视频处理器] 不支持的文件类型: ${file.path}');
        return false;
      }

      // 尝试获取视频信息来验证文件
      try {
        final info = await VideoCompress.getMediaInfo(file.path);
        if (info.duration == null || info.duration! <= 0) {
          debugPrint('❌ [视频处理器] 无效的视频文件: ${file.path}');
          return false;
        }

        // 检查视频时长
        final durationSeconds = info.duration! / 1000;
        if (durationSeconds > R2Config.maxVideoDuration) {
          debugPrint('❌ [视频处理器] 视频时长超过限制: ${durationSeconds}s');
          return false;
        }

        debugPrint(
          '✅ [视频处理器] 视频验证通过: ${info.width}x${info.height}, ${durationSeconds}s',
        );
        return true;
      } catch (e) {
        debugPrint('❌ [视频处理器] 获取视频信息失败: $e');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [视频处理器] 验证异常: $e');
      return false;
    }
  }

  @override
  Future<Map<String, dynamic>> getFileInfo(File file) async {
    try {
      final fileSize = await file.length();
      final info = await VideoCompress.getMediaInfo(file.path);

      final duration =
          info.duration != null
              ? Duration(milliseconds: info.duration!.toInt())
              : null;

      return {
        'size': fileSize,
        'width': info.width ?? 0,
        'height': info.height ?? 0,
        'duration': duration,
        'extension': file.path.split('.').last.toLowerCase(),
        'mime_type': _getMimeType(file),
        'size_category': R2Config.getFileSizeCategory(fileSize, true),
      };
    } catch (e) {
      debugPrint('❌ [视频处理器] 获取文件信息失败: $e');
      return {
        'size': 0,
        'width': 0,
        'height': 0,
        'duration': null,
        'extension': '',
        'mime_type': 'application/octet-stream',
        'size_category': 'unknown',
      };
    }
  }

  @override
  Future<void> cleanupTempFiles(List<File> files) async {
    for (final file in files) {
      try {
        if (await file.exists()) {
          await file.delete();
          debugPrint('🧹 [视频处理器] 清理临时文件: ${file.path}');
        }
      } catch (e) {
        debugPrint('⚠️ [视频处理器] 清理临时文件失败: ${file.path}, $e');
      }
    }
  }

  /// 将质量值转换为VideoCompress的质量枚举
  VideoQuality _getVideoQuality(int quality) {
    if (quality >= 80) {
      return VideoQuality.HighestQuality;
    } else if (quality >= 60) {
      return VideoQuality.DefaultQuality;
    } else if (quality >= 40) {
      return VideoQuality.MediumQuality;
    } else {
      return VideoQuality.LowQuality;
    }
  }

  /// 获取MIME类型
  String _getMimeType(File file) {
    final extension = file.path.split('.').last.toLowerCase();
    switch (extension) {
      case 'mp4':
        return 'video/mp4';
      case 'mov':
        return 'video/quicktime';
      case 'avi':
        return 'video/x-msvideo';
      case 'webm':
        return 'video/webm';
      default:
        return 'video/mp4';
    }
  }
}
