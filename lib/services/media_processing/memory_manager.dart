import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;

/// 内存使用统计
class MemoryStats {
  final int totalAllocated;
  final int currentUsage;
  final int peakUsage;
  final int tempFilesCount;
  final int tempFilesSize;

  const MemoryStats({
    required this.totalAllocated,
    required this.currentUsage,
    required this.peakUsage,
    required this.tempFilesCount,
    required this.tempFilesSize,
  });

  /// 获取格式化的内存使用信息
  Map<String, String> get formatted => {
    'total_allocated': _formatBytes(totalAllocated),
    'current_usage': _formatBytes(currentUsage),
    'peak_usage': _formatBytes(peakUsage),
    'temp_files_count': tempFilesCount.toString(),
    'temp_files_size': _formatBytes(tempFilesSize),
  };

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}

/// 临时文件信息
class TempFileInfo {
  final File file;
  final DateTime createdAt;
  final int size;
  final String purpose;

  TempFileInfo({
    required this.file,
    required this.createdAt,
    required this.size,
    required this.purpose,
  });

  /// 文件年龄
  Duration get age => DateTime.now().difference(createdAt);

  /// 是否过期
  bool get isExpired => age.inMinutes > 30; // 30分钟过期
}

/// 内存和临时文件管理器
class MemoryManager {
  static MemoryManager? _instance;
  static MemoryManager get instance => _instance ??= MemoryManager._();

  MemoryManager._();

  // 临时文件跟踪
  final Map<String, TempFileInfo> _tempFiles = {};

  // 内存使用跟踪
  int _currentMemoryUsage = 0;
  int _peakMemoryUsage = 0;
  int _totalAllocated = 0;

  // 清理定时器
  Timer? _cleanupTimer;

  /// 初始化内存管理器
  void initialize() {
    debugPrint('🧠 [内存管理] 初始化内存管理器');

    // 启动定期清理
    _startPeriodicCleanup();

    // 清理遗留的临时文件
    _cleanupOrphanedTempFiles();
  }

  /// 创建临时文件
  ///
  /// [data] 文件数据
  /// [extension] 文件扩展名
  /// [purpose] 文件用途
  ///
  /// 返回临时文件
  Future<File> createTempFile({
    required Uint8List data,
    required String extension,
    required String purpose,
  }) async {
    try {
      // 更新内存使用统计
      _updateMemoryUsage(data.length, true);

      // 创建临时文件
      final tempDir = Directory.systemTemp;
      final fileName =
          '${DateTime.now().millisecondsSinceEpoch}_${_generateRandomId()}.$extension';
      final tempFile = File(path.join(tempDir.path, 'fishing_app', fileName));

      // 确保目录存在
      await tempFile.parent.create(recursive: true);

      // 写入数据
      await tempFile.writeAsBytes(data);

      // 记录临时文件
      final fileInfo = TempFileInfo(
        file: tempFile,
        createdAt: DateTime.now(),
        size: data.length,
        purpose: purpose,
      );
      _tempFiles[tempFile.path] = fileInfo;

      debugPrint(
        '📁 [内存管理] 创建临时文件: ${tempFile.path} (${fileInfo.size} bytes, $purpose)',
      );

      return tempFile;
    } catch (e) {
      debugPrint('❌ [内存管理] 创建临时文件失败: $e');
      rethrow;
    }
  }

  /// 创建临时文件（从现有文件复制）
  ///
  /// [sourceFile] 源文件
  /// [purpose] 文件用途
  ///
  /// 返回临时文件
  Future<File> createTempFileFromFile({
    required File sourceFile,
    required String purpose,
  }) async {
    try {
      final data = await sourceFile.readAsBytes();
      final extension = path.extension(sourceFile.path).replaceFirst('.', '');

      return await createTempFile(
        data: data,
        extension: extension,
        purpose: purpose,
      );
    } catch (e) {
      debugPrint('❌ [内存管理] 从文件创建临时文件失败: $e');
      rethrow;
    }
  }

  /// 删除临时文件
  ///
  /// [file] 要删除的文件
  Future<void> deleteTempFile(File file) async {
    try {
      final filePath = file.path;
      final fileInfo = _tempFiles[filePath];

      if (await file.exists()) {
        await file.delete();
        debugPrint('🗑️ [内存管理] 删除临时文件: $filePath');
      }

      if (fileInfo != null) {
        _updateMemoryUsage(fileInfo.size, false);
        _tempFiles.remove(filePath);
      }
    } catch (e) {
      debugPrint('⚠️ [内存管理] 删除临时文件失败: ${file.path}, $e');
    }
  }

  /// 批量删除临时文件
  ///
  /// [files] 要删除的文件列表
  Future<void> deleteTempFiles(List<File> files) async {
    for (final file in files) {
      await deleteTempFile(file);
    }
  }

  /// 清理过期的临时文件
  Future<void> cleanupExpiredFiles() async {
    debugPrint('🧹 [内存管理] 开始清理过期临时文件');

    final expiredFiles = <String>[];

    for (final entry in _tempFiles.entries) {
      final fileInfo = entry.value;
      if (fileInfo.isExpired) {
        expiredFiles.add(entry.key);
        await deleteTempFile(fileInfo.file);
      }
    }

    if (expiredFiles.isNotEmpty) {
      debugPrint('🧹 [内存管理] 清理了 ${expiredFiles.length} 个过期临时文件');
    }
  }

  /// 清理所有临时文件
  Future<void> cleanupAllTempFiles() async {
    debugPrint('🧹 [内存管理] 清理所有临时文件');

    final allFiles = _tempFiles.values.map((info) => info.file).toList();
    await deleteTempFiles(allFiles);

    debugPrint('🧹 [内存管理] 已清理 ${allFiles.length} 个临时文件');
  }

  /// 获取内存使用统计
  MemoryStats getMemoryStats() {
    final tempFilesSize = _tempFiles.values
        .map((info) => info.size)
        .fold(0, (sum, size) => sum + size);

    return MemoryStats(
      totalAllocated: _totalAllocated,
      currentUsage: _currentMemoryUsage,
      peakUsage: _peakMemoryUsage,
      tempFilesCount: _tempFiles.length,
      tempFilesSize: tempFilesSize,
    );
  }

  /// 获取临时文件列表
  List<TempFileInfo> getTempFiles() {
    return _tempFiles.values.toList();
  }

  /// 检查内存使用是否过高
  bool isMemoryUsageHigh() {
    const maxMemoryUsage = 100 * 1024 * 1024; // 100MB
    return _currentMemoryUsage > maxMemoryUsage;
  }

  /// 强制垃圾回收
  void forceGarbageCollection() {
    debugPrint('🗑️ [内存管理] 强制垃圾回收');
    // 在Flutter中，我们无法直接触发GC，但可以清理我们的缓存
    cleanupExpiredFiles();
  }

  /// 释放资源
  void dispose() {
    debugPrint('🧠 [内存管理] 释放内存管理器资源');

    _cleanupTimer?.cancel();
    _cleanupTimer = null;

    // 清理所有临时文件
    cleanupAllTempFiles();
  }

  /// 启动定期清理
  void _startPeriodicCleanup() {
    _cleanupTimer = Timer.periodic(const Duration(minutes: 10), (timer) {
      cleanupExpiredFiles();

      // 如果内存使用过高，强制清理
      if (isMemoryUsageHigh()) {
        debugPrint('⚠️ [内存管理] 内存使用过高，执行强制清理');
        forceGarbageCollection();
      }
    });
  }

  /// 清理遗留的临时文件
  Future<void> _cleanupOrphanedTempFiles() async {
    try {
      final tempDir = Directory(
        path.join(Directory.systemTemp.path, 'fishing_app'),
      );

      if (await tempDir.exists()) {
        final files = await tempDir.list().toList();
        final now = DateTime.now();

        for (final entity in files) {
          if (entity is File) {
            final stat = await entity.stat();
            final age = now.difference(stat.modified);

            // 删除超过1小时的文件
            if (age.inHours > 1) {
              try {
                await entity.delete();
                debugPrint('🧹 [内存管理] 清理遗留临时文件: ${entity.path}');
              } catch (e) {
                debugPrint('⚠️ [内存管理] 清理遗留文件失败: ${entity.path}, $e');
              }
            }
          }
        }
      }
    } catch (e) {
      debugPrint('⚠️ [内存管理] 清理遗留临时文件失败: $e');
    }
  }

  /// 更新内存使用统计
  void _updateMemoryUsage(int bytes, bool isAllocation) {
    if (isAllocation) {
      _currentMemoryUsage += bytes;
      _totalAllocated += bytes;
      if (_currentMemoryUsage > _peakMemoryUsage) {
        _peakMemoryUsage = _currentMemoryUsage;
      }
    } else {
      _currentMemoryUsage = (_currentMemoryUsage - bytes).clamp(
        0,
        _currentMemoryUsage,
      );
    }
  }

  /// 生成随机ID
  String _generateRandomId() {
    return DateTime.now().microsecond.toRadixString(36);
  }
}
