import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';

/// 媒体处理错误类型
enum MediaErrorType {
  /// 文件不存在
  fileNotFound,

  /// 文件格式不支持
  unsupportedFormat,

  /// 文件大小超过限制
  fileSizeExceeded,

  /// 文件损坏
  fileCorrupted,

  /// 网络错误
  networkError,

  /// 权限错误
  permissionError,

  /// 存储空间不足
  insufficientStorage,

  /// 处理超时
  processingTimeout,

  /// 上传失败
  uploadFailed,

  /// 压缩失败
  compressionFailed,

  /// 缩略图生成失败
  thumbnailGenerationFailed,

  /// 未知错误
  unknown,
}

/// 媒体处理异常
class MediaProcessingException implements Exception {
  final MediaErrorType type;
  final String message;
  final String? details;
  final dynamic originalError;
  final StackTrace? stackTrace;

  const MediaProcessingException({
    required this.type,
    required this.message,
    this.details,
    this.originalError,
    this.stackTrace,
  });

  /// 从通用异常创建媒体处理异常
  factory MediaProcessingException.fromError(
    dynamic error, {
    StackTrace? stackTrace,
    String? customMessage,
  }) {
    MediaErrorType type;
    String message;

    if (error is FileSystemException) {
      if (error.osError?.errorCode == 2) {
        type = MediaErrorType.fileNotFound;
        message = '文件不存在';
      } else if (error.osError?.errorCode == 13) {
        type = MediaErrorType.permissionError;
        message = '权限不足';
      } else if (error.osError?.errorCode == 28) {
        type = MediaErrorType.insufficientStorage;
        message = '存储空间不足';
      } else {
        type = MediaErrorType.unknown;
        message = '文件系统错误';
      }
    } else if (error is SocketException) {
      type = MediaErrorType.networkError;
      message = '网络连接失败';
    } else if (error is TimeoutException) {
      type = MediaErrorType.processingTimeout;
      message = '处理超时';
    } else if (error is FormatException) {
      type = MediaErrorType.fileCorrupted;
      message = '文件格式错误';
    } else {
      type = MediaErrorType.unknown;
      message = customMessage ?? '未知错误';
    }

    return MediaProcessingException(
      type: type,
      message: message,
      details: error.toString(),
      originalError: error,
      stackTrace: stackTrace,
    );
  }

  /// 创建文件大小超限异常
  factory MediaProcessingException.fileSizeExceeded({
    required int actualSize,
    required int maxSize,
    required bool isVideo,
  }) {
    final mediaType = isVideo ? '视频' : '图片';
    final actualMB = (actualSize / (1024 * 1024)).toStringAsFixed(1);
    final maxMB = (maxSize / (1024 * 1024)).toStringAsFixed(1);

    return MediaProcessingException(
      type: MediaErrorType.fileSizeExceeded,
      message: '$mediaType文件大小超过限制',
      details: '当前大小: ${actualMB}MB, 最大限制: ${maxMB}MB',
    );
  }

  /// 创建不支持格式异常
  factory MediaProcessingException.unsupportedFormat({
    required String extension,
    required List<String> supportedFormats,
  }) {
    return MediaProcessingException(
      type: MediaErrorType.unsupportedFormat,
      message: '不支持的文件格式',
      details: '当前格式: $extension, 支持的格式: ${supportedFormats.join(', ')}',
    );
  }

  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.write('MediaProcessingException: $message');
    if (details != null) {
      buffer.write(' ($details)');
    }
    return buffer.toString();
  }

  /// 获取用户友好的错误消息
  String get userFriendlyMessage {
    switch (type) {
      case MediaErrorType.fileNotFound:
        return '文件不存在，请重新选择';
      case MediaErrorType.unsupportedFormat:
        return '不支持的文件格式，请选择其他文件';
      case MediaErrorType.fileSizeExceeded:
        return '文件太大，请选择较小的文件';
      case MediaErrorType.fileCorrupted:
        return '文件已损坏，请选择其他文件';
      case MediaErrorType.networkError:
        return '网络连接失败，请检查网络后重试';
      case MediaErrorType.permissionError:
        return '权限不足，请检查应用权限';
      case MediaErrorType.insufficientStorage:
        return '存储空间不足，请清理空间后重试';
      case MediaErrorType.processingTimeout:
        return '处理超时，请重试';
      case MediaErrorType.uploadFailed:
        return '上传失败，请重试';
      case MediaErrorType.compressionFailed:
        return '压缩失败，请重试';
      case MediaErrorType.thumbnailGenerationFailed:
        return '缩略图生成失败，请重试';
      case MediaErrorType.unknown:
        return '处理失败，请重试';
    }
  }

  /// 是否可以重试
  bool get canRetry {
    switch (type) {
      case MediaErrorType.networkError:
      case MediaErrorType.processingTimeout:
      case MediaErrorType.uploadFailed:
      case MediaErrorType.compressionFailed:
      case MediaErrorType.thumbnailGenerationFailed:
      case MediaErrorType.unknown:
        return true;
      case MediaErrorType.fileNotFound:
      case MediaErrorType.unsupportedFormat:
      case MediaErrorType.fileSizeExceeded:
      case MediaErrorType.fileCorrupted:
      case MediaErrorType.permissionError:
      case MediaErrorType.insufficientStorage:
        return false;
    }
  }
}

/// 媒体错误处理器
class MediaErrorHandler {
  static MediaErrorHandler? _instance;
  static MediaErrorHandler get instance => _instance ??= MediaErrorHandler._();

  MediaErrorHandler._();

  /// 处理错误并返回用户友好的消息
  String handleError(dynamic error, {StackTrace? stackTrace}) {
    final exception =
        error is MediaProcessingException
            ? error
            : MediaProcessingException.fromError(error, stackTrace: stackTrace);

    // 记录错误日志
    _logError(exception);

    return exception.userFriendlyMessage;
  }

  /// 验证文件并抛出相应异常
  Future<void> validateFile({
    required File file,
    required List<String> supportedExtensions,
    required int maxFileSize,
    required bool isVideo,
  }) async {
    // 检查文件是否存在
    if (!await file.exists()) {
      throw MediaProcessingException(
        type: MediaErrorType.fileNotFound,
        message: '文件不存在',
        details: file.path,
      );
    }

    // 检查文件扩展名
    final extension = file.path.split('.').last.toLowerCase();
    if (!supportedExtensions.contains(extension)) {
      throw MediaProcessingException.unsupportedFormat(
        extension: extension,
        supportedFormats: supportedExtensions,
      );
    }

    // 检查文件大小
    final fileSize = await file.length();
    if (fileSize > maxFileSize) {
      throw MediaProcessingException.fileSizeExceeded(
        actualSize: fileSize,
        maxSize: maxFileSize,
        isVideo: isVideo,
      );
    }

    // 检查文件是否可读
    try {
      await file.readAsBytes();
    } catch (e) {
      throw MediaProcessingException(
        type: MediaErrorType.fileCorrupted,
        message: '文件已损坏或无法读取',
        details: e.toString(),
        originalError: e,
      );
    }
  }

  /// 包装异步操作，自动处理常见异常
  Future<T?> wrapOperation<T>(
    Future<T> Function() operation, {
    String? operationName,
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
  }) async {
    int attempts = 0;

    while (attempts < maxRetries) {
      try {
        attempts++;
        return await operation();
      } catch (e, stackTrace) {
        final exception =
            e is MediaProcessingException
                ? e
                : MediaProcessingException.fromError(e, stackTrace: stackTrace);

        debugPrint(
          '❌ [错误处理] ${operationName ?? '操作'}失败 (尝试 $attempts/$maxRetries): ${exception.message}',
        );

        // 如果不能重试或已达到最大重试次数，抛出异常
        if (!exception.canRetry || attempts >= maxRetries) {
          throw exception;
        }

        // 等待后重试
        if (attempts < maxRetries) {
          debugPrint('⏳ [错误处理] ${retryDelay.inSeconds}秒后重试...');
          await Future.delayed(retryDelay);
        }
      }
    }

    return null;
  }

  /// 记录错误日志
  void _logError(MediaProcessingException exception) {
    debugPrint('❌ [媒体错误] ${exception.type.name}: ${exception.message}');
    if (exception.details != null) {
      debugPrint('📝 [媒体错误] 详情: ${exception.details}');
    }
    if (exception.originalError != null) {
      debugPrint('🔍 [媒体错误] 原始错误: ${exception.originalError}');
    }
  }

  /// 获取错误统计信息
  Map<String, dynamic> getErrorStatistics() {
    return {
      'supported_error_types':
          MediaErrorType.values.map((e) => e.name).toList(),
      'retryable_errors':
          MediaErrorType.values
              .where(
                (type) =>
                    MediaProcessingException(type: type, message: '').canRetry,
              )
              .map((e) => e.name)
              .toList(),
    };
  }
}
