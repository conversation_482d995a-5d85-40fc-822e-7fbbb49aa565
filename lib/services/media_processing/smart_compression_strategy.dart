import 'dart:io';
import 'package:flutter/foundation.dart';
import 'media_processor.dart';
import '../../config/r2_config.dart';

/// 压缩策略类型
enum CompressionStrategy {
  /// 高质量（文件较小时使用）
  highQuality,

  /// 平衡质量（中等文件大小）
  balanced,

  /// 高压缩（大文件时使用）
  highCompression,

  /// 自定义
  custom,
}

/// 智能压缩策略配置
class SmartCompressionConfig {
  /// 压缩策略
  final CompressionStrategy strategy;

  /// 目标文件大小（字节）
  final int? targetFileSize;

  /// 最大质量
  final int maxQuality;

  /// 最小质量
  final int minQuality;

  /// 是否启用自适应压缩
  final bool enableAdaptiveCompression;

  /// 压缩步长
  final int qualityStep;

  const SmartCompressionConfig({
    this.strategy = CompressionStrategy.balanced,
    this.targetFileSize,
    this.maxQuality = 95,
    this.minQuality = 50,
    this.enableAdaptiveCompression = true,
    this.qualityStep = 5,
  });

  /// 创建高质量配置
  factory SmartCompressionConfig.highQuality() {
    return const SmartCompressionConfig(
      strategy: CompressionStrategy.highQuality,
      maxQuality: 95,
      minQuality: 80,
      qualityStep: 3,
    );
  }

  /// 创建平衡配置
  factory SmartCompressionConfig.balanced() {
    return const SmartCompressionConfig(
      strategy: CompressionStrategy.balanced,
      maxQuality: 85,
      minQuality: 65,
      qualityStep: 5,
    );
  }

  /// 创建高压缩配置
  factory SmartCompressionConfig.highCompression() {
    return const SmartCompressionConfig(
      strategy: CompressionStrategy.highCompression,
      maxQuality: 75,
      minQuality: 50,
      qualityStep: 8,
    );
  }
}

/// 智能压缩策略管理器
class SmartCompressionStrategy {
  static SmartCompressionStrategy? _instance;
  static SmartCompressionStrategy get instance =>
      _instance ??= SmartCompressionStrategy._();

  SmartCompressionStrategy._();

  /// 根据文件信息创建智能压缩配置
  ///
  /// [file] 文件
  /// [mediaType] 媒体类型
  /// [customConfig] 自定义配置（可选）
  ///
  /// 返回优化的处理配置
  Future<MediaProcessingConfig> createSmartConfig({
    required File file,
    required MediaType mediaType,
    SmartCompressionConfig? customConfig,
  }) async {
    try {
      // 获取文件信息
      final fileSize = await file.length();
      final fileInfo = await _analyzeFile(file, mediaType);

      debugPrint('🧠 [智能压缩] 分析文件: ${file.path}');
      debugPrint('🧠 [智能压缩] 文件大小: ${_formatFileSize(fileSize)}');
      debugPrint('🧠 [智能压缩] 媒体类型: ${mediaType.name}');

      // 选择压缩策略
      final strategy =
          customConfig ?? _selectStrategy(fileSize, mediaType, fileInfo);

      // 计算最优质量
      final quality = _calculateOptimalQuality(fileSize, mediaType, strategy);

      // 创建处理配置
      final config = _createProcessingConfig(mediaType, quality, strategy);

      debugPrint('🧠 [智能压缩] 选择策略: ${strategy.strategy.name}');
      debugPrint('🧠 [智能压缩] 计算质量: $quality');

      return config;
    } catch (e) {
      debugPrint('❌ [智能压缩] 创建配置失败: $e');
      // 返回默认配置
      return _createDefaultConfig(mediaType);
    }
  }

  /// 自适应压缩（根据目标大小调整质量）
  ///
  /// [file] 原始文件
  /// [mediaType] 媒体类型
  /// [targetSize] 目标文件大小（字节）
  /// [maxAttempts] 最大尝试次数
  ///
  /// 返回最优配置
  Future<MediaProcessingConfig> adaptiveCompress({
    required File file,
    required MediaType mediaType,
    required int targetSize,
    int maxAttempts = 5,
  }) async {
    debugPrint('🎯 [自适应压缩] 目标大小: ${_formatFileSize(targetSize)}');

    int currentQuality = mediaType == MediaType.image ? 85 : 70;
    int minQuality = 30;

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      debugPrint('🔄 [自适应压缩] 尝试 $attempt/$maxAttempts, 质量: $currentQuality');

      final config = _createProcessingConfig(
        mediaType,
        currentQuality,
        const SmartCompressionConfig(strategy: CompressionStrategy.custom),
      );

      // 这里可以添加实际的压缩测试逻辑
      // 目前返回计算出的配置
      if (attempt == maxAttempts || currentQuality <= minQuality) {
        debugPrint('✅ [自适应压缩] 完成，最终质量: $currentQuality');
        return config;
      }

      // 调整质量
      currentQuality = (currentQuality * 0.8).round();
      if (currentQuality < minQuality) currentQuality = minQuality;
    }

    return _createDefaultConfig(mediaType);
  }

  /// 分析文件特征
  Future<Map<String, dynamic>> _analyzeFile(
    File file,
    MediaType mediaType,
  ) async {
    final info = <String, dynamic>{};

    try {
      final fileSize = await file.length();
      info['size'] = fileSize;
      info['size_category'] = R2Config.getFileSizeCategory(
        fileSize,
        mediaType == MediaType.video,
      );

      if (mediaType == MediaType.image) {
        // 图片特征分析
        info['complexity'] = _estimateImageComplexity(file);
      } else {
        // 视频特征分析
        info['duration'] = await _estimateVideoDuration(file);
      }
    } catch (e) {
      debugPrint('⚠️ [智能压缩] 文件分析失败: $e');
    }

    return info;
  }

  /// 选择压缩策略
  SmartCompressionConfig _selectStrategy(
    int fileSize,
    MediaType mediaType,
    Map<String, dynamic> fileInfo,
  ) {
    final sizeCategory = fileInfo['size_category'] as String? ?? 'medium';

    switch (sizeCategory) {
      case 'large':
        return SmartCompressionConfig.highCompression();
      case 'small':
        return SmartCompressionConfig.highQuality();
      default:
        return SmartCompressionConfig.balanced();
    }
  }

  /// 计算最优质量
  int _calculateOptimalQuality(
    int fileSize,
    MediaType mediaType,
    SmartCompressionConfig strategy,
  ) {
    int baseQuality;

    if (mediaType == MediaType.image) {
      baseQuality = R2Config.getSmartImageQuality(fileSize);
    } else {
      baseQuality = R2Config.getSmartVideoQuality(fileSize);
    }

    // 根据策略调整
    switch (strategy.strategy) {
      case CompressionStrategy.highQuality:
        baseQuality = (baseQuality * 1.1).round().clamp(
          strategy.minQuality,
          strategy.maxQuality,
        );
        break;
      case CompressionStrategy.highCompression:
        baseQuality = (baseQuality * 0.8).round().clamp(
          strategy.minQuality,
          strategy.maxQuality,
        );
        break;
      case CompressionStrategy.balanced:
      case CompressionStrategy.custom:
        baseQuality = baseQuality.clamp(
          strategy.minQuality,
          strategy.maxQuality,
        );
        break;
    }

    return baseQuality;
  }

  /// 创建处理配置
  MediaProcessingConfig _createProcessingConfig(
    MediaType mediaType,
    int quality,
    SmartCompressionConfig strategy,
  ) {
    if (mediaType == MediaType.image) {
      return MediaProcessingConfig.forImage(
        quality: quality,
        maxWidth: R2Config.maxImageWidth,
        maxHeight: R2Config.maxImageHeight,
      );
    } else {
      return MediaProcessingConfig.forVideo(quality: quality);
    }
  }

  /// 创建默认配置
  MediaProcessingConfig _createDefaultConfig(MediaType mediaType) {
    if (mediaType == MediaType.image) {
      return MediaProcessingConfig.forImage();
    } else {
      return MediaProcessingConfig.forVideo();
    }
  }

  /// 估算图片复杂度
  String _estimateImageComplexity(File file) {
    // 这里可以添加更复杂的图片分析逻辑
    // 目前返回默认值
    return 'medium';
  }

  /// 估算视频时长
  Future<Duration?> _estimateVideoDuration(File file) async {
    // 这里可以添加视频时长分析逻辑
    // 目前返回null
    return null;
  }

  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  /// 获取策略统计信息
  Map<String, dynamic> getStatistics() {
    return {
      'available_strategies':
          CompressionStrategy.values.map((e) => e.name).toList(),
      'default_image_quality': R2Config.imageQuality,
      'default_video_quality': R2Config.videoQuality,
      'quality_ranges': {
        'image': {'min': 50, 'max': 95, 'default': R2Config.imageQuality},
        'video': {'min': 30, 'max': 90, 'default': R2Config.videoQuality},
      },
    };
  }
}
