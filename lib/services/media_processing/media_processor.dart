import 'dart:io';
import 'dart:async';

/// 媒体类型枚举
enum MediaType { image, video }

/// 媒体处理配置
class MediaProcessingConfig {
  /// 压缩质量 (0-100)
  final int quality;
  
  /// 最大宽度
  final int? maxWidth;
  
  /// 最大高度
  final int? maxHeight;
  
  /// 是否生成缩略图
  final bool generateThumbnail;
  
  /// 缩略图质量 (0-100)
  final int thumbnailQuality;
  
  /// 缩略图最大宽度
  final int thumbnailMaxWidth;
  
  /// 缩略图最大高度
  final int thumbnailMaxHeight;
  
  /// 是否保留原始文件
  final bool keepOriginal;
  
  /// 自定义参数
  final Map<String, dynamic> customParams;

  const MediaProcessingConfig({
    this.quality = 85,
    this.maxWidth,
    this.maxHeight,
    this.generateThumbnail = true,
    this.thumbnailQuality = 80,
    this.thumbnailMaxWidth = 300,
    this.thumbnailMaxHeight = 300,
    this.keepOriginal = false,
    this.customParams = const {},
  });

  /// 创建图片处理配置
  factory MediaProcessingConfig.forImage({
    int quality = 85,
    int? maxWidth = 1920,
    int? maxHeight = 1080,
    bool generateThumbnail = true,
    int thumbnailQuality = 80,
  }) {
    return MediaProcessingConfig(
      quality: quality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
      generateThumbnail: generateThumbnail,
      thumbnailQuality: thumbnailQuality,
    );
  }

  /// 创建视频处理配置
  factory MediaProcessingConfig.forVideo({
    int quality = 70,
    bool generateThumbnail = true,
    int thumbnailQuality = 80,
    bool keepOriginal = false,
  }) {
    return MediaProcessingConfig(
      quality: quality,
      generateThumbnail: generateThumbnail,
      thumbnailQuality: thumbnailQuality,
      keepOriginal: keepOriginal,
      customParams: {
        'includeAudio': true,
        'deleteOrigin': !keepOriginal,
      },
    );
  }
}

/// 媒体处理结果
class MediaProcessingResult {
  /// 原始文件URL
  final String originalUrl;
  
  /// 缩略图URL
  final String? thumbnailUrl;
  
  /// 文件名
  final String fileName;
  
  /// 文件大小（字节）
  final int fileSize;
  
  /// 媒体类型
  final MediaType mediaType;
  
  /// 宽度（图片/视频）
  final int? width;
  
  /// 高度（图片/视频）
  final int? height;
  
  /// 时长（仅视频）
  final Duration? duration;
  
  /// 压缩率（0.0-1.0）
  final double? compressionRatio;
  
  /// 处理耗时
  final Duration processingTime;
  
  /// 额外信息
  final Map<String, dynamic> metadata;

  const MediaProcessingResult({
    required this.originalUrl,
    this.thumbnailUrl,
    required this.fileName,
    required this.fileSize,
    required this.mediaType,
    this.width,
    this.height,
    this.duration,
    this.compressionRatio,
    required this.processingTime,
    this.metadata = const {},
  });

  /// 是否为图片
  bool get isImage => mediaType == MediaType.image;

  /// 是否为视频
  bool get isVideo => mediaType == MediaType.video;

  /// 获取格式化的文件大小
  String get formattedFileSize {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  /// 获取格式化的时长
  String get formattedDuration {
    if (duration == null) return '';
    final seconds = duration!.inSeconds;
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'originalUrl': originalUrl,
      'thumbnailUrl': thumbnailUrl,
      'fileName': fileName,
      'fileSize': fileSize,
      'mediaType': mediaType.name,
      'width': width,
      'height': height,
      'duration': duration?.inMilliseconds,
      'compressionRatio': compressionRatio,
      'processingTime': processingTime.inMilliseconds,
      'metadata': metadata,
    };
  }

  /// 从JSON创建
  factory MediaProcessingResult.fromJson(Map<String, dynamic> json) {
    return MediaProcessingResult(
      originalUrl: json['originalUrl'],
      thumbnailUrl: json['thumbnailUrl'],
      fileName: json['fileName'],
      fileSize: json['fileSize'],
      mediaType: MediaType.values.firstWhere(
        (e) => e.name == json['mediaType'],
        orElse: () => MediaType.image,
      ),
      width: json['width'],
      height: json['height'],
      duration: json['duration'] != null 
          ? Duration(milliseconds: json['duration']) 
          : null,
      compressionRatio: json['compressionRatio']?.toDouble(),
      processingTime: Duration(milliseconds: json['processingTime'] ?? 0),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }
}

/// 媒体处理进度回调
typedef MediaProcessingProgressCallback = void Function(double progress, String? message);

/// 媒体处理器抽象基类
abstract class MediaProcessor {
  /// 媒体类型
  MediaType get mediaType;

  /// 支持的文件扩展名
  List<String> get supportedExtensions;

  /// 验证文件是否支持
  bool isSupported(File file) {
    final extension = file.path.split('.').last.toLowerCase();
    return supportedExtensions.contains(extension);
  }

  /// 处理媒体文件
  /// 
  /// [file] 原始文件
  /// [userId] 用户ID
  /// [config] 处理配置
  /// [spotId] 钓点ID（可选）
  /// [progressCallback] 进度回调
  /// 
  /// 返回处理结果
  Future<MediaProcessingResult?> processMedia({
    required File file,
    required String userId,
    required MediaProcessingConfig config,
    String? spotId,
    MediaProcessingProgressCallback? progressCallback,
  });

  /// 生成缩略图
  /// 
  /// [file] 原始文件
  /// [config] 处理配置
  /// 
  /// 返回缩略图文件
  Future<File?> generateThumbnail({
    required File file,
    required MediaProcessingConfig config,
  });

  /// 验证文件
  /// 
  /// [file] 要验证的文件
  /// 
  /// 返回验证结果
  Future<bool> validateFile(File file);

  /// 获取文件信息
  /// 
  /// [file] 文件
  /// 
  /// 返回文件信息
  Future<Map<String, dynamic>> getFileInfo(File file);

  /// 清理临时文件
  /// 
  /// [files] 要清理的文件列表
  Future<void> cleanupTempFiles(List<File> files);
}
