import 'dart:io';
import 'package:flutter/foundation.dart';
import 'media_processor.dart';
import 'image_processor.dart';
import 'video_processor.dart';
import 'smart_compression_strategy.dart';
import '../../config/r2_config.dart';

/// 媒体处理器工厂
class MediaProcessorFactory {
  static MediaProcessorFactory? _instance;
  static MediaProcessorFactory get instance =>
      _instance ??= MediaProcessorFactory._();

  MediaProcessorFactory._();

  // 处理器实例缓存
  final Map<MediaType, MediaProcessor> _processors = {};

  /// 根据文件获取对应的媒体处理器
  ///
  /// [file] 媒体文件
  ///
  /// 返回对应的处理器，如果不支持则返回null
  MediaProcessor? getProcessorForFile(File file) {
    final extension = file.path.split('.').last.toLowerCase();

    // 判断是否为图片
    if (R2Config.allowedExtensions.contains(extension)) {
      return getProcessor(MediaType.image);
    }

    // 判断是否为视频
    if (R2Config.allowedVideoExtensions.contains(extension)) {
      return getProcessor(MediaType.video);
    }

    debugPrint('⚠️ [媒体工厂] 不支持的文件类型: $extension');
    return null;
  }

  /// 根据媒体类型获取处理器
  ///
  /// [mediaType] 媒体类型
  ///
  /// 返回对应的处理器
  MediaProcessor getProcessor(MediaType mediaType) {
    // 使用缓存的处理器实例
    if (_processors.containsKey(mediaType)) {
      return _processors[mediaType]!;
    }

    // 创建新的处理器实例
    MediaProcessor processor;
    switch (mediaType) {
      case MediaType.image:
        processor = ImageProcessor();
        break;
      case MediaType.video:
        processor = VideoProcessor();
        break;
    }

    // 缓存处理器实例
    _processors[mediaType] = processor;
    debugPrint('🏭 [媒体工厂] 创建${mediaType.name}处理器');

    return processor;
  }

  /// 获取所有支持的文件扩展名
  List<String> getAllSupportedExtensions() {
    return [...R2Config.allowedExtensions, ...R2Config.allowedVideoExtensions];
  }

  /// 检查文件是否被支持
  ///
  /// [file] 要检查的文件
  ///
  /// 返回是否支持
  bool isFileSupported(File file) {
    return getProcessorForFile(file) != null;
  }

  /// 获取文件的媒体类型
  ///
  /// [file] 文件
  ///
  /// 返回媒体类型，如果不支持则返回null
  MediaType? getMediaType(File file) {
    final extension = file.path.split('.').last.toLowerCase();

    if (R2Config.allowedExtensions.contains(extension)) {
      return MediaType.image;
    }

    if (R2Config.allowedVideoExtensions.contains(extension)) {
      return MediaType.video;
    }

    return null;
  }

  /// 根据媒体类型创建默认配置
  ///
  /// [mediaType] 媒体类型
  /// [quality] 质量设置（可选）
  ///
  /// 返回默认配置
  MediaProcessingConfig createDefaultConfig(
    MediaType mediaType, {
    int? quality,
  }) {
    switch (mediaType) {
      case MediaType.image:
        return MediaProcessingConfig.forImage(
          quality: quality ?? R2Config.imageQuality,
          maxWidth: R2Config.maxImageWidth,
          maxHeight: R2Config.maxImageHeight,
        );
      case MediaType.video:
        return MediaProcessingConfig.forVideo(
          quality: quality ?? R2Config.videoQuality,
        );
    }
  }

  /// 根据文件大小创建智能配置
  ///
  /// [file] 文件
  /// [mediaType] 媒体类型
  ///
  /// 返回智能配置
  Future<MediaProcessingConfig> createSmartConfig(
    File file,
    MediaType mediaType,
  ) async {
    // 使用智能压缩策略
    return await SmartCompressionStrategy.instance.createSmartConfig(
      file: file,
      mediaType: mediaType,
    );
  }

  /// 获取处理器统计信息
  Map<String, dynamic> getStatistics() {
    return {
      'cached_processors': _processors.length,
      'supported_image_extensions': R2Config.allowedExtensions,
      'supported_video_extensions': R2Config.allowedVideoExtensions,
      'total_supported_extensions': getAllSupportedExtensions().length,
    };
  }

  /// 清理缓存的处理器
  void clearCache() {
    _processors.clear();
    debugPrint('🧹 [媒体工厂] 清理处理器缓存');
  }

  /// 预热处理器（提前创建实例）
  void warmUp() {
    debugPrint('🔥 [媒体工厂] 预热处理器');
    getProcessor(MediaType.image);
    getProcessor(MediaType.video);
  }
}
