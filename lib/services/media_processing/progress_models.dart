/// 任务进度状态枚举
enum TaskProgressStatus {
  /// 等待开始
  pending,

  /// 初始化中
  initializing,

  /// 处理中
  processing,

  /// 上传中
  uploading,

  /// 已完成
  completed,

  /// 已失败
  failed,

  /// 已取消
  cancelled,

  /// 已暂停
  paused,
}

/// 任务类型枚举
enum TaskType {
  /// 图片处理
  imageProcessing,

  /// 视频处理
  videoProcessing,

  /// 缩略图生成
  thumbnailGeneration,

  /// 文件上传
  fileUpload,

  /// 批量处理
  batchProcessing,
}

/// 进度事件类型
enum ProgressEventType {
  /// 状态变化
  statusChanged,

  /// 进度更新
  progressUpdated,

  /// 消息更新
  messageUpdated,

  /// 错误发生
  errorOccurred,

  /// 任务完成
  taskCompleted,

  /// 任务取消
  taskCancelled,
}

/// 任务进度信息
class TaskProgress {
  /// 任务ID
  final String taskId;

  /// 任务类型
  final TaskType taskType;

  /// 当前状态
  final TaskProgressStatus status;

  /// 进度值 (0.0 - 1.0)
  final double progress;

  /// 当前消息
  final String? message;

  /// 错误信息
  final String? error;

  /// 开始时间
  final DateTime startTime;

  /// 更新时间
  final DateTime lastUpdated;

  /// 完成时间
  final DateTime? completedTime;

  /// 总步骤数
  final int? totalSteps;

  /// 当前步骤
  final int? currentStep;

  /// 文件名
  final String? fileName;

  /// 文件大小
  final int? fileSize;

  /// 已处理大小
  final int? processedSize;

  /// 额外数据
  final Map<String, dynamic> metadata;

  const TaskProgress({
    required this.taskId,
    required this.taskType,
    required this.status,
    required this.progress,
    this.message,
    this.error,
    required this.startTime,
    required this.lastUpdated,
    this.completedTime,
    this.totalSteps,
    this.currentStep,
    this.fileName,
    this.fileSize,
    this.processedSize,
    this.metadata = const {},
  });

  /// 创建新任务
  factory TaskProgress.create({
    required String taskId,
    required TaskType taskType,
    String? fileName,
    int? fileSize,
    Map<String, dynamic>? metadata,
  }) {
    final now = DateTime.now();
    return TaskProgress(
      taskId: taskId,
      taskType: taskType,
      status: TaskProgressStatus.pending,
      progress: 0.0,
      startTime: now,
      lastUpdated: now,
      fileName: fileName,
      fileSize: fileSize,
      metadata: metadata ?? {},
    );
  }

  /// 更新进度
  TaskProgress updateProgress({
    double? progress,
    String? message,
    TaskProgressStatus? status,
    String? error,
    int? currentStep,
    int? processedSize,
    Map<String, dynamic>? metadata,
  }) {
    final now = DateTime.now();
    final newStatus = status ?? this.status;

    return TaskProgress(
      taskId: taskId,
      taskType: taskType,
      status: newStatus,
      progress: progress ?? this.progress,
      message: message ?? this.message,
      error: error ?? this.error,
      startTime: startTime,
      lastUpdated: now,
      completedTime:
          newStatus == TaskProgressStatus.completed ||
                  newStatus == TaskProgressStatus.failed ||
                  newStatus == TaskProgressStatus.cancelled
              ? now
              : completedTime,
      totalSteps: totalSteps,
      currentStep: currentStep ?? this.currentStep,
      fileName: fileName,
      fileSize: fileSize,
      processedSize: processedSize ?? this.processedSize,
      metadata:
          metadata != null ? {...this.metadata, ...metadata} : this.metadata,
    );
  }

  /// 标记为完成
  TaskProgress complete({String? message}) {
    return updateProgress(
      status: TaskProgressStatus.completed,
      progress: 1.0,
      message: message ?? '处理完成',
    );
  }

  /// 标记为失败
  TaskProgress fail({required String error, String? message}) {
    return updateProgress(
      status: TaskProgressStatus.failed,
      error: error,
      message: message ?? '处理失败',
    );
  }

  /// 标记为取消
  TaskProgress cancel({String? message}) {
    return updateProgress(
      status: TaskProgressStatus.cancelled,
      message: message ?? '已取消',
    );
  }

  /// 是否已完成（成功、失败或取消）
  bool get isFinished =>
      status == TaskProgressStatus.completed ||
      status == TaskProgressStatus.failed ||
      status == TaskProgressStatus.cancelled;

  /// 是否正在运行
  bool get isRunning =>
      status == TaskProgressStatus.processing ||
      status == TaskProgressStatus.uploading ||
      status == TaskProgressStatus.initializing;

  /// 是否成功完成
  bool get isSuccessful => status == TaskProgressStatus.completed;

  /// 是否失败
  bool get isFailed => status == TaskProgressStatus.failed;

  /// 是否被取消
  bool get isCancelled => status == TaskProgressStatus.cancelled;

  /// 获取进度百分比
  int get progressPercentage => (progress * 100).round();

  /// 获取格式化的进度文本
  String get progressText => '${progressPercentage}%';

  /// 获取状态描述
  String get statusDescription {
    switch (status) {
      case TaskProgressStatus.pending:
        return '等待开始';
      case TaskProgressStatus.initializing:
        return '初始化中';
      case TaskProgressStatus.processing:
        return '处理中';
      case TaskProgressStatus.uploading:
        return '上传中';
      case TaskProgressStatus.completed:
        return '已完成';
      case TaskProgressStatus.failed:
        return '失败';
      case TaskProgressStatus.cancelled:
        return '已取消';
      case TaskProgressStatus.paused:
        return '已暂停';
    }
  }

  /// 获取任务类型描述
  String get taskTypeDescription {
    switch (taskType) {
      case TaskType.imageProcessing:
        return '图片处理';
      case TaskType.videoProcessing:
        return '视频处理';
      case TaskType.thumbnailGeneration:
        return '缩略图生成';
      case TaskType.fileUpload:
        return '文件上传';
      case TaskType.batchProcessing:
        return '批量处理';
    }
  }

  /// 获取耗时
  Duration get duration => lastUpdated.difference(startTime);

  /// 获取格式化的耗时
  String get formattedDuration {
    final duration = this.duration;
    if (duration.inHours > 0) {
      return '${duration.inHours}:${(duration.inMinutes % 60).toString().padLeft(2, '0')}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}';
    } else {
      return '${duration.inSeconds}秒';
    }
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'taskType': taskType.name,
      'status': status.name,
      'progress': progress,
      'message': message,
      'error': error,
      'startTime': startTime.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
      'completedTime': completedTime?.toIso8601String(),
      'totalSteps': totalSteps,
      'currentStep': currentStep,
      'fileName': fileName,
      'fileSize': fileSize,
      'processedSize': processedSize,
      'metadata': metadata,
    };
  }

  /// 从JSON创建
  factory TaskProgress.fromJson(Map<String, dynamic> json) {
    return TaskProgress(
      taskId: json['taskId'],
      taskType: TaskType.values.firstWhere(
        (e) => e.name == json['taskType'],
        orElse: () => TaskType.imageProcessing,
      ),
      status: TaskProgressStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => TaskProgressStatus.pending,
      ),
      progress: json['progress']?.toDouble() ?? 0.0,
      message: json['message'],
      error: json['error'],
      startTime: DateTime.parse(json['startTime']),
      lastUpdated: DateTime.parse(json['lastUpdated']),
      completedTime:
          json['completedTime'] != null
              ? DateTime.parse(json['completedTime'])
              : null,
      totalSteps: json['totalSteps'],
      currentStep: json['currentStep'],
      fileName: json['fileName'],
      fileSize: json['fileSize'],
      processedSize: json['processedSize'],
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  @override
  String toString() {
    return 'TaskProgress(taskId: $taskId, status: $status, progress: ${progressText})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskProgress &&
        other.taskId == taskId &&
        other.status == status &&
        other.progress == progress &&
        other.lastUpdated == lastUpdated;
  }

  @override
  int get hashCode {
    return Object.hash(taskId, status, progress, lastUpdated);
  }
}

/// 进度事件
class ProgressEvent {
  /// 事件类型
  final ProgressEventType type;

  /// 任务进度
  final TaskProgress taskProgress;

  /// 事件时间
  final DateTime timestamp;

  /// 事件数据
  final Map<String, dynamic> data;

  const ProgressEvent({
    required this.type,
    required this.taskProgress,
    required this.timestamp,
    this.data = const {},
  });

  /// 创建状态变化事件
  factory ProgressEvent.statusChanged(TaskProgress taskProgress) {
    return ProgressEvent(
      type: ProgressEventType.statusChanged,
      taskProgress: taskProgress,
      timestamp: DateTime.now(),
    );
  }

  /// 创建进度更新事件
  factory ProgressEvent.progressUpdated(TaskProgress taskProgress) {
    return ProgressEvent(
      type: ProgressEventType.progressUpdated,
      taskProgress: taskProgress,
      timestamp: DateTime.now(),
    );
  }

  /// 创建错误事件
  factory ProgressEvent.error(TaskProgress taskProgress, String error) {
    return ProgressEvent(
      type: ProgressEventType.errorOccurred,
      taskProgress: taskProgress,
      timestamp: DateTime.now(),
      data: {'error': error},
    );
  }

  @override
  String toString() {
    return 'ProgressEvent(type: $type, taskId: ${taskProgress.taskId})';
  }
}
