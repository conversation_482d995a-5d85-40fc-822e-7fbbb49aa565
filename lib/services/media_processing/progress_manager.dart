import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'progress_models.dart';
import 'progress_listener.dart';

/// 进度管理器
/// 
/// 统一管理所有媒体处理任务的进度和状态
class ProgressManager {
  static ProgressManager? _instance;
  static ProgressManager get instance => _instance ??= ProgressManager._();
  
  ProgressManager._();

  // 任务进度存储
  final Map<String, TaskProgress> _tasks = {};
  
  // 监听器管理器
  final ProgressListenerManager _listenerManager = ProgressListenerManager();
  
  // 批量任务管理
  final Map<String, Set<String>> _batchTasks = {};
  
  // 任务取消控制器
  final Map<String, Completer<void>> _cancellationTokens = {};

  /// 创建新任务
  /// 
  /// [taskId] 任务ID
  /// [taskType] 任务类型
  /// [fileName] 文件名（可选）
  /// [fileSize] 文件大小（可选）
  /// [metadata] 额外数据（可选）
  /// 
  /// 返回任务进度
  TaskProgress createTask({
    required String taskId,
    required TaskType taskType,
    String? fileName,
    int? fileSize,
    Map<String, dynamic>? metadata,
  }) {
    if (_tasks.containsKey(taskId)) {
      debugPrint('⚠️ [进度管理] 任务 $taskId 已存在，将覆盖');
    }

    final task = TaskProgress.create(
      taskId: taskId,
      taskType: taskType,
      fileName: fileName,
      fileSize: fileSize,
      metadata: metadata,
    );

    _tasks[taskId] = task;
    
    // 创建取消控制器
    _cancellationTokens[taskId] = Completer<void>();

    debugPrint('📝 [进度管理] 创建任务: $taskId (${task.taskTypeDescription})');
    
    // 通知监听器
    _notifyEvent(ProgressEvent.statusChanged(task));
    
    return task;
  }

  /// 更新任务进度
  /// 
  /// [taskId] 任务ID
  /// [progress] 进度值 (0.0 - 1.0)
  /// [message] 消息（可选）
  /// [status] 状态（可选）
  /// [currentStep] 当前步骤（可选）
  /// [processedSize] 已处理大小（可选）
  /// [metadata] 额外数据（可选）
  /// 
  /// 返回更新后的任务进度
  TaskProgress? updateTaskProgress({
    required String taskId,
    double? progress,
    String? message,
    TaskProgressStatus? status,
    int? currentStep,
    int? processedSize,
    Map<String, dynamic>? metadata,
  }) {
    final currentTask = _tasks[taskId];
    if (currentTask == null) {
      debugPrint('⚠️ [进度管理] 任务 $taskId 不存在');
      return null;
    }

    // 检查任务是否已完成
    if (currentTask.isFinished) {
      debugPrint('⚠️ [进度管理] 任务 $taskId 已完成，无法更新');
      return currentTask;
    }

    final updatedTask = currentTask.updateProgress(
      progress: progress,
      message: message,
      status: status,
      currentStep: currentStep,
      processedSize: processedSize,
      metadata: metadata,
    );

    _tasks[taskId] = updatedTask;

    debugPrint('📊 [进度管理] 更新任务 $taskId: ${updatedTask.progressText} - ${updatedTask.message ?? updatedTask.statusDescription}');

    // 通知监听器
    if (status != null && status != currentTask.status) {
      _notifyEvent(ProgressEvent.statusChanged(updatedTask));
    } else {
      _notifyEvent(ProgressEvent.progressUpdated(updatedTask));
    }

    // 如果任务完成，清理资源
    if (updatedTask.isFinished) {
      _cleanupTask(taskId);
    }

    return updatedTask;
  }

  /// 完成任务
  /// 
  /// [taskId] 任务ID
  /// [message] 完成消息（可选）
  /// 
  /// 返回完成的任务进度
  TaskProgress? completeTask(String taskId, {String? message}) {
    final task = _tasks[taskId];
    if (task == null) {
      debugPrint('⚠️ [进度管理] 任务 $taskId 不存在');
      return null;
    }

    final completedTask = task.complete(message: message);
    _tasks[taskId] = completedTask;

    debugPrint('✅ [进度管理] 任务 $taskId 已完成: ${message ?? '处理完成'}');

    // 通知监听器
    _notifyEvent(ProgressEvent(
      type: ProgressEventType.taskCompleted,
      taskProgress: completedTask,
      timestamp: DateTime.now(),
    ));

    // 清理资源
    _cleanupTask(taskId);

    return completedTask;
  }

  /// 任务失败
  /// 
  /// [taskId] 任务ID
  /// [error] 错误信息
  /// [message] 失败消息（可选）
  /// 
  /// 返回失败的任务进度
  TaskProgress? failTask(String taskId, {required String error, String? message}) {
    final task = _tasks[taskId];
    if (task == null) {
      debugPrint('⚠️ [进度管理] 任务 $taskId 不存在');
      return null;
    }

    final failedTask = task.fail(error: error, message: message);
    _tasks[taskId] = failedTask;

    debugPrint('❌ [进度管理] 任务 $taskId 失败: $error');

    // 通知监听器
    _notifyEvent(ProgressEvent.error(failedTask, error));

    // 清理资源
    _cleanupTask(taskId);

    return failedTask;
  }

  /// 取消任务
  /// 
  /// [taskId] 任务ID
  /// [message] 取消消息（可选）
  /// 
  /// 返回取消的任务进度
  TaskProgress? cancelTask(String taskId, {String? message}) {
    final task = _tasks[taskId];
    if (task == null) {
      debugPrint('⚠️ [进度管理] 任务 $taskId 不存在');
      return null;
    }

    final cancelledTask = task.cancel(message: message);
    _tasks[taskId] = cancelledTask;

    debugPrint('⚠️ [进度管理] 任务 $taskId 已取消: ${message ?? '已取消'}');

    // 触发取消信号
    final cancellationToken = _cancellationTokens[taskId];
    if (cancellationToken != null && !cancellationToken.isCompleted) {
      cancellationToken.complete();
    }

    // 通知监听器
    _notifyEvent(ProgressEvent(
      type: ProgressEventType.taskCancelled,
      taskProgress: cancelledTask,
      timestamp: DateTime.now(),
    ));

    // 清理资源
    _cleanupTask(taskId);

    return cancelledTask;
  }

  /// 获取任务进度
  /// 
  /// [taskId] 任务ID
  /// 
  /// 返回任务进度
  TaskProgress? getTaskProgress(String taskId) {
    return _tasks[taskId];
  }

  /// 获取所有任务
  List<TaskProgress> getAllTasks() {
    return _tasks.values.toList();
  }

  /// 获取运行中的任务
  List<TaskProgress> getRunningTasks() {
    return _tasks.values.where((task) => task.isRunning).toList();
  }

  /// 获取已完成的任务
  List<TaskProgress> getCompletedTasks() {
    return _tasks.values.where((task) => task.isSuccessful).toList();
  }

  /// 获取失败的任务
  List<TaskProgress> getFailedTasks() {
    return _tasks.values.where((task) => task.isFailed).toList();
  }

  /// 检查任务是否被取消
  /// 
  /// [taskId] 任务ID
  /// 
  /// 返回取消Future
  Future<void> getCancellationFuture(String taskId) {
    final cancellationToken = _cancellationTokens[taskId];
    if (cancellationToken == null) {
      return Future.value(); // 任务不存在，立即返回
    }
    return cancellationToken.future;
  }

  /// 检查任务是否应该取消
  /// 
  /// [taskId] 任务ID
  /// 
  /// 返回是否应该取消
  bool shouldCancel(String taskId) {
    final task = _tasks[taskId];
    return task?.isCancelled ?? false;
  }

  /// 添加全局监听器
  void addGlobalListener(ProgressListener listener) {
    _listenerManager.addGlobalListener(listener);
  }

  /// 移除全局监听器
  void removeGlobalListener(String listenerId) {
    _listenerManager.removeGlobalListener(listenerId);
  }

  /// 添加任务监听器
  void addTaskListener(String taskId, ProgressListener listener) {
    _listenerManager.addTaskListener(taskId, listener);
  }

  /// 移除任务监听器
  void removeTaskListener(String taskId, String listenerId) {
    _listenerManager.removeTaskListener(taskId, listenerId);
  }

  /// 创建批量任务
  /// 
  /// [batchId] 批量任务ID
  /// [taskIds] 子任务ID列表
  /// 
  /// 返回批量任务进度
  TaskProgress createBatchTask({
    required String batchId,
    required List<String> taskIds,
    Map<String, dynamic>? metadata,
  }) {
    // 创建批量任务
    final batchTask = createTask(
      taskId: batchId,
      taskType: TaskType.batchProcessing,
      metadata: {
        'task_count': taskIds.length,
        'sub_tasks': taskIds,
        ...?metadata,
      },
    );

    // 记录批量任务关系
    _batchTasks[batchId] = taskIds.toSet();

    debugPrint('📦 [进度管理] 创建批量任务 $batchId，包含 ${taskIds.length} 个子任务');

    return batchTask;
  }

  /// 获取批量任务进度
  /// 
  /// [batchId] 批量任务ID
  /// 
  /// 返回聚合的进度信息
  TaskProgress? getBatchProgress(String batchId) {
    final batchTask = _tasks[batchId];
    final subTaskIds = _batchTasks[batchId];
    
    if (batchTask == null || subTaskIds == null) {
      return null;
    }

    // 计算子任务的聚合进度
    final subTasks = subTaskIds
        .map((id) => _tasks[id])
        .where((task) => task != null)
        .cast<TaskProgress>()
        .toList();

    if (subTasks.isEmpty) {
      return batchTask;
    }

    // 计算平均进度
    final totalProgress = subTasks.map((task) => task.progress).reduce((a, b) => a + b);
    final averageProgress = totalProgress / subTasks.length;

    // 计算状态
    final completedCount = subTasks.where((task) => task.isSuccessful).length;
    final failedCount = subTasks.where((task) => task.isFailed).length;
    final cancelledCount = subTasks.where((task) => task.isCancelled).length;
    final runningCount = subTasks.where((task) => task.isRunning).length;

    TaskProgressStatus batchStatus;
    String? message;

    if (failedCount > 0) {
      batchStatus = TaskProgressStatus.failed;
      message = '${failedCount} 个任务失败';
    } else if (cancelledCount > 0) {
      batchStatus = TaskProgressStatus.cancelled;
      message = '${cancelledCount} 个任务被取消';
    } else if (completedCount == subTasks.length) {
      batchStatus = TaskProgressStatus.completed;
      message = '所有任务已完成';
    } else if (runningCount > 0) {
      batchStatus = TaskProgressStatus.processing;
      message = '${runningCount} 个任务正在处理';
    } else {
      batchStatus = TaskProgressStatus.pending;
      message = '等待开始';
    }

    return batchTask.updateProgress(
      progress: averageProgress,
      status: batchStatus,
      message: message,
      metadata: {
        ...batchTask.metadata,
        'completed_count': completedCount,
        'failed_count': failedCount,
        'cancelled_count': cancelledCount,
        'running_count': runningCount,
      },
    );
  }

  /// 获取统计信息
  Map<String, dynamic> getStatistics() {
    final allTasks = _tasks.values.toList();
    
    return {
      'total_tasks': allTasks.length,
      'running_tasks': allTasks.where((task) => task.isRunning).length,
      'completed_tasks': allTasks.where((task) => task.isSuccessful).length,
      'failed_tasks': allTasks.where((task) => task.isFailed).length,
      'cancelled_tasks': allTasks.where((task) => task.isCancelled).length,
      'batch_tasks': _batchTasks.length,
      'listener_stats': _listenerManager.getStatistics(),
    };
  }

  /// 清理已完成的任务
  void cleanupFinishedTasks() {
    final finishedTaskIds = _tasks.entries
        .where((entry) => entry.value.isFinished)
        .map((entry) => entry.key)
        .toList();

    for (final taskId in finishedTaskIds) {
      _tasks.remove(taskId);
      _listenerManager.removeAllTaskListeners(taskId);
      _cancellationTokens.remove(taskId);
    }

    debugPrint('🧹 [进度管理] 清理了 ${finishedTaskIds.length} 个已完成的任务');
  }

  /// 清理所有任务
  void clearAllTasks() {
    final taskCount = _tasks.length;
    
    _tasks.clear();
    _batchTasks.clear();
    _cancellationTokens.clear();
    _listenerManager.dispose();

    debugPrint('🧹 [进度管理] 清理了所有任务 ($taskCount 个)');
  }

  /// 通知事件
  void _notifyEvent(ProgressEvent event) {
    _listenerManager.notifyProgressEvent(event);
  }

  /// 清理单个任务资源
  void _cleanupTask(String taskId) {
    // 延迟清理，给监听器一些时间处理事件
    Timer(const Duration(seconds: 1), () {
      _listenerManager.removeAllTaskListeners(taskId);
      _cancellationTokens.remove(taskId);
    });
  }

  /// 生成唯一任务ID
  static String generateTaskId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(9999).toString().padLeft(4, '0');
    return 'task_${timestamp}_$random';
  }
}
