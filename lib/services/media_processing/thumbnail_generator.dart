import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:video_compress/video_compress.dart';
import 'media_processor.dart';
import '../webp_image_service.dart';
import '../../config/r2_config.dart';

/// 统一缩略图生成器
///
/// 为图片和视频提供统一的缩略图生成逻辑
class ThumbnailGenerator {
  static ThumbnailGenerator? _instance;
  static ThumbnailGenerator get instance =>
      _instance ??= ThumbnailGenerator._();

  ThumbnailGenerator._();

  /// 生成缩略图
  ///
  /// [file] 原始文件
  /// [mediaType] 媒体类型
  /// [config] 处理配置
  ///
  /// 返回缩略图文件
  Future<File?> generateThumbnail({
    required File file,
    required MediaType mediaType,
    required MediaProcessingConfig config,
  }) async {
    try {
      debugPrint('🖼️ [缩略图生成器] 开始生成缩略图: ${file.path}');

      switch (mediaType) {
        case MediaType.image:
          return await _generateImageThumbnail(file, config);
        case MediaType.video:
          return await _generateVideoThumbnail(file, config);
      }
    } catch (e) {
      debugPrint('❌ [缩略图生成器] 生成缩略图异常: $e');
      return null;
    }
  }

  /// 批量生成缩略图
  ///
  /// [files] 文件列表
  /// [mediaTypes] 对应的媒体类型列表
  /// [config] 处理配置
  ///
  /// 返回缩略图文件列表
  Future<List<File?>> generateThumbnails({
    required List<File> files,
    required List<MediaType> mediaTypes,
    required MediaProcessingConfig config,
  }) async {
    if (files.length != mediaTypes.length) {
      throw ArgumentError('文件列表和媒体类型列表长度不匹配');
    }

    final results = <File?>[];

    for (int i = 0; i < files.length; i++) {
      final thumbnail = await generateThumbnail(
        file: files[i],
        mediaType: mediaTypes[i],
        config: config,
      );
      results.add(thumbnail);
    }

    return results;
  }

  /// 生成图片缩略图
  Future<File?> _generateImageThumbnail(
    File file,
    MediaProcessingConfig config,
  ) async {
    try {
      debugPrint('🖼️ [缩略图生成器] 生成图片缩略图');

      final imageBytes = await file.readAsBytes();
      final thumbnailResult = await WebPImageService.processImage(
        imageBytes: imageBytes,
        quality: config.thumbnailQuality,
        maxWidth: config.thumbnailMaxWidth,
        maxHeight: config.thumbnailMaxHeight,
        forThumbnail: true,
      );

      // 创建临时缩略图文件
      final tempDir = Directory.systemTemp;
      final thumbnailFile = File(
        '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_thumb.${thumbnailResult.extension}',
      );

      await thumbnailFile.writeAsBytes(thumbnailResult.imageBytes);

      debugPrint('✅ [缩略图生成器] 图片缩略图生成成功: ${thumbnailFile.path}');
      return thumbnailFile;
    } catch (e) {
      debugPrint('❌ [缩略图生成器] 生成图片缩略图失败: $e');
      return null;
    }
  }

  /// 生成视频缩略图
  Future<File?> _generateVideoThumbnail(
    File file,
    MediaProcessingConfig config,
  ) async {
    try {
      debugPrint('🎥 [缩略图生成器] 生成视频缩略图');

      // 使用VideoCompress生成缩略图
      final thumbnailFile = await VideoCompress.getFileThumbnail(
        file.path,
        quality: config.thumbnailQuality,
        position: _getOptimalThumbnailPosition(file),
      );

      if (thumbnailFile != null) {
        // 如果需要，可以进一步压缩缩略图
        if (config.thumbnailMaxWidth < 500 || config.thumbnailMaxHeight < 500) {
          final compressedThumbnail = await _compressVideoThumbnail(
            thumbnailFile,
            config,
          );

          // 删除原始缩略图
          if (await thumbnailFile.exists()) {
            await thumbnailFile.delete();
          }

          return compressedThumbnail;
        }

        debugPrint('✅ [缩略图生成器] 视频缩略图生成成功: ${thumbnailFile.path}');
        return thumbnailFile;
      } else {
        debugPrint('❌ [缩略图生成器] 视频缩略图生成失败');
        return null;
      }
    } catch (e) {
      debugPrint('❌ [缩略图生成器] 生成视频缩略图失败: $e');
      return null;
    }
  }

  /// 压缩视频缩略图
  Future<File?> _compressVideoThumbnail(
    File thumbnailFile,
    MediaProcessingConfig config,
  ) async {
    try {
      final imageBytes = await thumbnailFile.readAsBytes();
      final compressedResult = await WebPImageService.processImage(
        imageBytes: imageBytes,
        quality: config.thumbnailQuality,
        maxWidth: config.thumbnailMaxWidth,
        maxHeight: config.thumbnailMaxHeight,
        forThumbnail: true,
      );

      // 创建压缩后的缩略图文件
      final tempDir = Directory.systemTemp;
      final compressedFile = File(
        '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_compressed_thumb.${compressedResult.extension}',
      );

      await compressedFile.writeAsBytes(compressedResult.imageBytes);

      debugPrint('✅ [缩略图生成器] 视频缩略图压缩完成');
      return compressedFile;
    } catch (e) {
      debugPrint('❌ [缩略图生成器] 压缩视频缩略图失败: $e');
      return null;
    }
  }

  /// 获取最佳缩略图提取位置
  ///
  /// [file] 视频文件
  ///
  /// 返回提取位置（毫秒）
  int _getOptimalThumbnailPosition(File file) {
    // 默认从1秒处提取
    // 未来可以根据视频时长智能调整
    return 1000;
  }

  /// 验证缩略图质量
  ///
  /// [thumbnailFile] 缩略图文件
  /// [config] 配置
  ///
  /// 返回是否符合质量要求
  Future<bool> validateThumbnailQuality(
    File thumbnailFile,
    MediaProcessingConfig config,
  ) async {
    try {
      final fileSize = await thumbnailFile.length();

      // 检查文件大小（缩略图不应该太大）
      const maxThumbnailSize = 500 * 1024; // 500KB
      if (fileSize > maxThumbnailSize) {
        debugPrint('⚠️ [缩略图生成器] 缩略图文件过大: ${fileSize} bytes');
        return false;
      }

      // 检查文件是否可以正常读取
      final imageBytes = await thumbnailFile.readAsBytes();
      if (imageBytes.isEmpty) {
        debugPrint('⚠️ [缩略图生成器] 缩略图文件为空');
        return false;
      }

      debugPrint('✅ [缩略图生成器] 缩略图质量验证通过');
      return true;
    } catch (e) {
      debugPrint('❌ [缩略图生成器] 缩略图质量验证失败: $e');
      return false;
    }
  }

  /// 清理缩略图临时文件
  ///
  /// [thumbnailFiles] 缩略图文件列表
  Future<void> cleanupThumbnails(List<File> thumbnailFiles) async {
    for (final file in thumbnailFiles) {
      try {
        if (await file.exists()) {
          await file.delete();
          debugPrint('🧹 [缩略图生成器] 清理缩略图: ${file.path}');
        }
      } catch (e) {
        debugPrint('⚠️ [缩略图生成器] 清理缩略图失败: ${file.path}, $e');
      }
    }
  }

  /// 获取缩略图生成统计信息
  Map<String, dynamic> getStatistics() {
    return {
      'default_thumbnail_quality': R2Config.thumbnailQuality,
      'default_thumbnail_max_width': R2Config.thumbnailMaxWidth,
      'default_thumbnail_max_height': R2Config.thumbnailMaxHeight,
      'supported_image_formats': R2Config.allowedExtensions,
      'supported_video_formats': R2Config.allowedVideoExtensions,
    };
  }
}
