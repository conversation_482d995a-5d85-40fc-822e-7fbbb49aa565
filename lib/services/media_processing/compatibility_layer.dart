import 'dart:io';
import 'package:flutter/foundation.dart';
import 'unified_media_service.dart';
import 'media_processor.dart';
import '../unified_image_service.dart';
import '../../models/upload_result.dart';

/// 向后兼容性层
///
/// 提供与现有代码的兼容接口，确保升级过程中的平滑过渡
class CompatibilityLayer {
  static CompatibilityLayer? _instance;
  static CompatibilityLayer get instance =>
      _instance ??= CompatibilityLayer._();

  CompatibilityLayer._();

  final UnifiedMediaService _mediaService = UnifiedMediaService.instance;
  final UnifiedImageService _legacyService = UnifiedImageService();

  /// 兼容旧的图片上传接口
  ///
  /// [imageFile] 图片文件
  /// [userId] 用户ID
  /// [spotId] 钓点ID（可选）
  ///
  /// 返回上传结果
  Future<ImageUploadResult?> uploadImageLegacy({
    required File imageFile,
    required String userId,
    String? spotId,
  }) async {
    try {
      debugPrint('🔄 [兼容层] 使用兼容接口上传图片: ${imageFile.path}');

      // 使用新的统一媒体服务
      final result = await _mediaService.processMedia(
        file: imageFile,
        userId: userId,
        spotId: spotId,
      );

      if (result != null) {
        // 转换为旧的返回格式
        return ImageUploadResult(
          originalUrl: result.originalUrl,
          thumbnailUrl: result.thumbnailUrl ?? '',
          fileName: result.fileName,
          fileSize: result.fileSize,
          width: result.width ?? 0,
          height: result.height ?? 0,
        );
      }

      return null;
    } catch (e) {
      debugPrint('❌ [兼容层] 兼容接口上传失败: $e');

      // 如果新服务失败，回退到旧服务
      debugPrint('🔄 [兼容层] 回退到旧服务');
      return await _legacyService.uploadAvatar(
        imageFile: imageFile,
        userId: userId,
      );
    }
  }

  /// 兼容旧的视频上传接口
  ///
  /// [videoFile] 视频文件
  /// [userId] 用户ID
  ///
  /// 返回上传结果
  Future<ImageUploadResult?> uploadVideoLegacy({
    required File videoFile,
    required String userId,
  }) async {
    try {
      debugPrint('🔄 [兼容层] 使用兼容接口上传视频: ${videoFile.path}');

      // 使用新的统一媒体服务
      final result = await _mediaService.processMedia(
        file: videoFile,
        userId: userId,
      );

      if (result != null) {
        // 转换为旧的返回格式
        return ImageUploadResult(
          originalUrl: result.originalUrl,
          thumbnailUrl: result.thumbnailUrl ?? '',
          fileName: result.fileName,
          fileSize: result.fileSize,
          width: result.width ?? 0,
          height: result.height ?? 0,
        );
      }

      return null;
    } catch (e) {
      debugPrint('❌ [兼容层] 兼容接口上传失败: $e');

      // 如果新服务失败，回退到旧服务
      debugPrint('🔄 [兼容层] 回退到旧服务');
      return await _legacyService.uploadVideoIndependent(
        videoFile: videoFile,
        userId: userId,
      );
    }
  }

  /// 兼容旧的头像上传接口
  ///
  /// [imageFile] 图片文件
  /// [userId] 用户ID
  /// [customFileName] 自定义文件名（可选）
  ///
  /// 返回上传结果
  Future<ImageUploadResult?> uploadAvatarLegacy({
    required File imageFile,
    required String userId,
    String? customFileName,
  }) async {
    try {
      debugPrint('🔄 [兼容层] 使用兼容接口上传头像: ${imageFile.path}');

      // 使用新的统一媒体服务
      final result = await _mediaService.processMedia(
        file: imageFile,
        userId: userId,
      );

      if (result != null) {
        // 转换为旧的返回格式
        return ImageUploadResult(
          originalUrl: result.originalUrl,
          thumbnailUrl: result.thumbnailUrl ?? '',
          fileName: customFileName ?? result.fileName,
          fileSize: result.fileSize,
          width: result.width ?? 0,
          height: result.height ?? 0,
        );
      }

      return null;
    } catch (e) {
      debugPrint('❌ [兼容层] 兼容接口上传失败: $e');

      // 如果新服务失败，回退到旧服务
      debugPrint('🔄 [兼容层] 回退到旧服务');
      return await _legacyService.uploadAvatar(
        imageFile: imageFile,
        userId: userId,
        customFileName: customFileName,
      );
    }
  }

  /// 验证新旧服务的一致性
  ///
  /// [testFile] 测试文件
  /// [userId] 用户ID
  ///
  /// 返回验证结果
  Future<bool> validateConsistency({
    required File testFile,
    required String userId,
  }) async {
    try {
      debugPrint('🔍 [兼容层] 开始一致性验证: ${testFile.path}');

      // 使用新服务处理
      final newResult = await _mediaService.processMedia(
        file: testFile,
        userId: userId,
      );

      // 使用旧服务处理（仅图片）
      ImageUploadResult? oldResult;
      if (_mediaService.isFileSupported(testFile)) {
        final mediaType = testFile.path.split('.').last.toLowerCase();
        if (['jpg', 'jpeg', 'png', 'webp'].contains(mediaType)) {
          oldResult = await _legacyService.uploadAvatar(
            imageFile: testFile,
            userId: userId,
          );
        }
      }

      // 比较结果
      if (newResult != null && oldResult != null) {
        final consistent = _compareResults(newResult, oldResult);
        debugPrint('🔍 [兼容层] 一致性验证结果: ${consistent ? '通过' : '失败'}');
        return consistent;
      }

      debugPrint('🔍 [兼容层] 无法进行完整比较');
      return newResult != null; // 至少新服务应该工作
    } catch (e) {
      debugPrint('❌ [兼容层] 一致性验证异常: $e');
      return false;
    }
  }

  /// 获取兼容性统计信息
  Map<String, dynamic> getCompatibilityStats() {
    return {
      'new_service_available': true,
      'legacy_service_available': true,
      'supported_formats': _mediaService.getSupportedExtensions(),
      'fallback_enabled': true,
      'validation_available': true,
    };
  }

  /// 比较新旧服务的结果
  bool _compareResults(
    MediaProcessingResult newResult,
    ImageUploadResult oldResult,
  ) {
    // 比较基本属性
    final urlsMatch =
        newResult.originalUrl.isNotEmpty && oldResult.originalUrl.isNotEmpty;
    final filesizeReasonable =
        (newResult.fileSize - oldResult.fileSize).abs() <
        oldResult.fileSize * 0.2; // 20%误差范围

    debugPrint('🔍 [兼容层] URL匹配: $urlsMatch');
    debugPrint(
      '🔍 [兼容层] 文件大小合理: $filesizeReasonable (新: ${newResult.fileSize}, 旧: ${oldResult.fileSize})',
    );

    return urlsMatch && filesizeReasonable;
  }

  /// 执行兼容性测试套件
  ///
  /// [testFiles] 测试文件列表
  /// [userId] 用户ID
  ///
  /// 返回测试结果
  Future<Map<String, dynamic>> runCompatibilityTests({
    required List<File> testFiles,
    required String userId,
  }) async {
    final results = <String, dynamic>{
      'total_tests': testFiles.length,
      'passed': 0,
      'failed': 0,
      'errors': <String>[],
      'details': <Map<String, dynamic>>[],
    };

    for (int i = 0; i < testFiles.length; i++) {
      final file = testFiles[i];

      try {
        debugPrint('🧪 [兼容层] 测试文件 ${i + 1}/${testFiles.length}: ${file.path}');

        final testResult = await validateConsistency(
          testFile: file,
          userId: userId,
        );

        final detail = {
          'file': file.path,
          'passed': testResult,
          'timestamp': DateTime.now().toIso8601String(),
        };

        results['details'].add(detail);

        if (testResult) {
          results['passed']++;
        } else {
          results['failed']++;
        }
      } catch (e) {
        results['failed']++;
        results['errors'].add('${file.path}: $e');

        debugPrint('❌ [兼容层] 测试失败: ${file.path}, $e');
      }
    }

    final passRate =
        results['total_tests'] > 0
            ? (results['passed'] / results['total_tests'] * 100)
                .toStringAsFixed(1)
            : '0.0';

    results['pass_rate'] = '$passRate%';

    debugPrint(
      '📊 [兼容层] 测试完成: ${results['passed']}/${results['total_tests']} 通过 ($passRate%)',
    );

    return results;
  }

  /// 清理兼容层资源
  void dispose() {
    debugPrint('🧹 [兼容层] 清理兼容层资源');
    // 这里可以添加清理逻辑
  }
}
