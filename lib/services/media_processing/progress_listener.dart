import 'dart:async';
import 'package:flutter/foundation.dart';
import 'progress_models.dart';

/// 进度监听器接口
abstract class ProgressListener {
  /// 监听器ID
  String get listenerId;

  /// 处理进度事件
  void onProgressEvent(ProgressEvent event);

  /// 处理错误
  void onError(String taskId, String error) {
    debugPrint('❌ [进度监听器] 任务 $taskId 发生错误: $error');
  }

  /// 任务完成回调
  void onTaskCompleted(String taskId, TaskProgress progress) {
    debugPrint('✅ [进度监听器] 任务 $taskId 已完成');
  }

  /// 任务取消回调
  void onTaskCancelled(String taskId, TaskProgress progress) {
    debugPrint('⚠️ [进度监听器] 任务 $taskId 已取消');
  }
}

/// 函数式进度监听器
class FunctionProgressListener extends ProgressListener {
  @override
  final String listenerId;

  final void Function(ProgressEvent event) _onProgressEvent;
  final void Function(String taskId, String error)? _onError;
  final void Function(String taskId, TaskProgress progress)? _onTaskCompleted;
  final void Function(String taskId, TaskProgress progress)? _onTaskCancelled;

  FunctionProgressListener({
    required this.listenerId,
    required void Function(ProgressEvent event) onProgressEvent,
    void Function(String taskId, String error)? onError,
    void Function(String taskId, TaskProgress progress)? onTaskCompleted,
    void Function(String taskId, TaskProgress progress)? onTaskCancelled,
  }) : _onProgressEvent = onProgressEvent,
       _onError = onError,
       _onTaskCompleted = onTaskCompleted,
       _onTaskCancelled = onTaskCancelled;

  @override
  void onProgressEvent(ProgressEvent event) {
    _onProgressEvent(event);
  }

  @override
  void onError(String taskId, String error) {
    if (_onError != null) {
      _onError(taskId, error);
    } else {
      super.onError(taskId, error);
    }
  }

  @override
  void onTaskCompleted(String taskId, TaskProgress progress) {
    if (_onTaskCompleted != null) {
      _onTaskCompleted(taskId, progress);
    } else {
      super.onTaskCompleted(taskId, progress);
    }
  }

  @override
  void onTaskCancelled(String taskId, TaskProgress progress) {
    if (_onTaskCancelled != null) {
      _onTaskCancelled(taskId, progress);
    } else {
      super.onTaskCancelled(taskId, progress);
    }
  }
}

/// Stream进度监听器
class StreamProgressListener extends ProgressListener {
  @override
  final String listenerId;

  final StreamController<ProgressEvent> _eventController;
  final StreamController<String> _errorController;
  final StreamController<TaskProgress> _completedController;
  final StreamController<TaskProgress> _cancelledController;

  StreamProgressListener({required this.listenerId})
    : _eventController = StreamController<ProgressEvent>.broadcast(),
      _errorController = StreamController<String>.broadcast(),
      _completedController = StreamController<TaskProgress>.broadcast(),
      _cancelledController = StreamController<TaskProgress>.broadcast();

  /// 进度事件流
  Stream<ProgressEvent> get eventStream => _eventController.stream;

  /// 错误流
  Stream<String> get errorStream => _errorController.stream;

  /// 完成事件流
  Stream<TaskProgress> get completedStream => _completedController.stream;

  /// 取消事件流
  Stream<TaskProgress> get cancelledStream => _cancelledController.stream;

  @override
  void onProgressEvent(ProgressEvent event) {
    if (!_eventController.isClosed) {
      _eventController.add(event);
    }
  }

  @override
  void onError(String taskId, String error) {
    super.onError(taskId, error);
    if (!_errorController.isClosed) {
      _errorController.add(error);
    }
  }

  @override
  void onTaskCompleted(String taskId, TaskProgress progress) {
    super.onTaskCompleted(taskId, progress);
    if (!_completedController.isClosed) {
      _completedController.add(progress);
    }
  }

  @override
  void onTaskCancelled(String taskId, TaskProgress progress) {
    super.onTaskCancelled(taskId, progress);
    if (!_cancelledController.isClosed) {
      _cancelledController.add(progress);
    }
  }

  /// 关闭所有流
  void dispose() {
    _eventController.close();
    _errorController.close();
    _completedController.close();
    _cancelledController.close();
  }
}

/// 进度监听器管理器
class ProgressListenerManager {
  final Map<String, Set<ProgressListener>> _taskListeners = {};
  final Map<String, ProgressListener> _globalListeners = {};

  /// 添加全局监听器（监听所有任务）
  void addGlobalListener(ProgressListener listener) {
    _globalListeners[listener.listenerId] = listener;
    debugPrint('📡 [监听器管理] 添加全局监听器: ${listener.listenerId}');
  }

  /// 移除全局监听器
  void removeGlobalListener(String listenerId) {
    final removed = _globalListeners.remove(listenerId);
    if (removed != null) {
      debugPrint('📡 [监听器管理] 移除全局监听器: $listenerId');
      if (removed is StreamProgressListener) {
        removed.dispose();
      }
    }
  }

  /// 添加任务监听器（监听特定任务）
  void addTaskListener(String taskId, ProgressListener listener) {
    _taskListeners.putIfAbsent(taskId, () => <ProgressListener>{});
    _taskListeners[taskId]!.add(listener);
    debugPrint('📡 [监听器管理] 为任务 $taskId 添加监听器: ${listener.listenerId}');
  }

  /// 移除任务监听器
  void removeTaskListener(String taskId, String listenerId) {
    final listeners = _taskListeners[taskId];
    if (listeners != null) {
      final removed =
          listeners.where((l) => l.listenerId == listenerId).firstOrNull;
      if (removed != null) {
        listeners.remove(removed);
        debugPrint('📡 [监听器管理] 从任务 $taskId 移除监听器: $listenerId');

        if (removed is StreamProgressListener) {
          removed.dispose();
        }

        // 如果没有监听器了，清理任务
        if (listeners.isEmpty) {
          _taskListeners.remove(taskId);
        }
      }
    }
  }

  /// 移除任务的所有监听器
  void removeAllTaskListeners(String taskId) {
    final listeners = _taskListeners.remove(taskId);
    if (listeners != null) {
      for (final listener in listeners) {
        if (listener is StreamProgressListener) {
          listener.dispose();
        }
      }
      debugPrint('📡 [监听器管理] 移除任务 $taskId 的所有监听器 (${listeners.length}个)');
    }
  }

  /// 通知进度事件
  void notifyProgressEvent(ProgressEvent event) {
    final taskId = event.taskProgress.taskId;

    // 通知全局监听器
    for (final listener in _globalListeners.values) {
      try {
        listener.onProgressEvent(event);
      } catch (e) {
        debugPrint('❌ [监听器管理] 全局监听器 ${listener.listenerId} 处理事件失败: $e');
      }
    }

    // 通知任务监听器
    final taskListeners = _taskListeners[taskId];
    if (taskListeners != null) {
      for (final listener in taskListeners) {
        try {
          listener.onProgressEvent(event);
        } catch (e) {
          debugPrint('❌ [监听器管理] 任务监听器 ${listener.listenerId} 处理事件失败: $e');
        }
      }
    }

    // 处理特殊事件
    switch (event.type) {
      case ProgressEventType.errorOccurred:
        _notifyError(taskId, event.taskProgress.error ?? '未知错误');
        break;
      case ProgressEventType.taskCompleted:
        _notifyTaskCompleted(taskId, event.taskProgress);
        break;
      case ProgressEventType.taskCancelled:
        _notifyTaskCancelled(taskId, event.taskProgress);
        break;
      default:
        break;
    }
  }

  /// 通知错误
  void _notifyError(String taskId, String error) {
    // 通知全局监听器
    for (final listener in _globalListeners.values) {
      try {
        listener.onError(taskId, error);
      } catch (e) {
        debugPrint('❌ [监听器管理] 全局监听器 ${listener.listenerId} 处理错误失败: $e');
      }
    }

    // 通知任务监听器
    final taskListeners = _taskListeners[taskId];
    if (taskListeners != null) {
      for (final listener in taskListeners) {
        try {
          listener.onError(taskId, error);
        } catch (e) {
          debugPrint('❌ [监听器管理] 任务监听器 ${listener.listenerId} 处理错误失败: $e');
        }
      }
    }
  }

  /// 通知任务完成
  void _notifyTaskCompleted(String taskId, TaskProgress progress) {
    // 通知全局监听器
    for (final listener in _globalListeners.values) {
      try {
        listener.onTaskCompleted(taskId, progress);
      } catch (e) {
        debugPrint('❌ [监听器管理] 全局监听器 ${listener.listenerId} 处理完成事件失败: $e');
      }
    }

    // 通知任务监听器
    final taskListeners = _taskListeners[taskId];
    if (taskListeners != null) {
      for (final listener in taskListeners) {
        try {
          listener.onTaskCompleted(taskId, progress);
        } catch (e) {
          debugPrint('❌ [监听器管理] 任务监听器 ${listener.listenerId} 处理完成事件失败: $e');
        }
      }
    }
  }

  /// 通知任务取消
  void _notifyTaskCancelled(String taskId, TaskProgress progress) {
    // 通知全局监听器
    for (final listener in _globalListeners.values) {
      try {
        listener.onTaskCancelled(taskId, progress);
      } catch (e) {
        debugPrint('❌ [监听器管理] 全局监听器 ${listener.listenerId} 处理取消事件失败: $e');
      }
    }

    // 通知任务监听器
    final taskListeners = _taskListeners[taskId];
    if (taskListeners != null) {
      for (final listener in taskListeners) {
        try {
          listener.onTaskCancelled(taskId, progress);
        } catch (e) {
          debugPrint('❌ [监听器管理] 任务监听器 ${listener.listenerId} 处理取消事件失败: $e');
        }
      }
    }
  }

  /// 获取统计信息
  Map<String, dynamic> getStatistics() {
    final taskListenerCount = _taskListeners.values
        .map((listeners) => listeners.length)
        .fold(0, (sum, count) => sum + count);

    return {
      'global_listeners': _globalListeners.length,
      'task_listeners': taskListenerCount,
      'monitored_tasks': _taskListeners.length,
      'total_listeners': _globalListeners.length + taskListenerCount,
    };
  }

  /// 清理所有监听器
  void dispose() {
    // 清理全局监听器
    for (final listener in _globalListeners.values) {
      if (listener is StreamProgressListener) {
        listener.dispose();
      }
    }
    _globalListeners.clear();

    // 清理任务监听器
    for (final listeners in _taskListeners.values) {
      for (final listener in listeners) {
        if (listener is StreamProgressListener) {
          listener.dispose();
        }
      }
    }
    _taskListeners.clear();

    debugPrint('🧹 [监听器管理] 清理所有监听器');
  }
}
