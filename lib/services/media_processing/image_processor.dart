import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'media_processor.dart';
import 'memory_manager.dart';
import '../webp_image_service.dart';
import '../secure_image_upload_service.dart';
import '../../config/r2_config.dart';

/// 图片处理器
///
/// 包装现有的WebPImageService，实现统一的MediaProcessor接口
class ImageProcessor extends MediaProcessor {
  @override
  MediaType get mediaType => MediaType.image;

  @override
  List<String> get supportedExtensions => R2Config.allowedExtensions;

  @override
  Future<MediaProcessingResult?> processMedia({
    required File file,
    required String userId,
    required MediaProcessingConfig config,
    String? spotId,
    MediaProcessingProgressCallback? progressCallback,
  }) async {
    final stopwatch = Stopwatch()..start();

    try {
      debugPrint('🖼️ [图片处理器] 开始处理图片: ${file.path}');

      // 1. 验证文件
      progressCallback?.call(0.1, '验证图片文件...');
      if (!await validateFile(file)) {
        debugPrint('❌ [图片处理器] 图片验证失败');
        return null;
      }

      // 2. 读取图片信息
      progressCallback?.call(0.2, '读取图片信息...');
      final fileInfo = await getFileInfo(file);
      final originalSize = fileInfo['size'] as int;

      // 3. 处理图片
      progressCallback?.call(0.3, '压缩图片...');
      final imageBytes = await file.readAsBytes();

      // 使用现有的WebP服务处理原图
      final originalResult = await WebPImageService.processImage(
        imageBytes: imageBytes,
        quality: config.quality,
        maxWidth: config.maxWidth,
        maxHeight: config.maxHeight,
      );

      progressCallback?.call(0.6, '生成缩略图...');

      // 4. 生成缩略图（如果需要）
      String? thumbnailUrl;
      if (config.generateThumbnail) {
        final thumbnailResult = await WebPImageService.processImage(
          imageBytes: imageBytes,
          quality: config.thumbnailQuality,
          forThumbnail: true,
        );

        // 上传缩略图
        final secureUploadService = SecureImageUploadService();
        final thumbnailUploadResult = await secureUploadService
            .uploadImageIndependent(
              imageFile: await _createTempFile(
                thumbnailResult.imageBytes,
                'thumb.${thumbnailResult.extension}',
              ),
              userId: userId,
              customFileName: '${DateTime.now().millisecondsSinceEpoch}_thumb',
            );

        thumbnailUrl = thumbnailUploadResult?.thumbnailUrl;
      }

      progressCallback?.call(0.8, '上传图片...');

      // 5. 上传原图
      final processedFile = await _createTempFile(
        originalResult.imageBytes,
        'processed.${originalResult.extension}',
      );
      final secureUploadService = SecureImageUploadService();

      final uploadResult =
          spotId != null
              ? await secureUploadService.uploadImageSecure(
                imageFile: processedFile,
                userId: userId,
                spotId: spotId,
              )
              : await secureUploadService.uploadImageIndependent(
                imageFile: processedFile,
                userId: userId,
              );

      if (uploadResult == null) {
        debugPrint('❌ [图片处理器] 图片上传失败');
        return null;
      }

      progressCallback?.call(1.0, '处理完成');
      stopwatch.stop();

      // 6. 清理临时文件
      await cleanupTempFiles([processedFile]);

      // 7. 返回处理结果
      final compressionRatio =
          originalSize > 0
              ? (originalSize - originalResult.compressedSize) / originalSize
              : 0.0;

      debugPrint('✅ [图片处理器] 图片处理完成');
      debugPrint('🔍 [图片处理器] 原始大小: ${originalSize} bytes');
      debugPrint('🔍 [图片处理器] 压缩后大小: ${originalResult.compressedSize} bytes');
      debugPrint(
        '🔍 [图片处理器] 压缩率: ${(compressionRatio * 100).toStringAsFixed(1)}%',
      );

      return MediaProcessingResult(
        originalUrl: uploadResult.originalUrl,
        thumbnailUrl: thumbnailUrl ?? uploadResult.thumbnailUrl,
        fileName: uploadResult.fileName,
        fileSize: originalResult.compressedSize,
        mediaType: MediaType.image,
        width: originalResult.width,
        height: originalResult.height,
        compressionRatio: compressionRatio,
        processingTime: stopwatch.elapsed,
        metadata: {
          'original_size': originalSize,
          'compressed_size': originalResult.compressedSize,
          'format': originalResult.format,
          'quality': config.quality,
        },
      );
    } catch (e) {
      debugPrint('❌ [图片处理器] 处理异常: $e');
      stopwatch.stop();
      return null;
    }
  }

  @override
  Future<File?> generateThumbnail({
    required File file,
    required MediaProcessingConfig config,
  }) async {
    try {
      debugPrint('🖼️ [图片处理器] 生成缩略图: ${file.path}');

      final imageBytes = await file.readAsBytes();
      final thumbnailResult = await WebPImageService.processImage(
        imageBytes: imageBytes,
        quality: config.thumbnailQuality,
        forThumbnail: true,
      );

      return await _createTempFile(
        thumbnailResult.imageBytes,
        'thumbnail.${thumbnailResult.extension}',
      );
    } catch (e) {
      debugPrint('❌ [图片处理器] 生成缩略图失败: $e');
      return null;
    }
  }

  @override
  Future<bool> validateFile(File file) async {
    try {
      // 检查文件是否存在
      if (!await file.exists()) {
        debugPrint('❌ [图片处理器] 文件不存在: ${file.path}');
        return false;
      }

      // 检查文件大小
      final fileSize = await file.length();
      if (!R2Config.isFileSizeValid(fileSize, false)) {
        debugPrint('❌ [图片处理器] 文件大小超过限制: ${fileSize} bytes');
        return false;
      }

      // 检查文件扩展名
      if (!isSupported(file)) {
        debugPrint('❌ [图片处理器] 不支持的文件类型: ${file.path}');
        return false;
      }

      // 尝试解码图片
      final imageBytes = await file.readAsBytes();
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        debugPrint('❌ [图片处理器] 无法解码图片: ${file.path}');
        return false;
      }

      debugPrint('✅ [图片处理器] 图片验证通过: ${image.width}x${image.height}');
      return true;
    } catch (e) {
      debugPrint('❌ [图片处理器] 验证异常: $e');
      return false;
    }
  }

  @override
  Future<Map<String, dynamic>> getFileInfo(File file) async {
    try {
      final fileSize = await file.length();
      final imageBytes = await file.readAsBytes();
      final image = img.decodeImage(imageBytes);

      return {
        'size': fileSize,
        'width': image?.width ?? 0,
        'height': image?.height ?? 0,
        'extension': file.path.split('.').last.toLowerCase(),
        'mime_type': _getMimeType(file),
        'size_category': R2Config.getFileSizeCategory(fileSize, false),
      };
    } catch (e) {
      debugPrint('❌ [图片处理器] 获取文件信息失败: $e');
      return {
        'size': 0,
        'width': 0,
        'height': 0,
        'extension': '',
        'mime_type': 'application/octet-stream',
        'size_category': 'unknown',
      };
    }
  }

  @override
  Future<void> cleanupTempFiles(List<File> files) async {
    await MemoryManager.instance.deleteTempFiles(files);
  }

  /// 创建临时文件
  Future<File> _createTempFile(Uint8List bytes, String fileName) async {
    final extension = fileName.split('.').last;
    return await MemoryManager.instance.createTempFile(
      data: bytes,
      extension: extension,
      purpose: '图片处理',
    );
  }

  /// 获取MIME类型
  String _getMimeType(File file) {
    final extension = file.path.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'webp':
        return 'image/webp';
      default:
        return 'image/jpeg';
    }
  }
}
