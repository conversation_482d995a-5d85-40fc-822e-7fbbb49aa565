import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'media_processor.dart';
import 'media_processor_factory.dart';
import 'thumbnail_generator.dart';
import 'media_error_handler.dart';
import 'progress_manager.dart';
import 'progress_models.dart';
import 'progress_listener.dart';
import '../encrypted_r2_service.dart';
import '../../config/r2_config.dart';

/// 统一媒体服务
///
/// 提供统一的媒体处理接口，支持图片和视频的处理、上传和管理
class UnifiedMediaService {
  static UnifiedMediaService? _instance;
  static UnifiedMediaService get instance =>
      _instance ??= UnifiedMediaService._();

  UnifiedMediaService._();

  final MediaProcessorFactory _factory = MediaProcessorFactory.instance;
  final ThumbnailGenerator _thumbnailGenerator = ThumbnailGenerator.instance;
  final MediaErrorHandler _errorHandler = MediaErrorHandler.instance;
  final ProgressManager _progressManager = ProgressManager.instance;
  final EncryptedR2Service _r2Service = EncryptedR2Service();

  // 签名URL缓存
  final Map<String, String> _signedUrlCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const int _cacheExpiryMinutes = 50; // 缓存50分钟，签名URL有效期1小时

  /// 处理媒体文件（统一入口）
  ///
  /// [file] 媒体文件
  /// [userId] 用户ID
  /// [spotId] 钓点ID（可选）
  /// [config] 处理配置（可选，使用智能配置）
  /// [progressCallback] 进度回调
  /// [taskId] 任务ID（可选，自动生成）
  ///
  /// 返回处理结果
  Future<MediaProcessingResult?> processMedia({
    required File file,
    required String userId,
    String? spotId,
    MediaProcessingConfig? config,
    MediaProcessingProgressCallback? progressCallback,
    String? taskId,
  }) async {
    // 生成任务ID
    final currentTaskId = taskId ?? ProgressManager.generateTaskId();

    try {
      debugPrint('🎯 [统一媒体服务] 开始处理媒体文件: ${file.path}');

      // 1. 获取对应的处理器
      final processor = _factory.getProcessorForFile(file);
      if (processor == null) {
        debugPrint('❌ [统一媒体服务] 不支持的文件类型: ${file.path}');
        return null;
      }

      // 2. 创建配置（如果未提供）
      final mediaType = _factory.getMediaType(file)!;
      final processingConfig =
          config ?? await _factory.createSmartConfig(file, mediaType);

      debugPrint(
        '🔧 [统一媒体服务] 使用${mediaType.name}处理器，质量: ${processingConfig.quality}',
      );

      // 3. 创建进度任务
      final fileSize = await file.length();
      final taskType =
          mediaType == MediaType.image
              ? TaskType.imageProcessing
              : TaskType.videoProcessing;

      _progressManager.createTask(
        taskId: currentTaskId,
        taskType: taskType,
        fileName: file.path.split('/').last,
        fileSize: fileSize,
        metadata: {
          'user_id': userId,
          'spot_id': spotId,
          'media_type': mediaType.name,
          'quality': processingConfig.quality,
        },
      );

      // 4. 处理媒体文件
      final result = await processor.processMedia(
        file: file,
        userId: userId,
        config: processingConfig,
        spotId: spotId,
        progressCallback: (progress, message) {
          // 更新进度管理器
          _progressManager.updateTaskProgress(
            taskId: currentTaskId,
            progress: progress,
            message: message,
            status:
                progress >= 1.0
                    ? TaskProgressStatus.completed
                    : TaskProgressStatus.processing,
          );

          // 调用原始回调
          progressCallback?.call(progress, message);
        },
      );

      if (result != null) {
        // 标记任务完成
        _progressManager.completeTask(currentTaskId, message: '媒体处理完成');

        debugPrint('✅ [统一媒体服务] 媒体处理完成: ${result.fileName}');
        debugPrint('🔍 [统一媒体服务] 文件大小: ${result.formattedFileSize}');
        debugPrint('🔍 [统一媒体服务] 处理耗时: ${result.processingTime.inSeconds}s');
        return result;
      } else {
        // 标记任务失败
        _progressManager.failTask(currentTaskId, error: '媒体处理失败');

        throw MediaProcessingException(
          type: MediaErrorType.unknown,
          message: '媒体处理失败',
        );
      }
    } catch (e) {
      // 标记任务失败
      final errorMessage = _errorHandler.handleError(e);
      _progressManager.failTask(currentTaskId, error: errorMessage);

      debugPrint('❌ [统一媒体服务] $errorMessage');
      rethrow; // 重新抛出异常，让调用者处理
    }
  }

  /// 批量处理媒体文件
  ///
  /// [files] 文件列表
  /// [userId] 用户ID
  /// [spotId] 钓点ID（可选）
  /// [progressCallback] 进度回调
  /// [batchId] 批量任务ID（可选）
  ///
  /// 返回处理结果列表
  Future<List<MediaProcessingResult?>> processMediaBatch({
    required List<File> files,
    required String userId,
    String? spotId,
    MediaProcessingProgressCallback? progressCallback,
    String? batchId,
  }) async {
    // 生成批量任务ID和子任务ID
    final currentBatchId = batchId ?? ProgressManager.generateTaskId();
    final subTaskIds =
        files.map((file) => ProgressManager.generateTaskId()).toList();

    // 创建批量任务
    _progressManager.createBatchTask(
      batchId: currentBatchId,
      taskIds: subTaskIds,
      metadata: {
        'user_id': userId,
        'spot_id': spotId,
        'file_count': files.length,
      },
    );

    final results = <MediaProcessingResult?>[];

    try {
      for (int i = 0; i < files.length; i++) {
        final file = files[i];
        final subTaskId = subTaskIds[i];

        // 更新批量处理进度
        final batchProgress = i / files.length;
        progressCallback?.call(batchProgress, '处理文件 ${i + 1}/${files.length}');

        final result = await processMedia(
          file: file,
          userId: userId,
          spotId: spotId,
          taskId: subTaskId,
          progressCallback: (progress, message) {
            // 计算总体进度
            final totalProgress = batchProgress + (progress / files.length);
            progressCallback?.call(totalProgress, message);
          },
        );

        results.add(result);
      }

      // 完成批量任务
      _progressManager.completeTask(currentBatchId, message: '批量处理完成');
      progressCallback?.call(1.0, '批量处理完成');
    } catch (e) {
      // 批量任务失败
      _progressManager.failTask(currentBatchId, error: '批量处理失败: $e');
      rethrow;
    }

    return results;
  }

  /// 仅生成缩略图（不上传）
  ///
  /// [file] 媒体文件
  /// [config] 处理配置（可选）
  ///
  /// 返回缩略图文件
  Future<File?> generateThumbnailOnly({
    required File file,
    MediaProcessingConfig? config,
  }) async {
    try {
      final mediaType = _factory.getMediaType(file);
      if (mediaType == null) {
        debugPrint('❌ [统一媒体服务] 不支持的文件类型: ${file.path}');
        return null;
      }

      final processingConfig =
          config ?? _factory.createDefaultConfig(mediaType);

      return await _thumbnailGenerator.generateThumbnail(
        file: file,
        mediaType: mediaType,
        config: processingConfig,
      );
    } catch (e) {
      debugPrint('❌ [统一媒体服务] 生成缩略图异常: $e');
      return null;
    }
  }

  /// 验证媒体文件
  ///
  /// [file] 要验证的文件
  ///
  /// 返回验证结果
  Future<bool> validateMediaFile(File file) async {
    try {
      final processor = _factory.getProcessorForFile(file);
      if (processor == null) {
        return false;
      }

      return await processor.validateFile(file);
    } catch (e) {
      debugPrint('❌ [统一媒体服务] 验证文件异常: $e');
      return false;
    }
  }

  /// 获取媒体文件信息
  ///
  /// [file] 文件
  ///
  /// 返回文件信息
  Future<Map<String, dynamic>?> getMediaFileInfo(File file) async {
    try {
      final processor = _factory.getProcessorForFile(file);
      if (processor == null) {
        return null;
      }

      final info = await processor.getFileInfo(file);
      info['media_type'] = processor.mediaType.name;
      return info;
    } catch (e) {
      debugPrint('❌ [统一媒体服务] 获取文件信息异常: $e');
      return null;
    }
  }

  /// 生成预签名URL
  ///
  /// [originalUrl] 原始R2 URL
  ///
  /// 返回预签名URL
  Future<String?> generateSignedUrl(String originalUrl) async {
    try {
      // 检查缓存
      if (_signedUrlCache.containsKey(originalUrl)) {
        final timestamp = _cacheTimestamps[originalUrl];
        if (timestamp != null) {
          final age = DateTime.now().difference(timestamp);
          if (age.inMinutes < _cacheExpiryMinutes) {
            debugPrint('🔍 [统一媒体服务] 使用缓存的签名URL');
            return _signedUrlCache[originalUrl];
          }
        }
      }

      // 从原始URL中提取对象键
      final objectKey = _extractObjectKey(originalUrl);
      if (objectKey.isEmpty) {
        debugPrint('❌ [统一媒体服务] 无法从URL中提取对象键: $originalUrl');
        return originalUrl; // 返回原始URL作为后备
      }

      // 生成签名URL
      final signedUrl = await _r2Service.generatePresignedUrl(
        objectKey: objectKey,
        method: 'GET',
        expiresIn: 3600, // 1小时有效期
      );

      if (signedUrl != null) {
        // 缓存签名URL
        _signedUrlCache[originalUrl] = signedUrl;
        _cacheTimestamps[originalUrl] = DateTime.now();
        debugPrint('✅ [统一媒体服务] 签名URL生成成功');
      }

      return signedUrl ?? originalUrl;
    } catch (e) {
      debugPrint('❌ [统一媒体服务] 生成签名URL异常: $e');
      return originalUrl; // 返回原始URL作为后备
    }
  }

  /// 清理临时文件
  ///
  /// [files] 要清理的文件列表
  Future<void> cleanupTempFiles(List<File> files) async {
    for (final file in files) {
      try {
        if (await file.exists()) {
          await file.delete();
          debugPrint('🧹 [统一媒体服务] 清理临时文件: ${file.path}');
        }
      } catch (e) {
        debugPrint('⚠️ [统一媒体服务] 清理临时文件失败: ${file.path}, $e');
      }
    }
  }

  /// 获取支持的文件类型
  List<String> getSupportedExtensions() {
    return _factory.getAllSupportedExtensions();
  }

  /// 检查文件是否被支持
  bool isFileSupported(File file) {
    return _factory.isFileSupported(file);
  }

  /// 获取任务进度
  ///
  /// [taskId] 任务ID
  ///
  /// 返回任务进度
  TaskProgress? getTaskProgress(String taskId) {
    return _progressManager.getTaskProgress(taskId);
  }

  /// 取消任务
  ///
  /// [taskId] 任务ID
  /// [message] 取消消息（可选）
  ///
  /// 返回是否成功取消
  bool cancelTask(String taskId, {String? message}) {
    final result = _progressManager.cancelTask(taskId, message: message);
    return result != null;
  }

  /// 获取所有运行中的任务
  List<TaskProgress> getRunningTasks() {
    return _progressManager.getRunningTasks();
  }

  /// 添加进度监听器
  ///
  /// [listener] 进度监听器
  void addProgressListener(ProgressListener listener) {
    _progressManager.addGlobalListener(listener);
  }

  /// 移除进度监听器
  ///
  /// [listenerId] 监听器ID
  void removeProgressListener(String listenerId) {
    _progressManager.removeGlobalListener(listenerId);
  }

  /// 添加任务监听器
  ///
  /// [taskId] 任务ID
  /// [listener] 进度监听器
  void addTaskProgressListener(String taskId, ProgressListener listener) {
    _progressManager.addTaskListener(taskId, listener);
  }

  /// 获取服务统计信息
  Map<String, dynamic> getStatistics() {
    return {
      'factory_stats': _factory.getStatistics(),
      'thumbnail_stats': _thumbnailGenerator.getStatistics(),
      'progress_stats': _progressManager.getStatistics(),
      'cached_signed_urls': _signedUrlCache.length,
      'cache_hit_rate': _calculateCacheHitRate(),
    };
  }

  /// 清理缓存
  void clearCache() {
    _signedUrlCache.clear();
    _cacheTimestamps.clear();
    _factory.clearCache();
    debugPrint('🧹 [统一媒体服务] 清理所有缓存');
  }

  /// 预热服务
  void warmUp() {
    debugPrint('🔥 [统一媒体服务] 预热服务');
    _factory.warmUp();
  }

  /// 从URL中提取对象键
  String _extractObjectKey(String url) {
    try {
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;

      // 移除bucket名称，获取实际的对象键
      if (pathSegments.isNotEmpty &&
          pathSegments.first == R2Config.bucketName) {
        return pathSegments.skip(1).join('/');
      }

      return pathSegments.join('/');
    } catch (e) {
      debugPrint('❌ [统一媒体服务] 解析URL失败: $e');
      return '';
    }
  }

  /// 计算缓存命中率
  double _calculateCacheHitRate() {
    // 这里可以实现更复杂的缓存命中率计算逻辑
    // 目前简单返回缓存数量比例
    return _signedUrlCache.isNotEmpty ? 0.8 : 0.0;
  }
}
