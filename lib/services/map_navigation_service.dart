import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';

/// 地图导航参数（简化版）
class MapNavigationParams {
  final LatLng targetLocation;        // 目标位置坐标
  final bool showTemporaryMarker;     // 是否显示临时位置标记
  
  const MapNavigationParams({
    required this.targetLocation,
    this.showTemporaryMarker = false,
  });
  
  Map<String, dynamic> toJson() => {
    'targetLocation': {
      'latitude': targetLocation.latitude,
      'longitude': targetLocation.longitude,
    },
    'showTemporaryMarker': showTemporaryMarker,
  };
  
  static MapNavigationParams fromJson(Map<String, dynamic> json) {
    return MapNavigationParams(
      targetLocation: LatLng(
        json['targetLocation']['latitude'],
        json['targetLocation']['longitude'],
      ),
      showTemporaryMarker: json['showTemporaryMarker'] ?? false,
    );
  }
}

/// 统一的地图导航服务
class MapNavigationService {
  static MapNavigationService? _instance;
  static MapNavigationService get instance => _instance ??= MapNavigationService._();
  MapNavigationService._();
  
  Function(MapNavigationParams)? _mapNavigationCallback;
  
  void registerMapNavigationCallback(Function(MapNavigationParams) callback) {
    _mapNavigationCallback = callback;
  }
  
  void unregisterMapNavigationCallback() {
    _mapNavigationCallback = null;
  }
  
  /// 导航到钓点
  Future<bool> navigateToSpot({
    required LatLng spotLocation,
    BuildContext? context,
    bool forceNavigateToMapPage = false, // 强制跳转到地图页面
  }) async {
    final params = MapNavigationParams(
      targetLocation: spotLocation,
      showTemporaryMarker: false, // 钓点导航不显示临时标记
    );
    
    return _executeNavigation(params, context, forceNavigateToMapPage: forceNavigateToMapPage);
  }
  
  /// 导航到搜索位置
  Future<bool> navigateToSearchLocation({
    required LatLng location,
    BuildContext? context,
  }) async {
    final params = MapNavigationParams(
      targetLocation: location,
      showTemporaryMarker: true, // 搜索导航显示临时标记
    );
    
    return _executeNavigation(params, context, forceNavigateToMapPage: false);
  }
  
  /// 导航到GPS位置
  Future<bool> navigateToGpsLocation({
    required LatLng gpsLocation,
    BuildContext? context,
  }) async {
    final params = MapNavigationParams(
      targetLocation: gpsLocation,
      showTemporaryMarker: false, // GPS定位不显示临时标记
    );
    
    return _executeNavigation(params, context, forceNavigateToMapPage: false);
  }
  
  Future<bool> _executeNavigation(MapNavigationParams params, BuildContext? context, {bool forceNavigateToMapPage = false}) async {
    try {
      debugPrint('🗺️ [统一导航] 开始执行导航');
      debugPrint('🗺️ [统一导航] 目标位置: ${params.targetLocation}');
      debugPrint('🗺️ [统一导航] 显示临时标记: ${params.showTemporaryMarker}');
      debugPrint('🗺️ [统一导航] 强制跳转到地图页面: $forceNavigateToMapPage');
      
      // 如果强制跳转到地图页面，或者当前不在主页面，需要跳转
      if (forceNavigateToMapPage || _mapNavigationCallback == null) {
        if (context != null) {
          debugPrint('🗺️ [统一导航] 跳转到地图页面');
          return await _navigateToMapPage(params, context);
        } else {
          debugPrint('❌ [统一导航] 需要跳转但缺少上下文');
          return false;
        }
      }
      
      // 检查当前是否在主页面（地图页面可用）
      if (_mapNavigationCallback != null) {
        debugPrint('🗺️ [统一导航] 当前在主页面，直接调用地图页面回调');
        _mapNavigationCallback!(params);
        return true;
      }
      
      debugPrint('❌ [统一导航] 无法执行导航：缺少上下文或回调');
      return false;
    } catch (e) {
      debugPrint('❌ [统一导航] 导航失败: $e');
      return false;
    }
  }
  
  Future<bool> _navigateToMapPage(MapNavigationParams params, BuildContext context) async {
    try {
      final arguments = {
        'initialIndex': 0,
        'navigationParams': params.toJson(),
      };
      
      Navigator.of(context).pushReplacementNamed('/main', arguments: arguments);
      return true;
    } catch (e) {
      debugPrint('❌ [统一导航] 导航到地图页面失败: $e');
      return false;
    }
  }
}