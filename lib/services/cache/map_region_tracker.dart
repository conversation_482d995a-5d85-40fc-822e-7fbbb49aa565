import 'dart:math' as math;
import 'dart:math' show cos;
import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import 'cache_structures.dart';

/// 地图区域变化类型
enum RegionChangeType {
  /// 新增区域
  added,

  /// 移除区域
  removed,

  /// 重叠区域
  overlapped,

  /// 无变化
  unchanged,
}

/// 区域变化信息
class RegionChange {
  /// 变化类型
  final RegionChangeType type;

  /// 相关区域
  final MapRegion region;

  /// 重叠比例（仅对overlapped类型有效）
  final double? overlapRatio;

  const RegionChange({
    required this.type,
    required this.region,
    this.overlapRatio,
  });

  @override
  String toString() {
    return 'RegionChange(type: $type, region: ${region.id}, overlapRatio: $overlapRatio)';
  }
}

/// 区域变化结果
class RegionChangeResult {
  /// 新增的区域
  final List<MapRegion> addedRegions;

  /// 移除的区域
  final List<MapRegion> removedRegions;

  /// 重叠的区域
  final List<MapRegion> overlappedRegions;

  /// 保持不变的区域
  final List<MapRegion> unchangedRegions;

  /// 所有变化
  final List<RegionChange> allChanges;

  const RegionChangeResult({
    required this.addedRegions,
    required this.removedRegions,
    required this.overlappedRegions,
    required this.unchangedRegions,
    required this.allChanges,
  });

  /// 是否有任何变化
  bool get hasChanges => addedRegions.isNotEmpty || removedRegions.isNotEmpty;

  /// 需要加载数据的区域（新增 + 部分重叠）
  List<MapRegion> get regionsNeedingData {
    final result = <MapRegion>[];
    result.addAll(addedRegions);

    // 添加重叠度较低的区域（可能需要补充数据）
    for (final region in overlappedRegions) {
      final change = allChanges.firstWhere(
        (c) =>
            c.region.id == region.id && c.type == RegionChangeType.overlapped,
      );
      if (change.overlapRatio != null && change.overlapRatio! < 0.8) {
        result.add(region);
      }
    }

    return result;
  }

  @override
  String toString() {
    return 'RegionChangeResult(added: ${addedRegions.length}, removed: ${removedRegions.length}, overlapped: ${overlappedRegions.length}, unchanged: ${unchangedRegions.length})';
  }
}

/// 地图区域跟踪器
///
/// 负责跟踪地图视口的变化，计算区域的新增、移除和重叠
/// 支持智能的增量更新策略
class MapRegionTracker {
  /// 当前活跃的区域
  final List<MapRegion> _currentRegions = [];

  /// 历史区域记录（用于分析用户行为模式）
  final List<MapRegion> _historicalRegions = [];

  /// 最大历史记录数量
  final int maxHistorySize;

  /// 区域重叠阈值（超过此值认为是重叠区域）
  final double overlapThreshold;

  /// 最小区域大小（半径，公里）
  final double minRegionRadius;

  /// 最大区域大小（半径，公里）
  final double maxRegionRadius;

  MapRegionTracker({
    this.maxHistorySize = 100,
    this.overlapThreshold = 0.3,
    this.minRegionRadius = 1.0,
    this.maxRegionRadius = 50.0,
  });

  // ==================== 区域跟踪 ====================

  /// 更新当前区域并计算变化
  RegionChangeResult updateRegions(List<MapRegion> newRegions) {
    debugPrint('🗺️ [区域跟踪] 更新区域: ${newRegions.length} 个新区域');

    final result = _calculateRegionChanges(_currentRegions, newRegions);

    // 更新当前区域
    _updateCurrentRegions(newRegions);

    // 记录到历史
    _addToHistory(newRegions);

    debugPrint('🗺️ [区域跟踪] 变化结果: $result');
    return result;
  }

  /// 基于地图视口创建区域
  MapRegion createRegionFromViewport({
    required LatLng center,
    required double zoomLevel,
    double? customRadius,
  }) {
    // 根据缩放级别计算合适的区域半径
    double radius = customRadius ?? _calculateRadiusFromZoom(zoomLevel);

    // 限制半径范围
    radius = radius.clamp(minRegionRadius, maxRegionRadius);

    return MapRegion.fromCenterRadius(center: center, radiusKm: radius);
  }

  /// 批量创建区域（用于预加载）
  List<MapRegion> createRegionsForPreloading({
    required LatLng center,
    required double zoomLevel,
    int gridSize = 3,
  }) {
    final regions = <MapRegion>[];
    final baseRadius = _calculateRadiusFromZoom(zoomLevel);

    // 创建网格状的区域布局
    final step = baseRadius * 1.5; // 区域间距
    final offset = (gridSize - 1) / 2;

    for (int i = 0; i < gridSize; i++) {
      for (int j = 0; j < gridSize; j++) {
        final latOffset = (i - offset) * step / 111.0; // 大约每度111公里
        final lngOffset =
            (j - offset) *
            step /
            (111.0 * cos(center.latitude * math.pi / 180));

        final regionCenter = LatLng(
          center.latitude + latOffset,
          center.longitude + lngOffset,
        );

        regions.add(
          MapRegion.fromCenterRadius(
            center: regionCenter,
            radiusKm: baseRadius,
          ),
        );
      }
    }

    return regions;
  }

  // ==================== 变化计算 ====================

  /// 计算区域变化
  RegionChangeResult _calculateRegionChanges(
    List<MapRegion> oldRegions,
    List<MapRegion> newRegions,
  ) {
    final addedRegions = <MapRegion>[];
    final removedRegions = <MapRegion>[];
    final overlappedRegions = <MapRegion>[];
    final unchangedRegions = <MapRegion>[];
    final allChanges = <RegionChange>[];

    // 找出移除的区域
    for (final oldRegion in oldRegions) {
      bool found = false;
      for (final newRegion in newRegions) {
        if (_areRegionsSimilar(oldRegion, newRegion)) {
          found = true;
          break;
        }
      }
      if (!found) {
        removedRegions.add(oldRegion);
        allChanges.add(
          RegionChange(type: RegionChangeType.removed, region: oldRegion),
        );
      }
    }

    // 分析新区域
    for (final newRegion in newRegions) {
      RegionChangeType changeType = RegionChangeType.added;
      double? maxOverlapRatio;

      // 检查与旧区域的关系
      for (final oldRegion in oldRegions) {
        if (_areRegionsSimilar(oldRegion, newRegion)) {
          changeType = RegionChangeType.unchanged;
          break;
        } else if (oldRegion.overlaps(newRegion)) {
          final overlapRatio = newRegion.overlapRatio(oldRegion);
          if (overlapRatio > overlapThreshold) {
            changeType = RegionChangeType.overlapped;
            maxOverlapRatio = math.max(maxOverlapRatio ?? 0, overlapRatio);
          }
        }
      }

      // 分类区域
      switch (changeType) {
        case RegionChangeType.added:
          addedRegions.add(newRegion);
          break;
        case RegionChangeType.overlapped:
          overlappedRegions.add(newRegion);
          break;
        case RegionChangeType.unchanged:
          unchangedRegions.add(newRegion);
          break;
        case RegionChangeType.removed:
          // 不会在这里处理
          break;
      }

      allChanges.add(
        RegionChange(
          type: changeType,
          region: newRegion,
          overlapRatio: maxOverlapRatio,
        ),
      );
    }

    return RegionChangeResult(
      addedRegions: addedRegions,
      removedRegions: removedRegions,
      overlappedRegions: overlappedRegions,
      unchangedRegions: unchangedRegions,
      allChanges: allChanges,
    );
  }

  /// 检查两个区域是否相似（位置和大小接近）
  bool _areRegionsSimilar(MapRegion region1, MapRegion region2) {
    const Distance distance = Distance();
    final centerDistance = distance.as(
      LengthUnit.Kilometer,
      region1.center,
      region2.center,
    );

    // 中心点距离小于较小半径的50%，且半径差异小于20%
    final minRadius = math.min(region1.radiusKm, region2.radiusKm);
    final radiusDiff = (region1.radiusKm - region2.radiusKm).abs();

    return centerDistance < minRadius * 0.5 && radiusDiff < minRadius * 0.2;
  }

  /// 根据缩放级别计算区域半径
  double _calculateRadiusFromZoom(double zoomLevel) {
    // 缩放级别越高，区域越小
    // 这是一个经验公式，可以根据实际需要调整
    const baseRadius = 10.0; // 基础半径（公里）
    const zoomFactor = 0.7; // 缩放因子

    return baseRadius * math.pow(zoomFactor, zoomLevel - 10);
  }

  // ==================== 历史管理 ====================

  /// 更新当前区域
  void _updateCurrentRegions(List<MapRegion> newRegions) {
    _currentRegions.clear();
    _currentRegions.addAll(newRegions);
  }

  /// 添加到历史记录
  void _addToHistory(List<MapRegion> regions) {
    for (final region in regions) {
      _historicalRegions.add(region);
    }

    // 限制历史记录大小
    while (_historicalRegions.length > maxHistorySize) {
      _historicalRegions.removeAt(0);
    }
  }

  /// 获取当前区域
  List<MapRegion> get currentRegions => List.unmodifiable(_currentRegions);

  /// 获取历史区域
  List<MapRegion> get historicalRegions =>
      List.unmodifiable(_historicalRegions);

  // ==================== 智能预测 ====================

  /// 预测用户可能访问的区域
  List<MapRegion> predictNextRegions({
    required LatLng currentCenter,
    required double currentZoom,
    int maxPredictions = 5,
  }) {
    final predictions = <MapRegion>[];

    if (_historicalRegions.length < 3) {
      // 历史数据不足，返回周围区域
      return _createSurroundingRegions(currentCenter, currentZoom);
    }

    // 分析历史移动模式
    final recentRegions = _historicalRegions.reversed.take(10).toList();
    final movementVectors = <LatLng>[];

    for (int i = 1; i < recentRegions.length; i++) {
      final prev = recentRegions[i].center;
      final curr = recentRegions[i - 1].center;
      movementVectors.add(
        LatLng(curr.latitude - prev.latitude, curr.longitude - prev.longitude),
      );
    }

    if (movementVectors.isNotEmpty) {
      // 计算平均移动向量
      final avgVector = LatLng(
        movementVectors.map((v) => v.latitude).reduce((a, b) => a + b) /
            movementVectors.length,
        movementVectors.map((v) => v.longitude).reduce((a, b) => a + b) /
            movementVectors.length,
      );

      // 基于移动向量预测下一个位置
      final predictedCenter = LatLng(
        currentCenter.latitude + avgVector.latitude,
        currentCenter.longitude + avgVector.longitude,
      );

      predictions.add(
        createRegionFromViewport(
          center: predictedCenter,
          zoomLevel: currentZoom,
        ),
      );
    }

    // 添加周围区域作为补充
    predictions.addAll(_createSurroundingRegions(currentCenter, currentZoom));

    return predictions.take(maxPredictions).toList();
  }

  /// 创建周围区域
  List<MapRegion> _createSurroundingRegions(LatLng center, double zoomLevel) {
    final radius = _calculateRadiusFromZoom(zoomLevel);
    final regions = <MapRegion>[];

    // 创建8个方向的周围区域
    const directions = [
      [0, 1], // 北
      [1, 1], // 东北
      [1, 0], // 东
      [1, -1], // 东南
      [0, -1], // 南
      [-1, -1], // 西南
      [-1, 0], // 西
      [-1, 1], // 西北
    ];

    for (final dir in directions) {
      final latOffset = dir[1] * radius / 111.0;
      final lngOffset =
          dir[0] * radius / (111.0 * cos(center.latitude * math.pi / 180));

      final regionCenter = LatLng(
        center.latitude + latOffset,
        center.longitude + lngOffset,
      );

      regions.add(
        createRegionFromViewport(center: regionCenter, zoomLevel: zoomLevel),
      );
    }

    return regions;
  }

  // ==================== 统计和调试 ====================

  /// 获取跟踪统计信息
  Map<String, dynamic> getTrackingStats() {
    return {
      'current_regions': _currentRegions.length,
      'historical_regions': _historicalRegions.length,
      'max_history_size': maxHistorySize,
      'overlap_threshold': overlapThreshold,
      'min_region_radius': minRegionRadius,
      'max_region_radius': maxRegionRadius,
    };
  }

  /// 清空所有数据
  void clear() {
    _currentRegions.clear();
    _historicalRegions.clear();
    debugPrint('🧹 [区域跟踪] 已清空所有跟踪数据');
  }
}
