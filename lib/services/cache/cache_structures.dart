import 'dart:math' as math;
import 'dart:math' show cos;
import 'package:latlong2/latlong.dart';
import '../../models/marker_id_info.dart';
import '../../models/unified_marker.dart';

/// 简单的地理边界类
class LatLngBounds {
  final LatLng southWest;
  final LatLng northEast;

  const LatLngBounds(this.southWest, this.northEast);

  double get south => southWest.latitude;
  double get west => southWest.longitude;
  double get north => northEast.latitude;
  double get east => northEast.longitude;

  bool contains(LatLng point) {
    return point.latitude >= south &&
        point.latitude <= north &&
        point.longitude >= west &&
        point.longitude <= east;
  }

  bool overlaps(LatLngBounds other) {
    return !(other.south > north ||
        other.north < south ||
        other.west > east ||
        other.east < west);
  }
}

/// 缓存级别枚举
enum CacheLevel {
  /// Level 1: ID缓存 - 只包含ID和更新时间
  ids,

  /// Level 2: 摘要缓存 - 包含显示和排序所需的基础信息
  summaries,

  /// Level 3: 详细缓存 - 包含完整的详细信息
  details,
}

/// 地图区域定义
class MapRegion {
  /// 区域唯一标识符
  final String id;

  /// 区域边界
  final LatLngBounds bounds;

  /// 区域中心点
  final LatLng center;

  /// 区域半径（公里）
  final double radiusKm;

  /// 创建时间
  final DateTime createdAt;

  /// 最后访问时间
  final DateTime lastAccessedAt;

  const MapRegion({
    required this.id,
    required this.bounds,
    required this.center,
    required this.radiusKm,
    required this.createdAt,
    required this.lastAccessedAt,
  });

  /// 从中心点和半径创建区域
  factory MapRegion.fromCenterRadius({
    required LatLng center,
    required double radiusKm,
    DateTime? createdAt,
    DateTime? lastAccessedAt,
  }) {
    final now = DateTime.now();

    // 计算边界（简化计算，实际应该考虑地球曲率）
    const kmPerDegree = 111.0; // 大约每度111公里
    final latOffset = radiusKm / kmPerDegree;
    final lngOffset =
        radiusKm / (kmPerDegree * cos(center.latitude * math.pi / 180));

    final bounds = LatLngBounds(
      LatLng(center.latitude - latOffset, center.longitude - lngOffset),
      LatLng(center.latitude + latOffset, center.longitude + lngOffset),
    );

    final id = _generateRegionId(center, radiusKm);

    return MapRegion(
      id: id,
      bounds: bounds,
      center: center,
      radiusKm: radiusKm,
      createdAt: createdAt ?? now,
      lastAccessedAt: lastAccessedAt ?? now,
    );
  }

  /// 生成区域ID
  static String _generateRegionId(LatLng center, double radiusKm) {
    // 使用中心点坐标和半径生成唯一ID
    final latStr = center.latitude.toStringAsFixed(4);
    final lngStr = center.longitude.toStringAsFixed(4);
    final radiusStr = radiusKm.toStringAsFixed(1);
    return '${latStr}_${lngStr}_$radiusStr';
  }

  /// 检查点是否在区域内
  bool contains(LatLng point) {
    return bounds.contains(point);
  }

  /// 检查与另一个区域是否重叠
  bool overlaps(MapRegion other) {
    return bounds.overlaps(other.bounds);
  }

  /// 计算与另一个区域的重叠面积比例
  double overlapRatio(MapRegion other) {
    if (!overlaps(other)) return 0.0;

    // 简化计算重叠面积比例
    final intersection = _calculateIntersection(bounds, other.bounds);
    if (intersection == null) return 0.0;

    final thisArea = _calculateArea(bounds);
    final intersectionArea = _calculateArea(intersection);

    return intersectionArea / thisArea;
  }

  /// 计算两个边界的交集
  LatLngBounds? _calculateIntersection(LatLngBounds a, LatLngBounds b) {
    final maxSouth = math.max(a.south, b.south);
    final minNorth = math.min(a.north, b.north);
    final maxWest = math.max(a.west, b.west);
    final minEast = math.min(a.east, b.east);

    if (maxSouth >= minNorth || maxWest >= minEast) {
      return null; // 没有交集
    }

    return LatLngBounds(LatLng(maxSouth, maxWest), LatLng(minNorth, minEast));
  }

  /// 计算边界面积（简化计算）
  double _calculateArea(LatLngBounds bounds) {
    final latDiff = bounds.north - bounds.south;
    final lngDiff = bounds.east - bounds.west;
    return latDiff * lngDiff;
  }

  /// 更新最后访问时间
  MapRegion updateLastAccessed() {
    return MapRegion(
      id: id,
      bounds: bounds,
      center: center,
      radiusKm: radiusKm,
      createdAt: createdAt,
      lastAccessedAt: DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MapRegion && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MapRegion(id: $id, center: $center, radius: ${radiusKm}km)';
  }
}

/// Level 1 缓存结构 - ID缓存
class IdCacheEntry {
  /// 标记ID信息
  final MarkerIdInfo markerInfo;

  /// 缓存时间
  final DateTime cachedAt;

  /// 所属区域ID列表
  final Set<String> regionIds;

  const IdCacheEntry({
    required this.markerInfo,
    required this.cachedAt,
    required this.regionIds,
  });

  /// 添加区域ID
  IdCacheEntry addRegion(String regionId) {
    final newRegionIds = Set<String>.from(regionIds)..add(regionId);
    return IdCacheEntry(
      markerInfo: markerInfo,
      cachedAt: cachedAt,
      regionIds: newRegionIds,
    );
  }

  /// 移除区域ID
  IdCacheEntry removeRegion(String regionId) {
    final newRegionIds = Set<String>.from(regionIds)..remove(regionId);
    return IdCacheEntry(
      markerInfo: markerInfo,
      cachedAt: cachedAt,
      regionIds: newRegionIds,
    );
  }

  /// 检查是否过期
  bool isExpired(Duration maxAge) {
    return DateTime.now().difference(cachedAt) > maxAge;
  }

  Map<String, dynamic> toJson() {
    return {
      'marker_info': markerInfo.toJson(),
      'cached_at': cachedAt.toIso8601String(),
      'region_ids': regionIds.toList(),
    };
  }

  static IdCacheEntry fromJson(Map<String, dynamic> json) {
    return IdCacheEntry(
      markerInfo: MarkerIdInfo.fromJson(json['marker_info']),
      cachedAt: DateTime.parse(json['cached_at']),
      regionIds: Set<String>.from(json['region_ids']),
    );
  }
}

/// Level 2 缓存结构 - 摘要缓存
class SummaryCacheEntry {
  /// 统一标记摘要
  final UnifiedMarker marker;

  /// 缓存时间
  final DateTime cachedAt;

  /// 所属区域ID列表
  final Set<String> regionIds;

  const SummaryCacheEntry({
    required this.marker,
    required this.cachedAt,
    required this.regionIds,
  });

  /// 添加区域ID
  SummaryCacheEntry addRegion(String regionId) {
    final newRegionIds = Set<String>.from(regionIds)..add(regionId);
    return SummaryCacheEntry(
      marker: marker,
      cachedAt: cachedAt,
      regionIds: newRegionIds,
    );
  }

  /// 移除区域ID
  SummaryCacheEntry removeRegion(String regionId) {
    final newRegionIds = Set<String>.from(regionIds)..remove(regionId);
    return SummaryCacheEntry(
      marker: marker,
      cachedAt: cachedAt,
      regionIds: newRegionIds,
    );
  }

  /// 检查是否过期
  bool isExpired(Duration maxAge) {
    return DateTime.now().difference(cachedAt) > maxAge;
  }

  Map<String, dynamic> toJson() {
    return {
      'marker': marker.toJson(),
      'cached_at': cachedAt.toIso8601String(),
      'region_ids': regionIds.toList(),
    };
  }

  static SummaryCacheEntry fromJson(Map<String, dynamic> json) {
    return SummaryCacheEntry(
      marker: UnifiedMarker.fromJson(json['marker']),
      cachedAt: DateTime.parse(json['cached_at']),
      regionIds: Set<String>.from(json['region_ids']),
    );
  }
}

/// Level 3 缓存结构 - 详细缓存
class DetailCacheEntry {
  /// 详细数据（FishingSpot或FishingActivity的JSON）
  final Map<String, dynamic> detailData;

  /// 数据类型
  final MarkerType type;

  /// 缓存时间
  final DateTime cachedAt;

  const DetailCacheEntry({
    required this.detailData,
    required this.type,
    required this.cachedAt,
  });

  /// 检查是否过期
  bool isExpired(Duration maxAge) {
    return DateTime.now().difference(cachedAt) > maxAge;
  }

  Map<String, dynamic> toJson() {
    return {
      'detail_data': detailData,
      'type': type.value,
      'cached_at': cachedAt.toIso8601String(),
    };
  }

  static DetailCacheEntry fromJson(Map<String, dynamic> json) {
    return DetailCacheEntry(
      detailData: json['detail_data'],
      type: MarkerType.fromString(json['type']),
      cachedAt: DateTime.parse(json['cached_at']),
    );
  }
}

/// 区域索引结构
class RegionIndex {
  /// 区域ID到标记ID集合的映射
  final Map<String, Set<String>> _regionToMarkers = {};

  /// 标记ID到区域ID集合的映射
  final Map<String, Set<String>> _markerToRegions = {};

  /// 添加标记到区域
  void addMarkerToRegion(String markerId, String regionId) {
    _regionToMarkers.putIfAbsent(regionId, () => {}).add(markerId);
    _markerToRegions.putIfAbsent(markerId, () => {}).add(regionId);
  }

  /// 从区域移除标记
  void removeMarkerFromRegion(String markerId, String regionId) {
    _regionToMarkers[regionId]?.remove(markerId);
    _markerToRegions[markerId]?.remove(regionId);

    // 清理空集合
    if (_regionToMarkers[regionId]?.isEmpty == true) {
      _regionToMarkers.remove(regionId);
    }
    if (_markerToRegions[markerId]?.isEmpty == true) {
      _markerToRegions.remove(markerId);
    }
  }

  /// 获取区域内的所有标记ID
  Set<String> getMarkersInRegion(String regionId) {
    return Set<String>.from(_regionToMarkers[regionId] ?? {});
  }

  /// 获取标记所属的所有区域ID
  Set<String> getRegionsForMarker(String markerId) {
    return Set<String>.from(_markerToRegions[markerId] ?? {});
  }

  /// 移除区域及其所有关联
  void removeRegion(String regionId) {
    final markerIds = _regionToMarkers.remove(regionId) ?? {};
    for (final markerId in markerIds) {
      _markerToRegions[markerId]?.remove(regionId);
      if (_markerToRegions[markerId]?.isEmpty == true) {
        _markerToRegions.remove(markerId);
      }
    }
  }

  /// 移除标记及其所有关联
  void removeMarker(String markerId) {
    final regionIds = _markerToRegions.remove(markerId) ?? {};
    for (final regionId in regionIds) {
      _regionToMarkers[regionId]?.remove(markerId);
      if (_regionToMarkers[regionId]?.isEmpty == true) {
        _regionToMarkers.remove(regionId);
      }
    }
  }

  /// 获取所有区域ID
  Set<String> get allRegionIds => Set<String>.from(_regionToMarkers.keys);

  /// 获取所有标记ID
  Set<String> get allMarkerIds => Set<String>.from(_markerToRegions.keys);

  /// 清空索引
  void clear() {
    _regionToMarkers.clear();
    _markerToRegions.clear();
  }

  Map<String, dynamic> toJson() {
    return {
      'region_to_markers': _regionToMarkers.map(
        (key, value) => MapEntry(key, value.toList()),
      ),
      'marker_to_regions': _markerToRegions.map(
        (key, value) => MapEntry(key, value.toList()),
      ),
    };
  }

  static RegionIndex fromJson(Map<String, dynamic> json) {
    final index = RegionIndex();

    final regionToMarkers = json['region_to_markers'] as Map<String, dynamic>?;
    if (regionToMarkers != null) {
      for (final entry in regionToMarkers.entries) {
        final regionId = entry.key;
        final markerIds = Set<String>.from(entry.value as List);
        index._regionToMarkers[regionId] = markerIds;
      }
    }

    final markerToRegions = json['marker_to_regions'] as Map<String, dynamic>?;
    if (markerToRegions != null) {
      for (final entry in markerToRegions.entries) {
        final markerId = entry.key;
        final regionIds = Set<String>.from(entry.value as List);
        index._markerToRegions[markerId] = regionIds;
      }
    }

    return index;
  }
}
