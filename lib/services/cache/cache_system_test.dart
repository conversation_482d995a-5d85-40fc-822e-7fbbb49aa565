import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import 'cache_structures.dart';
import 'base_cache_operations.dart';
import 'region_index_manager.dart';
import 'cache_persistence.dart';
import 'map_region_tracker.dart';

import '../../models/marker_id_info.dart';
import '../../models/unified_marker.dart';

/// 缓存系统集成测试
///
/// 验证三级缓存的协同工作、区域索引的正确性和持久化的可靠性
class CacheSystemTest {
  /// 测试结果
  final List<String> _testResults = [];

  /// 是否所有测试都通过
  bool get allTestsPassed =>
      _testResults.every((result) => result.startsWith('✅'));

  /// 获取测试结果
  List<String> get testResults => List.unmodifiable(_testResults);

  /// 运行所有测试
  Future<void> runAllTests() async {
    debugPrint('🧪 [缓存测试] 开始运行缓存系统集成测试');
    _testResults.clear();

    await _testBasicCacheOperations();
    await _testRegionIndexManagement();
    await _testCachePersistence();
    await _testMapRegionTracking();
    await _testUnifiedCacheIntegration();

    debugPrint('🧪 [缓存测试] 测试完成，结果:');
    for (final result in _testResults) {
      debugPrint('🧪 $result');
    }

    if (allTestsPassed) {
      debugPrint('✅ [缓存测试] 所有测试通过！');
    } else {
      debugPrint('❌ [缓存测试] 部分测试失败！');
    }
  }

  // ==================== 基础缓存操作测试 ====================

  Future<void> _testBasicCacheOperations() async {
    try {
      debugPrint('🧪 [测试] 开始测试基础缓存操作');

      final cacheOps = BaseCacheOperations(
        maxIdCacheSize: 10,
        maxSummaryCacheSize: 5,
        maxDetailCacheSize: 3,
      );

      // 测试ID缓存
      final idInfo = MarkerIdInfo(
        id: 'test_spot_1',
        updated: DateTime.now(),
        type: MarkerType.spot,
      );

      final idEntry = IdCacheEntry(
        markerInfo: idInfo,
        cachedAt: DateTime.now(),
        regionIds: {'region_1'},
      );

      cacheOps.setIdCacheEntry('test_spot_1', idEntry);
      final retrievedIdEntry = cacheOps.getIdCacheEntry('test_spot_1');

      if (retrievedIdEntry?.markerInfo.id == 'test_spot_1') {
        _testResults.add('✅ ID缓存存储和检索正常');
      } else {
        _testResults.add('❌ ID缓存存储和检索失败');
      }

      // 测试摘要缓存
      final testMarker = SpotMarker(
        id: 'test_spot_1',
        name: '测试钓点',
        location: const LatLng(39.9, 116.4),
        userId: 'user_1',
        created: DateTime.now(),
        updated: DateTime.now(),
        status: 'active',
      );

      final summaryEntry = SummaryCacheEntry(
        marker: testMarker,
        cachedAt: DateTime.now(),
        regionIds: {'region_1'},
      );

      cacheOps.setSummaryCacheEntry('test_spot_1', summaryEntry);
      final retrievedSummaryEntry = cacheOps.getSummaryCacheEntry(
        'test_spot_1',
      );

      if (retrievedSummaryEntry?.marker.id == 'test_spot_1') {
        _testResults.add('✅ 摘要缓存存储和检索正常');
      } else {
        _testResults.add('❌ 摘要缓存存储和检索失败');
      }

      // 测试详细缓存
      final detailData = {'id': 'test_spot_1', 'description': '详细描述'};
      final detailEntry = DetailCacheEntry(
        detailData: detailData,
        type: MarkerType.spot,
        cachedAt: DateTime.now(),
      );

      cacheOps.setDetailCacheEntry('test_spot_1', detailEntry);
      final retrievedDetailEntry = cacheOps.getDetailCacheEntry('test_spot_1');

      if (retrievedDetailEntry?.detailData['id'] == 'test_spot_1') {
        _testResults.add('✅ 详细缓存存储和检索正常');
      } else {
        _testResults.add('❌ 详细缓存存储和检索失败');
      }

      // 测试LRU清理
      for (int i = 0; i < 15; i++) {
        final entry = IdCacheEntry(
          markerInfo: MarkerIdInfo(
            id: 'test_$i',
            updated: DateTime.now(),
            type: MarkerType.spot,
          ),
          cachedAt: DateTime.now(),
          regionIds: {},
        );
        cacheOps.setIdCacheEntry('test_$i', entry);
      }

      final stats = cacheOps.getCacheStats();
      if (stats['id_cache']['size'] <= 10) {
        _testResults.add('✅ LRU缓存清理正常');
      } else {
        _testResults.add('❌ LRU缓存清理失败');
      }
    } catch (e) {
      _testResults.add('❌ 基础缓存操作测试异常: $e');
    }
  }

  // ==================== 区域索引管理测试 ====================

  Future<void> _testRegionIndexManagement() async {
    try {
      debugPrint('🧪 [测试] 开始测试区域索引管理');

      final regionManager = RegionIndexManager(maxRegions: 5);

      // 创建测试区域
      final region1 = MapRegion.fromCenterRadius(
        center: const LatLng(39.9, 116.4),
        radiusKm: 5.0,
      );

      final region2 = MapRegion.fromCenterRadius(
        center: const LatLng(39.95, 116.45),
        radiusKm: 3.0,
      );

      regionManager.addRegion(region1);
      regionManager.addRegion(region2);

      // 测试区域添加和检索
      final retrievedRegion = regionManager.getRegion(region1.id);
      if (retrievedRegion?.id == region1.id) {
        _testResults.add('✅ 区域添加和检索正常');
      } else {
        _testResults.add('❌ 区域添加和检索失败');
      }

      // 测试标记与区域关联
      regionManager.addMarkerToRegion('marker_1', region1.id);
      regionManager.addMarkerToRegion('marker_2', region1.id);
      regionManager.addMarkerToRegion('marker_1', region2.id);

      final markersInRegion1 = regionManager.getMarkersInRegion(region1.id);
      final regionsForMarker1 = regionManager.getRegionsForMarker('marker_1');

      if (markersInRegion1.length == 2 && regionsForMarker1.length == 2) {
        _testResults.add('✅ 标记与区域关联正常');
      } else {
        _testResults.add('❌ 标记与区域关联失败');
      }

      // 测试重叠检测
      final overlappingRegions = regionManager.findOverlappingRegions(region1);
      if (overlappingRegions.isNotEmpty) {
        _testResults.add('✅ 区域重叠检测正常');
      } else {
        _testResults.add('❌ 区域重叠检测失败');
      }

      // 测试区域清理
      regionManager.removeRegion(region1.id);
      final removedRegion = regionManager.getRegion(region1.id);
      final markersAfterRemoval = regionManager.getMarkersInRegion(region1.id);

      if (removedRegion == null && markersAfterRemoval.isEmpty) {
        _testResults.add('✅ 区域清理正常');
      } else {
        _testResults.add('❌ 区域清理失败');
      }
    } catch (e) {
      _testResults.add('❌ 区域索引管理测试异常: $e');
    }
  }

  // ==================== 缓存持久化测试 ====================

  Future<void> _testCachePersistence() async {
    try {
      debugPrint('🧪 [测试] 开始测试缓存持久化');

      final cacheOps = BaseCacheOperations();
      final regionManager = RegionIndexManager();
      final persistence = CachePersistence();

      // 添加测试数据
      final testMarker = SpotMarker(
        id: 'persist_test_1',
        name: '持久化测试钓点',
        location: const LatLng(40.0, 116.5),
        userId: 'user_1',
        created: DateTime.now(),
        updated: DateTime.now(),
        status: 'active',
      );

      final summaryEntry = SummaryCacheEntry(
        marker: testMarker,
        cachedAt: DateTime.now(),
        regionIds: {'region_persist'},
      );

      cacheOps.setSummaryCacheEntry('persist_test_1', summaryEntry);

      final region = MapRegion.fromCenterRadius(
        center: const LatLng(40.0, 116.5),
        radiusKm: 2.0,
      );
      regionManager.addRegion(region);
      regionManager.addMarkerToRegion('persist_test_1', region.id);

      // 测试保存
      await persistence.saveAllCaches(
        cacheOps: cacheOps,
        regionManager: regionManager,
        force: true,
      );

      // 清空内存缓存
      cacheOps.clearAllCaches();
      regionManager.clear();

      // 测试加载
      final loadSuccess = await persistence.loadAllCaches(
        cacheOps: cacheOps,
        regionManager: regionManager,
      );

      if (loadSuccess) {
        final loadedEntry = cacheOps.getSummaryCacheEntry('persist_test_1');
        final loadedRegion = regionManager.getRegion(region.id);

        if (loadedEntry?.marker.id == 'persist_test_1' &&
            loadedRegion != null) {
          _testResults.add('✅ 缓存持久化正常');
        } else {
          _testResults.add('❌ 缓存持久化数据不完整');
        }
      } else {
        _testResults.add('❌ 缓存持久化加载失败');
      }

      // 清理测试数据
      await persistence.clearAllPersistedData();
    } catch (e) {
      _testResults.add('❌ 缓存持久化测试异常: $e');
    }
  }

  // ==================== 地图区域跟踪测试 ====================

  Future<void> _testMapRegionTracking() async {
    try {
      debugPrint('🧪 [测试] 开始测试地图区域跟踪');

      final regionTracker = MapRegionTracker();

      // 创建新区域
      final newRegions = [
        MapRegion.fromCenterRadius(
          center: const LatLng(39.95, 116.45),
          radiusKm: 4.0,
        ),
        MapRegion.fromCenterRadius(
          center: const LatLng(40.0, 116.5),
          radiusKm: 3.0,
        ),
      ];

      // 测试区域变化检测
      final changes = regionTracker.updateRegions(newRegions);

      if (changes.addedRegions.length == 2 && changes.hasChanges) {
        _testResults.add('✅ 区域变化检测正常');
      } else {
        _testResults.add('❌ 区域变化检测失败');
      }

      // 测试区域创建
      final viewportRegion = regionTracker.createRegionFromViewport(
        center: const LatLng(39.9, 116.4),
        zoomLevel: 12.0,
      );

      if (viewportRegion.center.latitude == 39.9) {
        _testResults.add('✅ 视口区域创建正常');
      } else {
        _testResults.add('❌ 视口区域创建失败');
      }

      // 测试预加载区域创建
      final preloadRegions = regionTracker.createRegionsForPreloading(
        center: const LatLng(39.9, 116.4),
        zoomLevel: 12.0,
        gridSize: 3,
      );

      if (preloadRegions.length == 9) {
        _testResults.add('✅ 预加载区域创建正常');
      } else {
        _testResults.add('❌ 预加载区域创建失败');
      }
    } catch (e) {
      _testResults.add('❌ 地图区域跟踪测试异常: $e');
    }
  }

  // ==================== 统一缓存集成测试 ====================

  Future<void> _testUnifiedCacheIntegration() async {
    try {
      debugPrint('🧪 [测试] 开始测试统一缓存集成');

      // 由于UnifiedMarkerCache需要真实的服务依赖，这里只测试基本的初始化
      // 在实际应用中，需要mock服务来进行完整测试

      final stats = <String, dynamic>{
        'cache': {'total_entries': 0},
        'regions': {'total_regions': 0},
        'tracking': {'current_regions': 0},
        'is_initialized': false,
      };

      if (stats.containsKey('cache') &&
          stats.containsKey('regions') &&
          stats.containsKey('tracking')) {
        _testResults.add('✅ 统一缓存统计结构正常');
      } else {
        _testResults.add('❌ 统一缓存统计结构异常');
      }

      // 测试边界计算
      final bounds = LatLngBounds(
        const LatLng(39.8, 116.3),
        const LatLng(40.0, 116.5),
      );

      final testPoint1 = const LatLng(39.9, 116.4); // 在边界内
      final testPoint2 = const LatLng(40.1, 116.6); // 在边界外

      if (bounds.contains(testPoint1) && !bounds.contains(testPoint2)) {
        _testResults.add('✅ 边界检测正常');
      } else {
        _testResults.add('❌ 边界检测失败');
      }
    } catch (e) {
      _testResults.add('❌ 统一缓存集成测试异常: $e');
    }
  }

  // ==================== 性能测试 ====================

  Future<void> runPerformanceTests() async {
    debugPrint('🚀 [性能测试] 开始运行性能测试');

    final cacheOps = BaseCacheOperations(maxIdCacheSize: 1000);
    final stopwatch = Stopwatch();

    // 测试大量数据的缓存性能
    stopwatch.start();
    for (int i = 0; i < 1000; i++) {
      final entry = IdCacheEntry(
        markerInfo: MarkerIdInfo(
          id: 'perf_test_$i',
          updated: DateTime.now(),
          type: MarkerType.spot,
        ),
        cachedAt: DateTime.now(),
        regionIds: {},
      );
      cacheOps.setIdCacheEntry('perf_test_$i', entry);
    }
    stopwatch.stop();

    final insertTime = stopwatch.elapsedMilliseconds;
    debugPrint('🚀 [性能测试] 1000个条目插入耗时: ${insertTime}ms');

    // 测试查询性能
    stopwatch.reset();
    stopwatch.start();
    for (int i = 0; i < 1000; i++) {
      cacheOps.getIdCacheEntry('perf_test_$i');
    }
    stopwatch.stop();

    final queryTime = stopwatch.elapsedMilliseconds;
    debugPrint('🚀 [性能测试] 1000个条目查询耗时: ${queryTime}ms');

    if (insertTime < 1000 && queryTime < 500) {
      _testResults.add('✅ 缓存性能测试通过');
    } else {
      _testResults.add('❌ 缓存性能测试失败');
    }
  }
}
