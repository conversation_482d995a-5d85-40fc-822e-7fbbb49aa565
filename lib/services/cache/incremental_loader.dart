import 'dart:async';
import 'package:flutter/foundation.dart';

import 'cache_structures.dart';
import 'map_region_tracker.dart';
import 'base_cache_operations.dart';
import 'region_index_manager.dart';
import '../../models/marker_id_info.dart';
import '../../models/unified_marker.dart';
import '../../services/fishing_spot_service_new.dart';
import '../../services/fishing_activity_service.dart';

/// 增量加载结果
class IncrementalLoadResult {
  /// 新加载的标记摘要
  final List<UnifiedMarker> newMarkers;

  /// 更新的标记摘要
  final List<UnifiedMarker> updatedMarkers;

  /// 从缓存获取的标记摘要
  final List<UnifiedMarker> cachedMarkers;

  /// 需要移除的标记ID
  final List<String> removedMarkerIds;

  /// 加载统计信息
  final Map<String, dynamic> stats;

  const IncrementalLoadResult({
    required this.newMarkers,
    required this.updatedMarkers,
    required this.cachedMarkers,
    required this.removedMarkerIds,
    required this.stats,
  });

  /// 获取所有有效标记
  List<UnifiedMarker> get allValidMarkers {
    final result = <UnifiedMarker>[];
    result.addAll(newMarkers);
    result.addAll(updatedMarkers);
    result.addAll(cachedMarkers);
    return result;
  }

  /// 是否有新数据
  bool get hasNewData => newMarkers.isNotEmpty || updatedMarkers.isNotEmpty;

  @override
  String toString() {
    return 'IncrementalLoadResult(new: ${newMarkers.length}, updated: ${updatedMarkers.length}, cached: ${cachedMarkers.length}, removed: ${removedMarkerIds.length})';
  }
}

/// 预加载策略
enum PreloadStrategy {
  /// 不预加载
  none,

  /// 预加载相邻区域
  adjacent,

  /// 基于用户行为预测
  predictive,

  /// 激进预加载（预加载更大范围）
  aggressive,
}

/// 增量加载器
///
/// 负责实现智能的增量数据加载策略
/// 支持新旧数据的合并、预加载和缓存优化
class IncrementalLoader {
  /// 缓存操作器
  final BaseCacheOperations _cacheOps;

  /// 区域索引管理器
  final RegionIndexManager _regionManager;

  /// 钓点服务
  final FishingSpotService _spotService;

  /// 活动服务
  final FishingActivityService _activityService;

  /// 预加载策略
  final PreloadStrategy preloadStrategy;

  /// 最大并发加载数
  final int maxConcurrentLoads;

  /// 加载超时时间
  final Duration loadTimeout;

  IncrementalLoader({
    required BaseCacheOperations cacheOps,
    required RegionIndexManager regionManager,
    required FishingSpotService spotService,
    required FishingActivityService activityService,
    this.preloadStrategy = PreloadStrategy.adjacent,
    this.maxConcurrentLoads = 3,
    this.loadTimeout = const Duration(seconds: 30),
  }) : _cacheOps = cacheOps,
       _regionManager = regionManager,
       _spotService = spotService,
       _activityService = activityService;

  // ==================== 增量加载主流程 ====================

  /// 执行增量加载
  Future<IncrementalLoadResult> performIncrementalLoad({
    required RegionChangeResult regionChanges,
  }) async {
    final startTime = DateTime.now();
    debugPrint('🔄 [增量加载] 开始增量加载: $regionChanges');

    try {
      // 第一步：获取需要加载数据的区域的ID信息
      final idLoadResults = await _loadMarkerIds(
        regionChanges.regionsNeedingData,
      );

      // 第二步：对比缓存，确定需要更新的标记
      final updateAnalysis = await _analyzeUpdatesNeeded(idLoadResults);

      // 第三步：批量加载需要更新的标记摘要
      final summaryLoadResults = await _loadMarkerSummaries(updateAnalysis);

      // 第四步：更新缓存和索引
      await _updateCacheAndIndex(summaryLoadResults, regionChanges);

      // 第五步：清理移除的区域
      await _cleanupRemovedRegions(regionChanges.removedRegions);

      // 第六步：预加载（如果启用）
      if (preloadStrategy != PreloadStrategy.none) {
        _performPreload(regionChanges);
      }

      final duration = DateTime.now().difference(startTime);
      final stats = _buildLoadStats(summaryLoadResults, duration);

      debugPrint(
        '✅ [增量加载] 加载完成: ${summaryLoadResults.toString()}, 耗时: ${duration.inMilliseconds}ms',
      );

      return summaryLoadResults.copyWith(stats: stats);
    } catch (e) {
      final duration = DateTime.now().difference(startTime);
      debugPrint('❌ [增量加载] 加载失败: $e, 耗时: ${duration.inMilliseconds}ms');

      // 返回空结果，但包含错误信息
      return IncrementalLoadResult(
        newMarkers: [],
        updatedMarkers: [],
        cachedMarkers: [],
        removedMarkerIds: [],
        stats: {
          'success': false,
          'error': e.toString(),
          'duration_ms': duration.inMilliseconds,
        },
      );
    }
  }

  // ==================== ID加载阶段 ====================

  /// 加载标记ID信息
  Future<Map<String, List<MarkerIdInfo>>> _loadMarkerIds(
    List<MapRegion> regions,
  ) async {
    if (regions.isEmpty) return {};

    debugPrint('🔍 [ID加载] 开始加载 ${regions.length} 个区域的标记ID');

    final results = <String, List<MarkerIdInfo>>{};
    final futures = <Future<void>>[];

    // 限制并发数量
    final semaphore = Semaphore(maxConcurrentLoads);

    for (final region in regions) {
      futures.add(
        semaphore.acquire().then((_) async {
          try {
            // TODO: 实现getRegionSpotIds方法
            // final spotIds = await _spotService.getRegionSpotIds(
            //   center: region.center,
            //   radiusKm: region.radiusKm,
            // );
            final spotIds = <MarkerIdInfo>[]; // 临时返回空列表

            final activityIds = await _activityService.getRegionActivityIds(
              center: region.center,
              radiusKm: region.radiusKm,
            );

            final allIds = <MarkerIdInfo>[];
            allIds.addAll(spotIds);
            allIds.addAll(activityIds);

            results[region.id] = allIds;
            debugPrint('🔍 [ID加载] 区域 ${region.id} 加载了 ${allIds.length} 个标记ID');
          } catch (e) {
            debugPrint('❌ [ID加载] 区域 ${region.id} 加载失败: $e');
            results[region.id] = [];
          } finally {
            semaphore.release();
          }
        }),
      );
    }

    await Future.wait(futures);

    final totalIds = results.values.fold(0, (sum, list) => sum + list.length);
    debugPrint('🔍 [ID加载] 完成，总共加载了 $totalIds 个标记ID');

    return results;
  }

  // ==================== 更新分析阶段 ====================

  /// 分析需要更新的标记
  Future<_UpdateAnalysis> _analyzeUpdatesNeeded(
    Map<String, List<MarkerIdInfo>> idLoadResults,
  ) async {
    debugPrint('🔍 [更新分析] 开始分析需要更新的标记');

    final newMarkerIds = <String>[];
    final updatedMarkerIds = <String>[];
    final cachedMarkers = <UnifiedMarker>[];

    for (final entry in idLoadResults.entries) {
      final regionId = entry.key;
      final markerIds = entry.value;

      for (final markerIdInfo in markerIds) {
        final markerId = markerIdInfo.id;

        // 检查ID缓存
        final idCacheEntry = _cacheOps.getIdCacheEntry(markerId);

        if (idCacheEntry == null) {
          // 全新的标记
          newMarkerIds.add(markerId);
        } else if (markerIdInfo.isNewerThan(idCacheEntry.markerInfo)) {
          // 需要更新的标记
          updatedMarkerIds.add(markerId);
        } else {
          // 检查摘要缓存
          final summaryCacheEntry = _cacheOps.getSummaryCacheEntry(markerId);
          if (summaryCacheEntry != null) {
            cachedMarkers.add(summaryCacheEntry.marker);
          } else {
            // ID缓存有但摘要缓存没有，需要加载
            newMarkerIds.add(markerId);
          }
        }

        // 更新区域关联
        _regionManager.addMarkerToRegion(markerId, regionId);
      }
    }

    debugPrint(
      '🔍 [更新分析] 分析完成: 新增=${newMarkerIds.length}, 更新=${updatedMarkerIds.length}, 缓存=${cachedMarkers.length}',
    );

    return _UpdateAnalysis(
      newMarkerIds: newMarkerIds,
      updatedMarkerIds: updatedMarkerIds,
      cachedMarkers: cachedMarkers,
    );
  }

  // ==================== 摘要加载阶段 ====================

  /// 加载标记摘要
  Future<IncrementalLoadResult> _loadMarkerSummaries(
    _UpdateAnalysis analysis,
  ) async {
    final allIdsToLoad = <String>[];
    allIdsToLoad.addAll(analysis.newMarkerIds);
    allIdsToLoad.addAll(analysis.updatedMarkerIds);

    if (allIdsToLoad.isEmpty) {
      return IncrementalLoadResult(
        newMarkers: [],
        updatedMarkers: [],
        cachedMarkers: analysis.cachedMarkers,
        removedMarkerIds: [],
        stats: {},
      );
    }

    debugPrint('🔍 [摘要加载] 开始加载 ${allIdsToLoad.length} 个标记摘要');

    // 按类型分组ID
    final spotIds = <String>[];
    final activityIds = <String>[];

    for (final markerId in allIdsToLoad) {
      // 从ID缓存或新加载的ID信息中确定类型
      final idCacheEntry = _cacheOps.getIdCacheEntry(markerId);
      if (idCacheEntry != null) {
        if (idCacheEntry.markerInfo.type == MarkerType.spot) {
          spotIds.add(markerId);
        } else {
          activityIds.add(markerId);
        }
      } else {
        // 如果ID缓存中没有，需要从最近的ID加载结果中推断
        // 这里简化处理，实际应该有更好的类型推断机制
        spotIds.add(markerId); // 默认当作钓点处理
      }
    }

    // 并行加载钓点和活动摘要
    final futures = <Future<List<UnifiedMarker>>>[];

    if (spotIds.isNotEmpty) {
      // TODO: 实现getSpotSummaries方法
      futures.add(
        // _spotService
        //     .getSpotSummaries(spotIds)
        //     .then((spots) => spots.cast<UnifiedMarker>()),
        Future.value(<UnifiedMarker>[]), // 临时返回空列表
      );
    }

    if (activityIds.isNotEmpty) {
      futures.add(
        _activityService
            .getActivitySummaries(activityIds)
            .then((activities) => activities.cast<UnifiedMarker>()),
      );
    }

    final results = await Future.wait(futures);
    final allLoadedMarkers = results.expand((list) => list).toList();

    // 分类新增和更新的标记
    final newMarkers = <UnifiedMarker>[];
    final updatedMarkers = <UnifiedMarker>[];

    for (final marker in allLoadedMarkers) {
      if (analysis.newMarkerIds.contains(marker.id)) {
        newMarkers.add(marker);
      } else {
        updatedMarkers.add(marker);
      }
    }

    debugPrint(
      '🔍 [摘要加载] 加载完成: 新增=${newMarkers.length}, 更新=${updatedMarkers.length}',
    );

    return IncrementalLoadResult(
      newMarkers: newMarkers,
      updatedMarkers: updatedMarkers,
      cachedMarkers: analysis.cachedMarkers,
      removedMarkerIds: [],
      stats: {},
    );
  }

  // ==================== 缓存更新阶段 ====================

  /// 更新缓存和索引
  Future<void> _updateCacheAndIndex(
    IncrementalLoadResult loadResult,
    RegionChangeResult regionChanges,
  ) async {
    debugPrint('💾 [缓存更新] 开始更新缓存和索引');

    final now = DateTime.now();

    // 更新新增标记的缓存
    for (final marker in loadResult.newMarkers) {
      // 更新ID缓存
      final idCacheEntry = IdCacheEntry(
        markerInfo: MarkerIdInfo(
          id: marker.id,
          updated: marker.updated,
          type: marker.type,
        ),
        cachedAt: now,
        regionIds: {},
      );
      _cacheOps.setIdCacheEntry(marker.id, idCacheEntry);

      // 更新摘要缓存
      final summaryCacheEntry = SummaryCacheEntry(
        marker: marker,
        cachedAt: now,
        regionIds: {},
      );
      _cacheOps.setSummaryCacheEntry(marker.id, summaryCacheEntry);
    }

    // 更新已更新标记的缓存
    for (final marker in loadResult.updatedMarkers) {
      // 更新ID缓存
      final idCacheEntry = IdCacheEntry(
        markerInfo: MarkerIdInfo(
          id: marker.id,
          updated: marker.updated,
          type: marker.type,
        ),
        cachedAt: now,
        regionIds: {},
      );
      _cacheOps.setIdCacheEntry(marker.id, idCacheEntry);

      // 更新摘要缓存
      final summaryCacheEntry = SummaryCacheEntry(
        marker: marker,
        cachedAt: now,
        regionIds: {},
      );
      _cacheOps.setSummaryCacheEntry(marker.id, summaryCacheEntry);
    }

    debugPrint('💾 [缓存更新] 缓存更新完成');
  }

  // ==================== 清理阶段 ====================

  /// 清理移除的区域
  Future<void> _cleanupRemovedRegions(List<MapRegion> removedRegions) async {
    if (removedRegions.isEmpty) return;

    debugPrint('🧹 [区域清理] 开始清理 ${removedRegions.length} 个移除的区域');

    for (final region in removedRegions) {
      _regionManager.removeRegion(region.id);
    }

    debugPrint('🧹 [区域清理] 清理完成');
  }

  // ==================== 预加载阶段 ====================

  /// 执行预加载
  void _performPreload(RegionChangeResult regionChanges) {
    // 预加载在后台异步执行，不阻塞主流程
    Future.microtask(() async {
      try {
        debugPrint('🔮 [预加载] 开始预加载，策略: $preloadStrategy');

        switch (preloadStrategy) {
          case PreloadStrategy.adjacent:
            await _preloadAdjacentRegions(regionChanges);
            break;
          case PreloadStrategy.predictive:
            await _preloadPredictiveRegions(regionChanges);
            break;
          case PreloadStrategy.aggressive:
            await _preloadAggressiveRegions(regionChanges);
            break;
          case PreloadStrategy.none:
            break;
        }
      } catch (e) {
        debugPrint('❌ [预加载] 预加载失败: $e');
      }
    });
  }

  /// 预加载相邻区域
  Future<void> _preloadAdjacentRegions(RegionChangeResult regionChanges) async {
    // 实现相邻区域预加载逻辑
    debugPrint('🔮 [预加载] 相邻区域预加载 - 待实现');
  }

  /// 预加载预测区域
  Future<void> _preloadPredictiveRegions(
    RegionChangeResult regionChanges,
  ) async {
    // 实现基于用户行为的预测性预加载
    debugPrint('🔮 [预加载] 预测性预加载 - 待实现');
  }

  /// 激进预加载
  Future<void> _preloadAggressiveRegions(
    RegionChangeResult regionChanges,
  ) async {
    // 实现激进的预加载策略
    debugPrint('🔮 [预加载] 激进预加载 - 待实现');
  }

  // ==================== 辅助方法 ====================

  /// 构建加载统计信息
  Map<String, dynamic> _buildLoadStats(
    IncrementalLoadResult result,
    Duration duration,
  ) {
    return {
      'success': true,
      'duration_ms': duration.inMilliseconds,
      'new_markers': result.newMarkers.length,
      'updated_markers': result.updatedMarkers.length,
      'cached_markers': result.cachedMarkers.length,
      'total_markers': result.allValidMarkers.length,
      'preload_strategy': preloadStrategy.toString(),
    };
  }
}

/// 更新分析结果（内部使用）
class _UpdateAnalysis {
  final List<String> newMarkerIds;
  final List<String> updatedMarkerIds;
  final List<UnifiedMarker> cachedMarkers;

  const _UpdateAnalysis({
    required this.newMarkerIds,
    required this.updatedMarkerIds,
    required this.cachedMarkers,
  });
}

/// 简单的信号量实现
class Semaphore {
  final int maxCount;
  int _currentCount;
  final List<Completer<void>> _waitQueue = [];

  Semaphore(this.maxCount) : _currentCount = maxCount;

  Future<void> acquire() async {
    if (_currentCount > 0) {
      _currentCount--;
      return;
    }

    final completer = Completer<void>();
    _waitQueue.add(completer);
    return completer.future;
  }

  void release() {
    if (_waitQueue.isNotEmpty) {
      final completer = _waitQueue.removeAt(0);
      completer.complete();
    } else {
      _currentCount++;
    }
  }
}

/// IncrementalLoadResult扩展方法
extension IncrementalLoadResultExtensions on IncrementalLoadResult {
  IncrementalLoadResult copyWith({
    List<UnifiedMarker>? newMarkers,
    List<UnifiedMarker>? updatedMarkers,
    List<UnifiedMarker>? cachedMarkers,
    List<String>? removedMarkerIds,
    Map<String, dynamic>? stats,
  }) {
    return IncrementalLoadResult(
      newMarkers: newMarkers ?? this.newMarkers,
      updatedMarkers: updatedMarkers ?? this.updatedMarkers,
      cachedMarkers: cachedMarkers ?? this.cachedMarkers,
      removedMarkerIds: removedMarkerIds ?? this.removedMarkerIds,
      stats: stats ?? this.stats,
    );
  }
}
