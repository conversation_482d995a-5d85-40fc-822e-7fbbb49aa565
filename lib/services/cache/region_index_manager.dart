import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import 'cache_structures.dart';

/// 区域索引管理器
///
/// 负责管理地图区域与标记之间的映射关系
/// 支持区域重叠检测和智能清理
class RegionIndexManager {
  /// 区域索引
  final RegionIndex _regionIndex = RegionIndex();

  /// 区域信息缓存
  final Map<String, MapRegion> _regions = {};

  /// 最大区域数量限制
  final int maxRegions;

  /// 区域过期时间
  final Duration regionExpiry;

  RegionIndexManager({
    this.maxRegions = 50,
    this.regionExpiry = const Duration(minutes: 30),
  });

  // ==================== 区域管理 ====================

  /// 添加区域
  void addRegion(MapRegion region) {
    debugPrint('🗺️ [区域管理] 添加区域: ${region.id}');

    // 检查是否需要清理旧区域
    if (_regions.length >= maxRegions) {
      _cleanupOldRegions();
    }

    _regions[region.id] = region;
    debugPrint('🗺️ [区域管理] 区域添加完成，当前区域数: ${_regions.length}');
  }

  /// 获取区域
  MapRegion? getRegion(String regionId) {
    final region = _regions[regionId];
    if (region == null) return null;

    // 检查是否过期
    if (_isRegionExpired(region)) {
      removeRegion(regionId);
      return null;
    }

    // 更新最后访问时间
    final updatedRegion = region.updateLastAccessed();
    _regions[regionId] = updatedRegion;

    return updatedRegion;
  }

  /// 移除区域
  void removeRegion(String regionId) {
    debugPrint('🗺️ [区域管理] 移除区域: $regionId');

    _regions.remove(regionId);
    _regionIndex.removeRegion(regionId);

    debugPrint('🗺️ [区域管理] 区域移除完成，当前区域数: ${_regions.length}');
  }

  /// 获取所有区域
  List<MapRegion> getAllRegions() {
    return _regions.values.toList();
  }

  /// 获取活跃区域（未过期的区域）
  List<MapRegion> getActiveRegions() {
    final activeRegions = <MapRegion>[];
    final expiredRegionIds = <String>[];

    for (final entry in _regions.entries) {
      if (_isRegionExpired(entry.value)) {
        expiredRegionIds.add(entry.key);
      } else {
        activeRegions.add(entry.value);
      }
    }

    // 清理过期区域
    for (final regionId in expiredRegionIds) {
      removeRegion(regionId);
    }

    return activeRegions;
  }

  // ==================== 标记与区域关联管理 ====================

  /// 添加标记到区域
  void addMarkerToRegion(String markerId, String regionId) {
    _regionIndex.addMarkerToRegion(markerId, regionId);
    debugPrint('🔗 [区域索引] 标记 $markerId 添加到区域 $regionId');
  }

  /// 从区域移除标记
  void removeMarkerFromRegion(String markerId, String regionId) {
    _regionIndex.removeMarkerFromRegion(markerId, regionId);
    debugPrint('🔗 [区域索引] 标记 $markerId 从区域 $regionId 移除');
  }

  /// 批量添加标记到区域
  void addMarkersToRegion(List<String> markerIds, String regionId) {
    for (final markerId in markerIds) {
      _regionIndex.addMarkerToRegion(markerId, regionId);
    }
    debugPrint('🔗 [区域索引] 批量添加 ${markerIds.length} 个标记到区域 $regionId');
  }

  /// 获取区域内的所有标记ID
  Set<String> getMarkersInRegion(String regionId) {
    return _regionIndex.getMarkersInRegion(regionId);
  }

  /// 获取标记所属的所有区域ID
  Set<String> getRegionsForMarker(String markerId) {
    return _regionIndex.getRegionsForMarker(markerId);
  }

  /// 移除标记的所有关联
  void removeMarker(String markerId) {
    _regionIndex.removeMarker(markerId);
    debugPrint('🔗 [区域索引] 标记 $markerId 的所有关联已移除');
  }

  // ==================== 区域查询和检测 ====================

  /// 查找包含指定点的区域
  List<MapRegion> findRegionsContaining(LatLng point) {
    final containingRegions = <MapRegion>[];

    for (final region in getActiveRegions()) {
      if (region.contains(point)) {
        containingRegions.add(region);
      }
    }

    return containingRegions;
  }

  /// 查找与指定区域重叠的区域
  List<MapRegion> findOverlappingRegions(MapRegion targetRegion) {
    final overlappingRegions = <MapRegion>[];

    for (final region in getActiveRegions()) {
      if (region.id != targetRegion.id && region.overlaps(targetRegion)) {
        overlappingRegions.add(region);
      }
    }

    return overlappingRegions;
  }

  /// 查找最适合的区域（基于中心点距离）
  MapRegion? findBestRegionForPoint(LatLng point) {
    MapRegion? bestRegion;
    double minDistance = double.infinity;

    for (final region in getActiveRegions()) {
      if (region.contains(point)) {
        final distance = _calculateDistance(point, region.center);
        if (distance < minDistance) {
          minDistance = distance;
          bestRegion = region;
        }
      }
    }

    return bestRegion;
  }

  /// 获取区域覆盖的标记
  ///
  /// 返回指定区域范围内的所有标记ID，包括重叠区域的标记
  Set<String> getMarkersCoveredByRegion(MapRegion region) {
    final coveredMarkers = <String>{};

    // 添加直接属于该区域的标记
    coveredMarkers.addAll(getMarkersInRegion(region.id));

    // 添加重叠区域的标记
    final overlappingRegions = findOverlappingRegions(region);
    for (final overlappingRegion in overlappingRegions) {
      final overlapRatio = region.overlapRatio(overlappingRegion);
      if (overlapRatio > 0.3) {
        // 重叠度超过30%才包含
        coveredMarkers.addAll(getMarkersInRegion(overlappingRegion.id));
      }
    }

    return coveredMarkers;
  }

  // ==================== 清理和维护 ====================

  /// 清理过期区域
  void cleanupExpiredRegions() {
    final expiredRegionIds = <String>[];

    for (final entry in _regions.entries) {
      if (_isRegionExpired(entry.value)) {
        expiredRegionIds.add(entry.key);
      }
    }

    for (final regionId in expiredRegionIds) {
      removeRegion(regionId);
    }

    if (expiredRegionIds.isNotEmpty) {
      debugPrint('🧹 [区域清理] 清理了 ${expiredRegionIds.length} 个过期区域');
    }
  }

  /// 清理旧区域（基于LRU策略）
  void _cleanupOldRegions() {
    if (_regions.length < maxRegions) return;

    // 按最后访问时间排序，移除最旧的区域
    final sortedRegions =
        _regions.entries.toList()..sort(
          (a, b) => a.value.lastAccessedAt.compareTo(b.value.lastAccessedAt),
        );

    final toRemove = sortedRegions.length - maxRegions + 10; // 多清理10个，避免频繁清理
    for (int i = 0; i < toRemove && i < sortedRegions.length; i++) {
      removeRegion(sortedRegions[i].key);
    }

    debugPrint('🧹 [区域清理] LRU清理了 $toRemove 个旧区域');
  }

  /// 检查区域是否过期
  bool _isRegionExpired(MapRegion region) {
    return DateTime.now().difference(region.lastAccessedAt) > regionExpiry;
  }

  /// 计算两点之间的距离（公里）
  double _calculateDistance(LatLng point1, LatLng point2) {
    const Distance distance = Distance();
    return distance.as(LengthUnit.Kilometer, point1, point2);
  }

  // ==================== 统计和调试 ====================

  /// 获取索引统计信息
  Map<String, dynamic> getIndexStats() {
    final activeRegions = getActiveRegions();
    int totalMarkers = 0;

    for (final regionId in _regionIndex.allRegionIds) {
      totalMarkers += getMarkersInRegion(regionId).length;
    }

    return {
      'total_regions': _regions.length,
      'active_regions': activeRegions.length,
      'total_markers': _regionIndex.allMarkerIds.length,
      'total_associations': totalMarkers,
      'max_regions': maxRegions,
      'region_expiry_minutes': regionExpiry.inMinutes,
    };
  }

  /// 打印索引统计信息
  void printIndexStats() {
    final stats = getIndexStats();
    debugPrint('📊 [区域索引统计] 总区域数: ${stats['total_regions']}');
    debugPrint('📊 [区域索引统计] 活跃区域数: ${stats['active_regions']}');
    debugPrint('📊 [区域索引统计] 总标记数: ${stats['total_markers']}');
    debugPrint('📊 [区域索引统计] 总关联数: ${stats['total_associations']}');
  }

  /// 清空所有数据
  void clear() {
    _regions.clear();
    _regionIndex.clear();
    debugPrint('🧹 [区域管理] 已清空所有区域和索引数据');
  }

  // ==================== 序列化支持 ====================

  /// 转换为JSON（用于持久化）
  Map<String, dynamic> toJson() {
    return {
      'regions': _regions.map(
        (key, value) => MapEntry(key, {
          'id': value.id,
          'center': {
            'lat': value.center.latitude,
            'lon': value.center.longitude,
          },
          'radius_km': value.radiusKm,
          'created_at': value.createdAt.toIso8601String(),
          'last_accessed_at': value.lastAccessedAt.toIso8601String(),
        }),
      ),
      'region_index': _regionIndex.toJson(),
    };
  }

  /// 从JSON恢复（用于持久化）
  void fromJson(Map<String, dynamic> json) {
    clear();

    // 恢复区域数据
    final regionsData = json['regions'] as Map<String, dynamic>?;
    if (regionsData != null) {
      for (final entry in regionsData.entries) {
        final regionData = entry.value as Map<String, dynamic>;
        final centerData = regionData['center'] as Map<String, dynamic>;

        final region = MapRegion.fromCenterRadius(
          center: LatLng(centerData['lat'], centerData['lon']),
          radiusKm: regionData['radius_km'],
          createdAt: DateTime.parse(regionData['created_at']),
          lastAccessedAt: DateTime.parse(regionData['last_accessed_at']),
        );

        _regions[entry.key] = region;
      }
    }

    // 恢复索引数据
    final indexData = json['region_index'] as Map<String, dynamic>?;
    if (indexData != null) {
      final restoredIndex = RegionIndex.fromJson(indexData);
      _regionIndex.clear();

      // 复制索引数据
      for (final regionId in restoredIndex.allRegionIds) {
        for (final markerId in restoredIndex.getMarkersInRegion(regionId)) {
          _regionIndex.addMarkerToRegion(markerId, regionId);
        }
      }
    }

    debugPrint('📥 [区域管理] 从JSON恢复了 ${_regions.length} 个区域');
  }
}
