import 'package:flutter/foundation.dart';
import 'cache_structures.dart';

/// LRU缓存节点
class _LRUNode<K, V> {
  K key;
  V value;
  _LRUNode<K, V>? prev;
  _LRUNode<K, V>? next;

  _LRUNode(this.key, this.value);
}

/// LRU缓存实现
class LRUCache<K, V> {
  final int capacity;
  final Map<K, _LRUNode<K, V>> _cache = {};
  _LRUNode<K, V>? _head;
  _LRUNode<K, V>? _tail;

  LRUCache(this.capacity) {
    // 创建虚拟头尾节点
    _head = _LRUNode<K, V>(null as K, null as V);
    _tail = _LRUNode<K, V>(null as K, null as V);
    _head!.next = _tail;
    _tail!.prev = _head;
  }

  /// 获取值
  V? get(K key) {
    final node = _cache[key];
    if (node == null) return null;

    // 移动到头部（最近使用）
    _moveToHead(node);
    return node.value;
  }

  /// 设置值
  void put(K key, V value) {
    final existingNode = _cache[key];

    if (existingNode != null) {
      // 更新现有节点
      existingNode.value = value;
      _moveToHead(existingNode);
    } else {
      // 创建新节点
      final newNode = _LRUNode(key, value);

      if (_cache.length >= capacity) {
        // 移除最少使用的节点
        final tail = _removeTail();
        if (tail != null) {
          _cache.remove(tail.key);
        }
      }

      _cache[key] = newNode;
      _addToHead(newNode);
    }
  }

  /// 移除值
  V? remove(K key) {
    final node = _cache.remove(key);
    if (node == null) return null;

    _removeNode(node);
    return node.value;
  }

  /// 检查是否包含键
  bool containsKey(K key) {
    return _cache.containsKey(key);
  }

  /// 获取所有键
  Iterable<K> get keys => _cache.keys;

  /// 获取所有值
  Iterable<V> get values => _cache.values.map((node) => node.value);

  /// 获取当前大小
  int get length => _cache.length;

  /// 是否为空
  bool get isEmpty => _cache.isEmpty;

  /// 清空缓存
  void clear() {
    _cache.clear();
    _head!.next = _tail;
    _tail!.prev = _head;
  }

  /// 移动节点到头部
  void _moveToHead(_LRUNode<K, V> node) {
    _removeNode(node);
    _addToHead(node);
  }

  /// 移除节点
  void _removeNode(_LRUNode<K, V> node) {
    node.prev!.next = node.next;
    node.next!.prev = node.prev;
  }

  /// 添加节点到头部
  void _addToHead(_LRUNode<K, V> node) {
    node.prev = _head;
    node.next = _head!.next;
    _head!.next!.prev = node;
    _head!.next = node;
  }

  /// 移除尾部节点
  _LRUNode<K, V>? _removeTail() {
    final lastNode = _tail!.prev;
    if (lastNode == _head) return null;

    _removeNode(lastNode!);
    return lastNode;
  }
}

/// 基础缓存操作类
class BaseCacheOperations {
  /// 默认缓存配置
  static const Duration defaultMaxAge = Duration(hours: 1);
  static const int defaultMaxSize = 1000;

  /// Level 1 缓存 - ID缓存
  final LRUCache<String, IdCacheEntry> _idCache;

  /// Level 2 缓存 - 摘要缓存
  final LRUCache<String, SummaryCacheEntry> _summaryCache;

  /// Level 3 缓存 - 详细缓存
  final LRUCache<String, DetailCacheEntry> _detailCache;

  /// 缓存配置
  final Duration maxAge;
  final int maxIdCacheSize;
  final int maxSummaryCacheSize;
  final int maxDetailCacheSize;

  BaseCacheOperations({
    this.maxAge = defaultMaxAge,
    this.maxIdCacheSize = defaultMaxSize,
    this.maxSummaryCacheSize = 500,
    this.maxDetailCacheSize = 200,
  }) : _idCache = LRUCache<String, IdCacheEntry>(maxIdCacheSize),
       _summaryCache = LRUCache<String, SummaryCacheEntry>(maxSummaryCacheSize),
       _detailCache = LRUCache<String, DetailCacheEntry>(maxDetailCacheSize);

  // ==================== Level 1 缓存操作 ====================

  /// 获取ID缓存条目
  IdCacheEntry? getIdCacheEntry(String markerId) {
    final entry = _idCache.get(markerId);
    if (entry == null) return null;

    // 检查是否过期
    if (entry.isExpired(maxAge)) {
      _idCache.remove(markerId);
      return null;
    }

    return entry;
  }

  /// 设置ID缓存条目
  void setIdCacheEntry(String markerId, IdCacheEntry entry) {
    _idCache.put(markerId, entry);
  }

  /// 移除ID缓存条目
  IdCacheEntry? removeIdCacheEntry(String markerId) {
    return _idCache.remove(markerId);
  }

  /// 获取所有ID缓存条目
  Map<String, IdCacheEntry> getAllIdCacheEntries() {
    final result = <String, IdCacheEntry>{};

    for (final key in _idCache.keys.toList()) {
      final entry = _idCache.get(key);
      if (entry != null && !entry.isExpired(maxAge)) {
        result[key] = entry;
      } else if (entry != null) {
        // 移除过期条目
        _idCache.remove(key);
      }
    }

    return result;
  }

  // ==================== Level 2 缓存操作 ====================

  /// 获取摘要缓存条目
  SummaryCacheEntry? getSummaryCacheEntry(String markerId) {
    final entry = _summaryCache.get(markerId);
    if (entry == null) return null;

    // 检查是否过期
    if (entry.isExpired(maxAge)) {
      _summaryCache.remove(markerId);
      return null;
    }

    return entry;
  }

  /// 设置摘要缓存条目
  void setSummaryCacheEntry(String markerId, SummaryCacheEntry entry) {
    _summaryCache.put(markerId, entry);
  }

  /// 移除摘要缓存条目
  SummaryCacheEntry? removeSummaryCacheEntry(String markerId) {
    return _summaryCache.remove(markerId);
  }

  /// 获取所有摘要缓存条目
  Map<String, SummaryCacheEntry> getAllSummaryCacheEntries() {
    final result = <String, SummaryCacheEntry>{};

    for (final key in _summaryCache.keys.toList()) {
      final entry = _summaryCache.get(key);
      if (entry != null && !entry.isExpired(maxAge)) {
        result[key] = entry;
      } else if (entry != null) {
        // 移除过期条目
        _summaryCache.remove(key);
      }
    }

    return result;
  }

  // ==================== Level 3 缓存操作 ====================

  /// 获取详细缓存条目
  DetailCacheEntry? getDetailCacheEntry(String markerId) {
    final entry = _detailCache.get(markerId);
    if (entry == null) return null;

    // 检查是否过期
    if (entry.isExpired(maxAge)) {
      _detailCache.remove(markerId);
      return null;
    }

    return entry;
  }

  /// 设置详细缓存条目
  void setDetailCacheEntry(String markerId, DetailCacheEntry entry) {
    _detailCache.put(markerId, entry);
  }

  /// 移除详细缓存条目
  DetailCacheEntry? removeDetailCacheEntry(String markerId) {
    return _detailCache.remove(markerId);
  }

  /// 获取所有详细缓存条目
  Map<String, DetailCacheEntry> getAllDetailCacheEntries() {
    final result = <String, DetailCacheEntry>{};

    for (final key in _detailCache.keys.toList()) {
      final entry = _detailCache.get(key);
      if (entry != null && !entry.isExpired(maxAge)) {
        result[key] = entry;
      } else if (entry != null) {
        // 移除过期条目
        _detailCache.remove(key);
      }
    }

    return result;
  }

  // ==================== 通用操作 ====================

  /// 移除标记的所有缓存
  void removeMarkerFromAllCaches(String markerId) {
    _idCache.remove(markerId);
    _summaryCache.remove(markerId);
    _detailCache.remove(markerId);
  }

  /// 清理过期缓存
  void cleanupExpiredEntries() {
    debugPrint('🧹 [缓存清理] 开始清理过期缓存条目');

    int cleanedCount = 0;

    // 清理ID缓存
    for (final key in _idCache.keys.toList()) {
      final entry = _idCache.get(key);
      if (entry != null && entry.isExpired(maxAge)) {
        _idCache.remove(key);
        cleanedCount++;
      }
    }

    // 清理摘要缓存
    for (final key in _summaryCache.keys.toList()) {
      final entry = _summaryCache.get(key);
      if (entry != null && entry.isExpired(maxAge)) {
        _summaryCache.remove(key);
        cleanedCount++;
      }
    }

    // 清理详细缓存
    for (final key in _detailCache.keys.toList()) {
      final entry = _detailCache.get(key);
      if (entry != null && entry.isExpired(maxAge)) {
        _detailCache.remove(key);
        cleanedCount++;
      }
    }

    debugPrint('🧹 [缓存清理] 清理完成，移除了 $cleanedCount 个过期条目');
  }

  /// 清空所有缓存
  void clearAllCaches() {
    _idCache.clear();
    _summaryCache.clear();
    _detailCache.clear();
    debugPrint('🧹 [缓存清理] 已清空所有缓存');
  }

  /// 获取缓存统计信息
  Map<String, dynamic> getCacheStats() {
    return {
      'id_cache': {
        'size': _idCache.length,
        'capacity': maxIdCacheSize,
        'usage_ratio': _idCache.length / maxIdCacheSize,
      },
      'summary_cache': {
        'size': _summaryCache.length,
        'capacity': maxSummaryCacheSize,
        'usage_ratio': _summaryCache.length / maxSummaryCacheSize,
      },
      'detail_cache': {
        'size': _detailCache.length,
        'capacity': maxDetailCacheSize,
        'usage_ratio': _detailCache.length / maxDetailCacheSize,
      },
      'total_entries':
          _idCache.length + _summaryCache.length + _detailCache.length,
      'max_age_hours': maxAge.inHours,
    };
  }

  /// 打印缓存统计信息
  void printCacheStats() {
    final stats = getCacheStats();
    debugPrint(
      '📊 [缓存统计] ID缓存: ${stats['id_cache']['size']}/${stats['id_cache']['capacity']}',
    );
    debugPrint(
      '📊 [缓存统计] 摘要缓存: ${stats['summary_cache']['size']}/${stats['summary_cache']['capacity']}',
    );
    debugPrint(
      '📊 [缓存统计] 详细缓存: ${stats['detail_cache']['size']}/${stats['detail_cache']['capacity']}',
    );
    debugPrint('📊 [缓存统计] 总条目数: ${stats['total_entries']}');
  }
}
