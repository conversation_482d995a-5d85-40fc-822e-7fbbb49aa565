import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import 'cache_structures.dart';
import 'base_cache_operations.dart';
import 'region_index_manager.dart';
import 'cache_persistence.dart';
import 'map_region_tracker.dart';
import 'incremental_loader.dart';
import '../../models/marker_id_info.dart';
import '../../models/unified_marker.dart';
import '../../services/fishing_spot_service_new.dart';
import '../../services/fishing_activity_service.dart';

/// 统一标记缓存管理器
///
/// 集成所有缓存组件，提供统一的缓存管理接口
/// 支持三级缓存、区域管理、增量更新和持久化
class UnifiedMarkerCache {
  /// 基础缓存操作器
  final BaseCacheOperations _cacheOps;

  /// 区域索引管理器
  final RegionIndexManager _regionManager;

  /// 缓存持久化管理器
  final CachePersistence _persistence;

  /// 地图区域跟踪器
  final MapRegionTracker _regionTracker;

  /// 增量加载器
  final IncrementalLoader _incrementalLoader;

  /// 自动保存定时器
  Timer? _autoSaveTimer;

  /// 自动清理定时器
  Timer? _autoCleanupTimer;

  /// 是否已初始化
  bool _isInitialized = false;

  UnifiedMarkerCache({
    BaseCacheOperations? cacheOps,
    RegionIndexManager? regionManager,
    CachePersistence? persistence,
    MapRegionTracker? regionTracker,
    required FishingSpotService spotService,
    required FishingActivityService activityService,
  }) : _cacheOps = cacheOps ?? BaseCacheOperations(),
       _regionManager = regionManager ?? RegionIndexManager(),
       _persistence = persistence ?? CachePersistence(),
       _regionTracker = regionTracker ?? MapRegionTracker(),
       _incrementalLoader = IncrementalLoader(
         cacheOps: cacheOps ?? BaseCacheOperations(),
         regionManager: regionManager ?? RegionIndexManager(),
         spotService: spotService,
         activityService: activityService,
       );

  // ==================== 初始化和清理 ====================

  /// 初始化缓存系统
  Future<void> initialize() async {
    if (_isInitialized) return;

    debugPrint('🚀 [统一缓存] 开始初始化缓存系统');

    try {
      // 加载持久化数据
      final loadSuccess = await _persistence.loadAllCaches(
        cacheOps: _cacheOps,
        regionManager: _regionManager,
      );

      if (loadSuccess) {
        debugPrint('📥 [统一缓存] 持久化数据加载成功');
      } else {
        debugPrint('⚠️ [统一缓存] 持久化数据加载失败或不存在');
      }

      // 启动自动保存定时器（每5分钟保存一次）
      _autoSaveTimer = Timer.periodic(const Duration(minutes: 5), (_) {
        _autoSave();
      });

      // 启动自动清理定时器（每30分钟清理一次）
      _autoCleanupTimer = Timer.periodic(const Duration(minutes: 30), (_) {
        _autoCleanup();
      });

      _isInitialized = true;
      debugPrint('✅ [统一缓存] 缓存系统初始化完成');
    } catch (e) {
      debugPrint('❌ [统一缓存] 初始化失败: $e');
      rethrow;
    }
  }

  /// 清理资源
  Future<void> dispose() async {
    debugPrint('🧹 [统一缓存] 开始清理缓存系统');

    // 取消定时器
    _autoSaveTimer?.cancel();
    _autoCleanupTimer?.cancel();

    // 最后一次保存
    await _persistence.saveAllCaches(
      cacheOps: _cacheOps,
      regionManager: _regionManager,
      force: true,
    );

    _isInitialized = false;
    debugPrint('✅ [统一缓存] 缓存系统清理完成');
  }

  // ==================== 核心缓存接口 ====================

  /// 获取过期的标记ID列表
  ///
  /// 对比服务器返回的ID和更新时间，确定哪些标记需要更新
  /// [serverMarkerIds] 服务器返回的标记ID信息
  Future<List<String>> getOutdatedMarkerIds(
    List<MarkerIdInfo> serverMarkerIds,
  ) async {
    _ensureInitialized();

    debugPrint('🔍 [过期检查] 开始检查 ${serverMarkerIds.length} 个标记的过期状态');

    final outdatedIds = <String>[];

    for (final serverInfo in serverMarkerIds) {
      final cachedEntry = _cacheOps.getIdCacheEntry(serverInfo.id);

      if (cachedEntry == null) {
        // 缓存中没有，需要加载
        outdatedIds.add(serverInfo.id);
      } else if (serverInfo.isNewerThan(cachedEntry.markerInfo)) {
        // 服务器版本更新，需要更新
        outdatedIds.add(serverInfo.id);
      }
      // 否则缓存是最新的，不需要更新
    }

    debugPrint('🔍 [过期检查] 检查完成，发现 ${outdatedIds.length} 个过期标记');
    return outdatedIds;
  }

  /// 获取区域内的缓存标记
  ///
  /// [bounds] 地理边界
  /// [includeExpired] 是否包含过期的缓存条目
  List<UnifiedMarker> getCachedMarkersInRegion(
    LatLngBounds bounds, {
    bool includeExpired = false,
  }) {
    _ensureInitialized();

    debugPrint('🗺️ [区域查询] 开始查询区域内的缓存标记');

    final cachedMarkers = <UnifiedMarker>[];

    // 查找包含指定边界的区域
    final containingRegions = _regionManager.findRegionsContaining(
      LatLng(
        (bounds.south + bounds.north) / 2,
        (bounds.west + bounds.east) / 2,
      ),
    );

    final markerIds = <String>{};
    for (final region in containingRegions) {
      markerIds.addAll(_regionManager.getMarkersInRegion(region.id));
    }

    // 获取标记的摘要缓存
    for (final markerId in markerIds) {
      final cacheEntry = _cacheOps.getSummaryCacheEntry(markerId);
      if (cacheEntry != null) {
        if (includeExpired || !cacheEntry.isExpired(_cacheOps.maxAge)) {
          // 检查标记是否在指定边界内
          if (bounds.contains(cacheEntry.marker.location)) {
            cachedMarkers.add(cacheEntry.marker);
          }
        }
      }
    }

    debugPrint('🗺️ [区域查询] 查询完成，找到 ${cachedMarkers.length} 个缓存标记');
    return cachedMarkers;
  }

  /// 清理超出边界的缓存
  ///
  /// [currentBounds] 当前需要保留的边界
  /// [bufferRatio] 缓冲区比例，超出此比例的缓存将被清理
  Future<void> cleanupOutOfBoundsCache(
    LatLngBounds currentBounds, {
    double bufferRatio = 2.0,
  }) async {
    _ensureInitialized();

    debugPrint('🧹 [边界清理] 开始清理超出边界的缓存');

    // 计算扩展边界（包含缓冲区）
    final latRange = currentBounds.north - currentBounds.south;
    final lngRange = currentBounds.east - currentBounds.west;
    final latBuffer = latRange * (bufferRatio - 1) / 2;
    final lngBuffer = lngRange * (bufferRatio - 1) / 2;

    final expandedBounds = LatLngBounds(
      LatLng(currentBounds.south - latBuffer, currentBounds.west - lngBuffer),
      LatLng(currentBounds.north + latBuffer, currentBounds.east + lngBuffer),
    );

    // 获取所有摘要缓存条目
    final allSummaryEntries = _cacheOps.getAllSummaryCacheEntries();
    final toRemove = <String>[];

    for (final entry in allSummaryEntries.entries) {
      final marker = entry.value.marker;
      if (!expandedBounds.contains(marker.location)) {
        toRemove.add(entry.key);
      }
    }

    // 移除超出边界的缓存
    for (final markerId in toRemove) {
      _cacheOps.removeMarkerFromAllCaches(markerId);
      _regionManager.removeMarker(markerId);
    }

    debugPrint('🧹 [边界清理] 清理完成，移除了 ${toRemove.length} 个超出边界的缓存');
  }

  // ==================== 增量更新接口 ====================

  /// 执行增量更新
  ///
  /// [newRegions] 新的地图区域列表
  Future<IncrementalLoadResult> performIncrementalUpdate(
    List<MapRegion> newRegions,
  ) async {
    _ensureInitialized();

    debugPrint('🔄 [增量更新] 开始执行增量更新');

    // 使用区域跟踪器计算变化
    final regionChanges = _regionTracker.updateRegions(newRegions);

    // 使用增量加载器执行加载
    final loadResult = await _incrementalLoader.performIncrementalLoad(
      regionChanges: regionChanges,
    );

    // 标记需要保存
    _persistence.markPendingChanges();

    debugPrint('🔄 [增量更新] 增量更新完成: $loadResult');
    return loadResult;
  }

  // ==================== 详细数据管理 ====================

  /// 获取标记详细数据
  ///
  /// [markerId] 标记ID
  /// [type] 标记类型
  Future<Map<String, dynamic>?> getMarkerDetails(
    String markerId,
    MarkerType type,
  ) async {
    _ensureInitialized();

    // 首先检查详细缓存
    final detailEntry = _cacheOps.getDetailCacheEntry(markerId);
    if (detailEntry != null && !detailEntry.isExpired(_cacheOps.maxAge)) {
      debugPrint('💾 [详细数据] 从缓存获取标记详情: $markerId');
      return detailEntry.detailData;
    }

    // 缓存中没有或已过期，需要从服务器加载
    debugPrint('🌐 [详细数据] 从服务器加载标记详情: $markerId');

    try {
      Map<String, dynamic>? detailData;

      switch (type) {
        case MarkerType.spot:
          // TODO: 调用钓点服务获取详细数据
          // final spot = await _spotService.getSpotById(markerId);
          // detailData = spot?.toJson();
          // 临时返回空数据，避免编译警告
          detailData = null;
          break;
        case MarkerType.activity:
          // TODO: 调用活动服务获取详细数据
          // final activity = await _activityService.getActivityById(markerId);
          // detailData = activity?.toJson();
          // 临时返回空数据，避免编译警告
          detailData = null;
          break;
      }

      if (detailData != null) {
        // 缓存详细数据
        final detailCacheEntry = DetailCacheEntry(
          detailData: detailData,
          type: type,
          cachedAt: DateTime.now(),
        );
        _cacheOps.setDetailCacheEntry(markerId, detailCacheEntry);

        // 标记需要保存
        _persistence.markPendingChanges();
      }

      return detailData;
    } catch (e) {
      debugPrint('❌ [详细数据] 加载标记详情失败: $markerId, 错误: $e');
      return null;
    }
  }

  /// 预缓存标记详细数据
  ///
  /// [markerIds] 要预缓存的标记ID列表
  Future<void> precacheMarkerDetails(List<String> markerIds) async {
    _ensureInitialized();

    if (markerIds.isEmpty) return;

    debugPrint('🔮 [预缓存] 开始预缓存 ${markerIds.length} 个标记的详细数据');

    // 在后台异步预缓存，不阻塞主流程
    Future.microtask(() async {
      for (final markerId in markerIds) {
        try {
          // 检查是否已经缓存
          final detailEntry = _cacheOps.getDetailCacheEntry(markerId);
          if (detailEntry != null && !detailEntry.isExpired(_cacheOps.maxAge)) {
            continue; // 已经缓存且未过期
          }

          // 从摘要缓存获取类型信息
          final summaryEntry = _cacheOps.getSummaryCacheEntry(markerId);
          if (summaryEntry != null) {
            await getMarkerDetails(markerId, summaryEntry.marker.type);
          }
        } catch (e) {
          debugPrint('⚠️ [预缓存] 预缓存标记详情失败: $markerId, 错误: $e');
        }
      }

      debugPrint('✅ [预缓存] 预缓存完成');
    });
  }

  // ==================== 缓存统计和管理 ====================

  /// 获取缓存统计信息
  Map<String, dynamic> getCacheStats() {
    final cacheStats = _cacheOps.getCacheStats();
    final regionStats = _regionManager.getIndexStats();
    final trackingStats = _regionTracker.getTrackingStats();

    return {
      'cache': cacheStats,
      'regions': regionStats,
      'tracking': trackingStats,
      'is_initialized': _isInitialized,
    };
  }

  /// 打印缓存统计信息
  void printCacheStats() {
    final stats = getCacheStats();
    debugPrint('📊 [缓存统计] ========== 统一缓存统计 ==========');
    _cacheOps.printCacheStats();
    _regionManager.printIndexStats();
    debugPrint('📊 [缓存统计] 初始化状态: ${stats['is_initialized']}');
    debugPrint('📊 [缓存统计] =====================================');
  }

  /// 清空所有缓存
  Future<void> clearAllCaches() async {
    debugPrint('🧹 [缓存清理] 开始清空所有缓存');

    _cacheOps.clearAllCaches();
    _regionManager.clear();
    _regionTracker.clear();

    await _persistence.clearAllPersistedData();

    debugPrint('🧹 [缓存清理] 所有缓存已清空');
  }

  // ==================== 私有方法 ====================

  /// 确保已初始化
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('UnifiedMarkerCache 尚未初始化，请先调用 initialize()');
    }
  }

  /// 自动保存
  void _autoSave() {
    Future.microtask(() async {
      try {
        await _persistence.saveAllCaches(
          cacheOps: _cacheOps,
          regionManager: _regionManager,
        );
      } catch (e) {
        debugPrint('❌ [自动保存] 自动保存失败: $e');
      }
    });
  }

  /// 自动清理
  void _autoCleanup() {
    Future.microtask(() async {
      try {
        _cacheOps.cleanupExpiredEntries();
        _regionManager.cleanupExpiredRegions();
      } catch (e) {
        debugPrint('❌ [自动清理] 自动清理失败: $e');
      }
    });
  }
}
