import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'cache_structures.dart';
import 'base_cache_operations.dart';
import 'region_index_manager.dart';

/// 缓存持久化管理器
/// 
/// 负责将缓存数据持久化到SharedPreferences
/// 支持增量保存和恢复，避免大量数据的频繁序列化
class CachePersistence {
  /// SharedPreferences键名
  static const String _idCacheKey = 'unified_marker_id_cache';
  static const String _summaryCacheKey = 'unified_marker_summary_cache';
  static const String _detailCacheKey = 'unified_marker_detail_cache';
  static const String _regionIndexKey = 'unified_marker_region_index';
  static const String _metadataKey = 'unified_marker_cache_metadata';

  /// 缓存版本号，用于兼容性检查
  static const int _cacheVersion = 1;

  /// 最大单次保存的条目数，避免SharedPreferences存储过大数据
  static const int _maxBatchSize = 100;

  /// 保存间隔，避免频繁写入
  static const Duration _saveInterval = Duration(seconds: 30);

  /// 上次保存时间
  DateTime? _lastSaveTime;

  /// 是否有待保存的更改
  bool _hasPendingChanges = false;

  // ==================== 保存操作 ====================

  /// 保存所有缓存数据
  Future<void> saveAllCaches({
    required BaseCacheOperations cacheOps,
    required RegionIndexManager regionManager,
    bool force = false,
  }) async {
    // 检查是否需要保存
    if (!force && !_shouldSave()) {
      return;
    }

    try {
      debugPrint('💾 [缓存持久化] 开始保存所有缓存数据');
      final startTime = DateTime.now();

      final prefs = await SharedPreferences.getInstance();

      // 并行保存各种缓存
      await Future.wait([
        _saveIdCache(prefs, cacheOps),
        _saveSummaryCache(prefs, cacheOps),
        _saveDetailCache(prefs, cacheOps),
        _saveRegionIndex(prefs, regionManager),
        _saveMetadata(prefs),
      ]);

      _lastSaveTime = DateTime.now();
      _hasPendingChanges = false;

      final duration = DateTime.now().difference(startTime);
      debugPrint('💾 [缓存持久化] 保存完成，耗时: ${duration.inMilliseconds}ms');
    } catch (e) {
      debugPrint('❌ [缓存持久化] 保存失败: $e');
      rethrow;
    }
  }

  /// 保存ID缓存
  Future<void> _saveIdCache(
    SharedPreferences prefs,
    BaseCacheOperations cacheOps,
  ) async {
    try {
      final entries = cacheOps.getAllIdCacheEntries();
      final jsonData = <String, dynamic>{};

      // 分批处理，避免单次序列化过大数据
      final entryList = entries.entries.toList();
      for (int i = 0; i < entryList.length; i += _maxBatchSize) {
        final batch = entryList.skip(i).take(_maxBatchSize);
        for (final entry in batch) {
          jsonData[entry.key] = entry.value.toJson();
        }
      }

      final jsonString = jsonEncode(jsonData);
      await prefs.setString(_idCacheKey, jsonString);
      
      debugPrint('💾 [ID缓存] 保存了 ${entries.length} 个条目');
    } catch (e) {
      debugPrint('❌ [ID缓存] 保存失败: $e');
    }
  }

  /// 保存摘要缓存
  Future<void> _saveSummaryCache(
    SharedPreferences prefs,
    BaseCacheOperations cacheOps,
  ) async {
    try {
      final entries = cacheOps.getAllSummaryCacheEntries();
      final jsonData = <String, dynamic>{};

      // 分批处理
      final entryList = entries.entries.toList();
      for (int i = 0; i < entryList.length; i += _maxBatchSize) {
        final batch = entryList.skip(i).take(_maxBatchSize);
        for (final entry in batch) {
          jsonData[entry.key] = entry.value.toJson();
        }
      }

      final jsonString = jsonEncode(jsonData);
      await prefs.setString(_summaryCacheKey, jsonString);
      
      debugPrint('💾 [摘要缓存] 保存了 ${entries.length} 个条目');
    } catch (e) {
      debugPrint('❌ [摘要缓存] 保存失败: $e');
    }
  }

  /// 保存详细缓存
  Future<void> _saveDetailCache(
    SharedPreferences prefs,
    BaseCacheOperations cacheOps,
  ) async {
    try {
      final entries = cacheOps.getAllDetailCacheEntries();
      final jsonData = <String, dynamic>{};

      // 详细缓存数据较大，更小的批次处理
      final smallBatchSize = _maxBatchSize ~/ 2;
      final entryList = entries.entries.toList();
      
      for (int i = 0; i < entryList.length; i += smallBatchSize) {
        final batch = entryList.skip(i).take(smallBatchSize);
        for (final entry in batch) {
          jsonData[entry.key] = entry.value.toJson();
        }
      }

      final jsonString = jsonEncode(jsonData);
      await prefs.setString(_detailCacheKey, jsonString);
      
      debugPrint('💾 [详细缓存] 保存了 ${entries.length} 个条目');
    } catch (e) {
      debugPrint('❌ [详细缓存] 保存失败: $e');
    }
  }

  /// 保存区域索引
  Future<void> _saveRegionIndex(
    SharedPreferences prefs,
    RegionIndexManager regionManager,
  ) async {
    try {
      final jsonData = regionManager.toJson();
      final jsonString = jsonEncode(jsonData);
      await prefs.setString(_regionIndexKey, jsonString);
      
      final stats = regionManager.getIndexStats();
      debugPrint('💾 [区域索引] 保存了 ${stats['total_regions']} 个区域');
    } catch (e) {
      debugPrint('❌ [区域索引] 保存失败: $e');
    }
  }

  /// 保存元数据
  Future<void> _saveMetadata(SharedPreferences prefs) async {
    try {
      final metadata = {
        'version': _cacheVersion,
        'saved_at': DateTime.now().toIso8601String(),
        'app_version': '1.0.0', // TODO: 从package info获取
      };

      final jsonString = jsonEncode(metadata);
      await prefs.setString(_metadataKey, jsonString);
    } catch (e) {
      debugPrint('❌ [元数据] 保存失败: $e');
    }
  }

  // ==================== 加载操作 ====================

  /// 加载所有缓存数据
  Future<bool> loadAllCaches({
    required BaseCacheOperations cacheOps,
    required RegionIndexManager regionManager,
  }) async {
    try {
      debugPrint('📥 [缓存持久化] 开始加载所有缓存数据');
      final startTime = DateTime.now();

      final prefs = await SharedPreferences.getInstance();

      // 首先检查元数据和版本兼容性
      if (!await _checkCompatibility(prefs)) {
        debugPrint('⚠️ [缓存持久化] 版本不兼容，跳过加载');
        return false;
      }

      // 并行加载各种缓存
      final results = await Future.wait([
        _loadIdCache(prefs, cacheOps),
        _loadSummaryCache(prefs, cacheOps),
        _loadDetailCache(prefs, cacheOps),
        _loadRegionIndex(prefs, regionManager),
      ]);

      final allSuccess = results.every((result) => result);
      final duration = DateTime.now().difference(startTime);
      
      if (allSuccess) {
        debugPrint('📥 [缓存持久化] 加载完成，耗时: ${duration.inMilliseconds}ms');
      } else {
        debugPrint('⚠️ [缓存持久化] 部分加载失败，耗时: ${duration.inMilliseconds}ms');
      }

      return allSuccess;
    } catch (e) {
      debugPrint('❌ [缓存持久化] 加载失败: $e');
      return false;
    }
  }

  /// 检查版本兼容性
  Future<bool> _checkCompatibility(SharedPreferences prefs) async {
    try {
      final metadataString = prefs.getString(_metadataKey);
      if (metadataString == null) {
        debugPrint('📥 [兼容性检查] 没有找到元数据，可能是首次运行');
        return true; // 首次运行，允许加载
      }

      final metadata = jsonDecode(metadataString) as Map<String, dynamic>;
      final savedVersion = metadata['version'] as int?;

      if (savedVersion == null || savedVersion != _cacheVersion) {
        debugPrint('📥 [兼容性检查] 版本不匹配: 保存版本=$savedVersion, 当前版本=$_cacheVersion');
        return false;
      }

      debugPrint('📥 [兼容性检查] 版本兼容: $savedVersion');
      return true;
    } catch (e) {
      debugPrint('❌ [兼容性检查] 检查失败: $e');
      return false;
    }
  }

  /// 加载ID缓存
  Future<bool> _loadIdCache(
    SharedPreferences prefs,
    BaseCacheOperations cacheOps,
  ) async {
    try {
      final jsonString = prefs.getString(_idCacheKey);
      if (jsonString == null) {
        debugPrint('📥 [ID缓存] 没有找到缓存数据');
        return true;
      }

      final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;
      int loadedCount = 0;

      for (final entry in jsonData.entries) {
        try {
          final cacheEntry = IdCacheEntry.fromJson(entry.value);
          cacheOps.setIdCacheEntry(entry.key, cacheEntry);
          loadedCount++;
        } catch (e) {
          debugPrint('⚠️ [ID缓存] 加载条目失败: ${entry.key}, 错误: $e');
        }
      }

      debugPrint('📥 [ID缓存] 加载了 $loadedCount 个条目');
      return true;
    } catch (e) {
      debugPrint('❌ [ID缓存] 加载失败: $e');
      return false;
    }
  }

  /// 加载摘要缓存
  Future<bool> _loadSummaryCache(
    SharedPreferences prefs,
    BaseCacheOperations cacheOps,
  ) async {
    try {
      final jsonString = prefs.getString(_summaryCacheKey);
      if (jsonString == null) {
        debugPrint('📥 [摘要缓存] 没有找到缓存数据');
        return true;
      }

      final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;
      int loadedCount = 0;

      for (final entry in jsonData.entries) {
        try {
          final cacheEntry = SummaryCacheEntry.fromJson(entry.value);
          cacheOps.setSummaryCacheEntry(entry.key, cacheEntry);
          loadedCount++;
        } catch (e) {
          debugPrint('⚠️ [摘要缓存] 加载条目失败: ${entry.key}, 错误: $e');
        }
      }

      debugPrint('📥 [摘要缓存] 加载了 $loadedCount 个条目');
      return true;
    } catch (e) {
      debugPrint('❌ [摘要缓存] 加载失败: $e');
      return false;
    }
  }

  /// 加载详细缓存
  Future<bool> _loadDetailCache(
    SharedPreferences prefs,
    BaseCacheOperations cacheOps,
  ) async {
    try {
      final jsonString = prefs.getString(_detailCacheKey);
      if (jsonString == null) {
        debugPrint('📥 [详细缓存] 没有找到缓存数据');
        return true;
      }

      final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;
      int loadedCount = 0;

      for (final entry in jsonData.entries) {
        try {
          final cacheEntry = DetailCacheEntry.fromJson(entry.value);
          cacheOps.setDetailCacheEntry(entry.key, cacheEntry);
          loadedCount++;
        } catch (e) {
          debugPrint('⚠️ [详细缓存] 加载条目失败: ${entry.key}, 错误: $e');
        }
      }

      debugPrint('📥 [详细缓存] 加载了 $loadedCount 个条目');
      return true;
    } catch (e) {
      debugPrint('❌ [详细缓存] 加载失败: $e');
      return false;
    }
  }

  /// 加载区域索引
  Future<bool> _loadRegionIndex(
    SharedPreferences prefs,
    RegionIndexManager regionManager,
  ) async {
    try {
      final jsonString = prefs.getString(_regionIndexKey);
      if (jsonString == null) {
        debugPrint('📥 [区域索引] 没有找到缓存数据');
        return true;
      }

      final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;
      regionManager.fromJson(jsonData);

      final stats = regionManager.getIndexStats();
      debugPrint('📥 [区域索引] 加载了 ${stats['total_regions']} 个区域');
      return true;
    } catch (e) {
      debugPrint('❌ [区域索引] 加载失败: $e');
      return false;
    }
  }

  // ==================== 清理操作 ====================

  /// 清除所有持久化数据
  Future<void> clearAllPersistedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await Future.wait([
        prefs.remove(_idCacheKey),
        prefs.remove(_summaryCacheKey),
        prefs.remove(_detailCacheKey),
        prefs.remove(_regionIndexKey),
        prefs.remove(_metadataKey),
      ]);

      debugPrint('🧹 [缓存持久化] 已清除所有持久化数据');
    } catch (e) {
      debugPrint('❌ [缓存持久化] 清除失败: $e');
    }
  }

  // ==================== 辅助方法 ====================

  /// 标记有待保存的更改
  void markPendingChanges() {
    _hasPendingChanges = true;
  }

  /// 检查是否应该保存
  bool _shouldSave() {
    if (!_hasPendingChanges) return false;
    
    if (_lastSaveTime == null) return true;
    
    return DateTime.now().difference(_lastSaveTime!) > _saveInterval;
  }

  /// 获取持久化统计信息
  Future<Map<String, dynamic>> getPersistenceStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final stats = <String, dynamic>{
        'has_id_cache': prefs.containsKey(_idCacheKey),
        'has_summary_cache': prefs.containsKey(_summaryCacheKey),
        'has_detail_cache': prefs.containsKey(_detailCacheKey),
        'has_region_index': prefs.containsKey(_regionIndexKey),
        'has_metadata': prefs.containsKey(_metadataKey),
        'last_save_time': _lastSaveTime?.toIso8601String(),
        'has_pending_changes': _hasPendingChanges,
      };

      // 获取数据大小估算
      final idCacheSize = prefs.getString(_idCacheKey)?.length ?? 0;
      final summaryCacheSize = prefs.getString(_summaryCacheKey)?.length ?? 0;
      final detailCacheSize = prefs.getString(_detailCacheKey)?.length ?? 0;
      final regionIndexSize = prefs.getString(_regionIndexKey)?.length ?? 0;

      stats['estimated_sizes'] = {
        'id_cache_bytes': idCacheSize,
        'summary_cache_bytes': summaryCacheSize,
        'detail_cache_bytes': detailCacheSize,
        'region_index_bytes': regionIndexSize,
        'total_bytes': idCacheSize + summaryCacheSize + detailCacheSize + regionIndexSize,
      };

      return stats;
    } catch (e) {
      debugPrint('❌ [持久化统计] 获取失败: $e');
      return {};
    }
  }
}
