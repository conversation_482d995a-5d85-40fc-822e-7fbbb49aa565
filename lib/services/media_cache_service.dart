import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import '../models/spot_media.dart';
import 'unified_image_service.dart';

/// 媒体缓存服务
///
/// 负责管理钓点媒体的缓存和预加载
/// 支持边下载边播放和智能预加载策略
class MediaCacheService {
  static MediaCacheService? _instance;
  static MediaCacheService get instance => _instance ??= MediaCacheService._();

  MediaCacheService._();

  /// 统一图片服务（用于获取预签名URL）
  final UnifiedImageService _imageService = UnifiedImageService();

  /// 媒体文件缓存管理器
  static final CacheManager _mediaCacheManager = CacheManager(
    Config(
      'spot_media_cache',
      stalePeriod: const Duration(days: 7), // 7天过期
      maxNrOfCacheObjects: 200, // 最多缓存200个文件
      repo: JsonCacheInfoRepository(databaseName: 'spot_media_cache'),
      fileService: HttpFileService(),
    ),
  );

  /// 缩略图缓存管理器
  static final CacheManager _thumbnailCacheManager = CacheManager(
    Config(
      'spot_thumbnail_cache',
      stalePeriod: const Duration(days: 14), // 缩略图保存更久
      maxNrOfCacheObjects: 500, // 缩略图更多
      repo: JsonCacheInfoRepository(databaseName: 'spot_thumbnail_cache'),
      fileService: HttpFileService(),
    ),
  );

  /// 预加载媒体项目
  ///
  /// [mediaItem] 要预加载的媒体项目
  /// [priority] 优先级，true为高优先级（当前显示的媒体）
  Future<void> preloadMedia(
    SpotMediaItem mediaItem, {
    bool priority = false,
  }) async {
    try {
      debugPrint('🔄 [媒体缓存] 开始预加载媒体: ${mediaItem.filename} (优先级: $priority)');

      // 设置加载状态
      mediaItem.setLoadingState(loading: true, progress: 0.0);

      // 获取预签名URL
      await _getSignedUrls(mediaItem);

      // 预加载缩略图（如果有）
      if (mediaItem.thumbnailUrl != null && !mediaItem.hasThumbnailCache) {
        _preloadThumbnail(mediaItem);
      }

      // 预加载主文件
      if (!mediaItem.isCached) {
        await _preloadMainFile(mediaItem, priority: priority);
      }

      debugPrint('✅ [媒体缓存] 媒体预加载完成: ${mediaItem.filename}');
    } catch (e) {
      debugPrint('❌ [媒体缓存] 媒体预加载失败: ${mediaItem.filename}, 错误: $e');
      mediaItem.setLoadingState(
        loading: false,
        error: true,
        errorMsg: e.toString(),
      );
    }
  }

  /// 获取媒体项目的预签名URL
  Future<void> _getSignedUrls(SpotMediaItem mediaItem) async {
    try {
      // 获取主文件的预签名URL
      final signedUrl = await _imageService.getSignedUrl(mediaItem.url);
      mediaItem.signedUrl = signedUrl;
      debugPrint('✅ [预签名URL] 主文件签名URL获取成功: ${mediaItem.filename}');

      // 获取缩略图的预签名URL（如果有）
      if (mediaItem.thumbnailUrl != null) {
        final signedThumbnailUrl = await _imageService.getSignedUrl(
          mediaItem.thumbnailUrl!,
        );
        mediaItem.signedThumbnailUrl = signedThumbnailUrl;
        debugPrint('✅ [预签名URL] 缩略图签名URL获取成功: ${mediaItem.filename}');
      }
    } catch (e) {
      debugPrint('❌ [预签名URL] 签名URL获取失败: ${mediaItem.filename}, 错误: $e');
      // 如果获取签名URL失败，使用原始URL作为后备
      mediaItem.signedUrl = mediaItem.url;
      mediaItem.signedThumbnailUrl = mediaItem.thumbnailUrl;
      rethrow;
    }
  }

  /// 预加载缩略图
  Future<void> _preloadThumbnail(SpotMediaItem mediaItem) async {
    if (mediaItem.thumbnailUrl == null) return;

    try {
      // 使用签名URL进行下载
      final downloadUrl = mediaItem.downloadThumbnailUrl;
      final file = await _thumbnailCacheManager.getSingleFile(downloadUrl);
      mediaItem.setCachedThumbnail(file);
      debugPrint('✅ [缩略图缓存] 缩略图缓存完成: ${mediaItem.filename}');
    } catch (e) {
      debugPrint('❌ [缩略图缓存] 缩略图缓存失败: ${mediaItem.filename}, 错误: $e');
    }
  }

  /// 预加载主文件
  Future<void> _preloadMainFile(
    SpotMediaItem mediaItem, {
    bool priority = false,
  }) async {
    try {
      // 对于视频，使用流式下载以支持边下载边播放
      if (mediaItem.isVideo) {
        await _preloadVideoFile(mediaItem, priority: priority);
      } else {
        await _preloadImageFile(mediaItem);
      }
    } catch (e) {
      debugPrint('❌ [主文件缓存] 主文件缓存失败: ${mediaItem.filename}, 错误: $e');
      rethrow;
    }
  }

  /// 预加载视频文件
  Future<void> _preloadVideoFile(
    SpotMediaItem mediaItem, {
    bool priority = false,
  }) async {
    try {
      // 使用签名URL和getFileStream支持边下载边播放
      final downloadUrl = mediaItem.downloadUrl;
      final fileStream = _mediaCacheManager.getFileStream(
        downloadUrl,
        withProgress: true,
      );

      await for (final result in fileStream) {
        if (result is DownloadProgress) {
          // 更新下载进度
          final progress =
              result.totalSize != null
                  ? result.downloaded / result.totalSize!
                  : 0.0;
          mediaItem.setLoadingState(progress: progress);

          debugPrint(
            '📥 [视频下载] ${mediaItem.filename} 进度: ${(progress * 100).toStringAsFixed(1)}%',
          );
        } else if (result is FileInfo) {
          // 下载完成
          mediaItem.setCachedFile(result.file);
          debugPrint('✅ [视频缓存] 视频缓存完成: ${mediaItem.filename}');
          break;
        }
      }
    } catch (e) {
      debugPrint('❌ [视频缓存] 视频缓存失败: ${mediaItem.filename}, 错误: $e');
      rethrow;
    }
  }

  /// 预加载图片文件
  Future<void> _preloadImageFile(SpotMediaItem mediaItem) async {
    try {
      // 使用签名URL进行下载
      final downloadUrl = mediaItem.downloadUrl;
      final file = await _mediaCacheManager.getSingleFile(downloadUrl);
      mediaItem.setCachedFile(file);
      debugPrint('✅ [图片缓存] 图片缓存完成: ${mediaItem.filename}');
    } catch (e) {
      debugPrint('❌ [图片缓存] 图片缓存失败: ${mediaItem.filename}, 错误: $e');
      rethrow;
    }
  }

  /// 获取缓存的媒体文件
  ///
  /// 如果文件已缓存，立即返回
  /// 如果文件未缓存，开始下载并返回流
  Future<File?> getCachedFile(SpotMediaItem mediaItem) async {
    try {
      // 先获取签名URL
      if (mediaItem.signedUrl == null) {
        await _getSignedUrls(mediaItem);
      }

      // 检查是否已有缓存（使用签名URL检查）
      final downloadUrl = mediaItem.downloadUrl;
      final fileInfo = await _mediaCacheManager.getFileFromCache(downloadUrl);
      if (fileInfo != null) {
        mediaItem.setCachedFile(fileInfo.file);
        return fileInfo.file;
      }

      // 没有缓存，开始下载
      await preloadMedia(mediaItem, priority: true);
      return mediaItem.cachedFile;
    } catch (e) {
      debugPrint('❌ [获取缓存] 获取缓存文件失败: ${mediaItem.filename}, 错误: $e');
      return null;
    }
  }

  /// 获取缓存的缩略图文件
  Future<File?> getCachedThumbnail(SpotMediaItem mediaItem) async {
    if (mediaItem.thumbnailUrl == null) return null;

    try {
      // 先获取签名URL
      if (mediaItem.signedThumbnailUrl == null) {
        await _getSignedUrls(mediaItem);
      }

      // 检查是否已有缓存（使用签名URL检查）
      final downloadUrl = mediaItem.downloadThumbnailUrl;
      final fileInfo = await _thumbnailCacheManager.getFileFromCache(
        downloadUrl,
      );
      if (fileInfo != null) {
        mediaItem.setCachedThumbnail(fileInfo.file);
        return fileInfo.file;
      }

      // 没有缓存，开始下载缩略图
      await _preloadThumbnail(mediaItem);
      return mediaItem.cachedThumbnailFile;
    } catch (e) {
      debugPrint('❌ [获取缩略图] 获取缩略图失败: ${mediaItem.filename}, 错误: $e');
      return null;
    }
  }

  /// 智能预加载策略
  ///
  /// 预加载当前媒体和前后各1个媒体
  /// [mediaItems] 媒体列表
  /// [currentIndex] 当前显示的媒体索引
  Future<void> smartPreload(
    List<SpotMediaItem> mediaItems,
    int currentIndex,
  ) async {
    if (mediaItems.isEmpty) return;

    debugPrint('🧠 [智能预加载] 开始智能预加载，当前索引: $currentIndex');

    // 预加载范围：当前 + 前后各1个
    final preloadIndices = <int>[];

    // 当前媒体（高优先级）
    preloadIndices.add(currentIndex);

    // 前一个媒体
    if (currentIndex > 0) {
      preloadIndices.add(currentIndex - 1);
    }

    // 后一个媒体
    if (currentIndex < mediaItems.length - 1) {
      preloadIndices.add(currentIndex + 1);
    }

    // 并行预加载
    final futures = preloadIndices.map((index) {
      final mediaItem = mediaItems[index];
      final priority = index == currentIndex; // 当前媒体为高优先级
      return preloadMedia(mediaItem, priority: priority);
    });

    await Future.wait(futures);
    debugPrint('✅ [智能预加载] 智能预加载完成');
  }

  /// 清理缓存
  Future<void> clearCache() async {
    try {
      await _mediaCacheManager.emptyCache();
      await _thumbnailCacheManager.emptyCache();
      debugPrint('✅ [缓存清理] 缓存清理完成');
    } catch (e) {
      debugPrint('❌ [缓存清理] 缓存清理失败: $e');
    }
  }

  /// 获取缓存大小
  Future<int> getCacheSize() async {
    try {
      // 这里简化处理，实际可以遍历缓存目录计算大小
      return 0;
    } catch (e) {
      debugPrint('❌ [缓存大小] 获取缓存大小失败: $e');
      return 0;
    }
  }
}
