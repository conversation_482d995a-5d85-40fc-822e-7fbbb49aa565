import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import '../models/unified_marker.dart';
import '../models/marker_id_info.dart';
import '../config/filter_config.dart';
import 'cache/unified_marker_cache.dart';
import 'cache/map_region_tracker.dart';
import 'cache/cache_structures.dart';
import 'marker_filter_service.dart';
import 'marker_priority_calculator.dart';
import 'fishing_spot_service_new.dart';
import 'fishing_activity_service.dart';
import 'debounce_throttle_manager.dart';

/// 标记加载结果
class MarkerLoadResult {
  /// 加载的标记列表
  final List<UnifiedMarker> markers;

  /// 加载统计信息
  final Map<String, dynamic> stats;

  /// 是否成功
  final bool success;

  /// 错误信息（如果失败）
  final String? error;

  const MarkerLoadResult({
    required this.markers,
    required this.stats,
    required this.success,
    this.error,
  });

  /// 创建成功结果
  factory MarkerLoadResult.success({
    required List<UnifiedMarker> markers,
    required Map<String, dynamic> stats,
  }) {
    return MarkerLoadResult(markers: markers, stats: stats, success: true);
  }

  /// 创建失败结果
  factory MarkerLoadResult.failure({
    required String error,
    Map<String, dynamic>? stats,
  }) {
    return MarkerLoadResult(
      markers: [],
      stats: stats ?? {},
      success: false,
      error: error,
    );
  }

  @override
  String toString() {
    return 'MarkerLoadResult(success: $success, markers: ${markers.length}, error: $error)';
  }
}

/// 标记详情结果
class MarkerDetailsResult {
  /// 详情数据
  final dynamic details;

  /// 是否成功
  final bool success;

  /// 是否来自缓存
  final bool fromCache;

  /// 错误信息（如果失败）
  final String? error;

  /// 统计信息
  final Map<String, dynamic> stats;

  const MarkerDetailsResult({
    this.details,
    required this.success,
    this.fromCache = false,
    this.error,
    required this.stats,
  });

  /// 创建成功结果
  factory MarkerDetailsResult.success({
    required dynamic details,
    required bool fromCache,
    required Map<String, dynamic> stats,
  }) {
    return MarkerDetailsResult(
      details: details,
      success: true,
      fromCache: fromCache,
      stats: stats,
    );
  }

  /// 创建失败结果
  factory MarkerDetailsResult.failure({
    required String error,
    required Map<String, dynamic> stats,
  }) {
    return MarkerDetailsResult(success: false, error: error, stats: stats);
  }

  @override
  String toString() {
    return 'MarkerDetailsResult(success: $success, fromCache: $fromCache, error: $error)';
  }
}

/// 统一标记管理器
///
/// 集成缓存管理器、过滤服务、排序计算器
/// 提供统一的标记管理接口，支持三步加载和增量更新
class UnifiedMarkerManager {
  /// 统一标记缓存
  final UnifiedMarkerCache _cache;

  /// 地图区域跟踪器
  final MapRegionTracker _regionTracker;

  /// 标记过滤服务
  final MarkerFilterService _filterService;

  /// 标记优先级计算器
  final MarkerPriorityCalculator _priorityCalculator;

  /// 钓点服务
  final FishingSpotService _spotService;

  /// 活动服务
  final FishingActivityService _activityService;

  /// 防抖节流管理器
  final DebounceThrottleManager _debounceThrottleManager;

  /// 当前过滤配置
  FilterConfig _currentConfig = FilterConfig.defaultConfig();

  /// 当前用户位置
  LatLng? _currentLocation;

  /// 当前用户ID
  String? _currentUserId;

  /// 是否已初始化
  bool _isInitialized = false;

  /// 加载状态流控制器
  final StreamController<bool> _loadingStateController =
      StreamController<bool>.broadcast();

  /// 标记更新流控制器
  final StreamController<List<UnifiedMarker>> _markersController =
      StreamController<List<UnifiedMarker>>.broadcast();

  // ==================== 内存管理字段 ====================

  /// 内存使用监控定时器
  Timer? _memoryMonitorTimer;

  /// 缓存清理定时器
  Timer? _cacheCleanupTimer;

  /// 最大内存使用量（MB）
  static const int _maxMemoryUsageMB = 100;

  /// 内存压力阈值（MB）
  static const int _memoryPressureThresholdMB = 80;

  /// 缓存清理间隔
  static const Duration _cacheCleanupInterval = Duration(minutes: 5);

  /// 内存监控间隔
  static const Duration _memoryMonitorInterval = Duration(seconds: 30);

  /// 上次内存清理时间
  DateTime? _lastMemoryCleanup;

  /// 内存使用统计
  final Map<String, int> _memoryStats = {
    'cache_size': 0,
    'marker_count': 0,
    'region_count': 0,
  };

  UnifiedMarkerManager({
    required UnifiedMarkerCache cache,
    required FishingSpotService spotService,
    required FishingActivityService activityService,
    MapRegionTracker? regionTracker,
    MarkerFilterService? filterService,
    MarkerPriorityCalculator? priorityCalculator,
    DebounceThrottleManager? debounceThrottleManager,
  }) : _cache = cache,
       _spotService = spotService,
       _activityService = activityService,
       _regionTracker = regionTracker ?? MapRegionTracker(),
       _filterService = filterService ?? MarkerFilterService(),
       _priorityCalculator = priorityCalculator ?? MarkerPriorityCalculator(),
       _debounceThrottleManager =
           debounceThrottleManager ?? DebounceThrottleManager();

  // ==================== 流和状态 ====================

  /// 加载状态流
  Stream<bool> get loadingStateStream => _loadingStateController.stream;

  /// 标记更新流
  Stream<List<UnifiedMarker>> get markersStream => _markersController.stream;

  /// 当前是否正在加载
  bool get isLoading => _loadingStateController.hasListener;

  /// 当前过滤配置
  FilterConfig get currentConfig => _currentConfig;

  /// 当前用户位置
  LatLng? get currentLocation => _currentLocation;

  /// 当前用户ID
  String? get currentUserId => _currentUserId;

  // ==================== 初始化和配置 ====================

  /// 初始化管理器
  Future<void> initialize() async {
    if (_isInitialized) return;

    debugPrint('🚀 [统一管理器] 开始初始化');

    try {
      // 初始化缓存系统
      await _cache.initialize();

      // 设置服务的用户信息
      _filterService.setCurrentUserId(_currentUserId);
      _filterService.setCurrentLocation(_currentLocation);
      _priorityCalculator.setCurrentLocation(_currentLocation);

      // 启动内存监控
      _startMemoryMonitoring();

      _isInitialized = true;
      debugPrint('✅ [统一管理器] 初始化完成');
    } catch (e) {
      debugPrint('❌ [marker统一管理器] 初始化失败: $e');
      rethrow;
    }
  }

  /// 设置当前用户信息
  void setCurrentUser({String? userId, LatLng? location}) {
    bool needsUpdate = false;

    if (_currentUserId != userId) {
      _currentUserId = userId;
      _filterService.setCurrentUserId(userId);
      needsUpdate = true;
    }

    if (_currentLocation != location) {
      _currentLocation = location;
      _filterService.setCurrentLocation(location);
      _priorityCalculator.setCurrentLocation(location);
      needsUpdate = true;
    }

    if (needsUpdate) {
      debugPrint('👤 [统一管理器] 用户信息已更新: userId=$userId, location=$location');
    }
  }

  /// 更新过滤配置
  void updateFilterConfig(FilterConfig config) {
    if (_currentConfig != config) {
      debugPrint('⚙️ [统一管理器] 配置更新前: ${_currentConfig.getSummary()}');
      debugPrint('⚙️ [统一管理器] 配置更新后: ${config.getSummary()}');
      debugPrint(
        '⚙️ [统一管理器] 启用类型变化: ${_currentConfig.enabledTypes.map((t) => t.value).toList()} -> ${config.enabledTypes.map((t) => t.value).toList()}',
      );
      _currentConfig = config;
      debugPrint('⚙️ [统一管理器] 过滤配置已更新完成');
    } else {
      debugPrint('⚙️ [统一管理器] 配置无变化，跳过更新');
    }
  }

  // ==================== 核心加载接口 ====================

  /// 为指定区域加载标记（三步加载主流程）
  Future<MarkerLoadResult> loadMarkersForRegion({
    required LatLng center,
    required double radiusKm,
    FilterConfig? config,
    bool forceReload = false,
    Function(String)? onProgressUpdate,
  }) async {
    _ensureInitialized();

    final useConfig = config ?? _currentConfig;
    final startTime = DateTime.now();

    debugPrint('🔄 [三步加载] 开始三步加载流程: center=$center, radius=${radiusKm}km');

    try {
      _loadingStateController.add(true);
      onProgressUpdate?.call('开始加载...');

      // 创建地图区域
      final region = MapRegion.fromCenterRadius(
        center: center,
        radiusKm: radiusKm,
      );

      // === 第一步：获取区域内标记ID ===
      onProgressUpdate?.call('第1步：获取标记ID...');
      final step1Result = await _performStep1LoadIds(region, forceReload);

      if (!step1Result.success) {
        return MarkerLoadResult.failure(
          error: step1Result.error ?? '第一步加载失败',
          stats: step1Result.stats,
        );
      }

      final allMarkerIds = step1Result.markerIds;
      debugPrint('🔄 [三步加载] 第1步完成: 获取到 ${allMarkerIds.length} 个标记ID');

      // === 第二步：对比缓存，获取需要更新的标记摘要 ===
      onProgressUpdate?.call('第2步：检查缓存状态...');
      final step2Result = await _performStep2CheckCache(
        allMarkerIds,
        forceReload,
      );

      debugPrint(
        '🔄 [三步加载] 第2步完成: 需要加载 ${step2Result.outdatedIds.length} 个标记摘要',
      );

      // === 第三步：批量加载标记摘要 ===
      onProgressUpdate?.call('第3步：加载标记摘要...');
      final step3Result = await _performStep3LoadSummaries(step2Result);

      if (!step3Result.success) {
        return MarkerLoadResult.failure(
          error: step3Result.error ?? '第三步加载失败',
          stats: step3Result.stats,
        );
      }

      debugPrint(
        '🔄 [三步加载] 第3步完成: 加载了 ${step3Result.newMarkers.length} 个新标记摘要',
      );

      // === 后处理：过滤、排序和缓存更新 ===
      onProgressUpdate?.call('处理数据...');
      final allMarkers = [
        ...step3Result.newMarkers,
        ...step2Result.cachedMarkers,
      ];

      // 应用过滤
      final filterResult = _filterService.filterMarkers(allMarkers, useConfig);

      // 应用排序
      final sortResult = _priorityCalculator.sortMarkers(
        filterResult.filteredMarkers,
        useConfig,
      );

      // 更新区域索引
      await _updateRegionIndex(region, allMarkers);

      // 更新标记流
      _markersController.add(sortResult.sortedMarkers);

      final duration = DateTime.now().difference(startTime);
      final stats = {
        'success': true,
        'duration_ms': duration.inMilliseconds,
        'step1_ids': allMarkerIds.length,
        'step2_cached': step2Result.cachedMarkers.length,
        'step2_outdated': step2Result.outdatedIds.length,
        'step3_loaded': step3Result.newMarkers.length,
        'total_markers': allMarkers.length,
        'filtered_markers': filterResult.filteredMarkers.length,
        'final_markers': sortResult.sortedMarkers.length,
        'filter_stats': filterResult.stats.filterBreakdown,
        'sort_stats': sortResult.stats.averageScores,
      };

      onProgressUpdate?.call('加载完成');
      debugPrint(
        '✅ [三步加载] 加载完成: ${sortResult.sortedMarkers.length} 个标记, 耗时: ${duration.inMilliseconds}ms',
      );

      return MarkerLoadResult.success(
        markers: sortResult.sortedMarkers,
        stats: stats,
      );
    } catch (e) {
      final duration = DateTime.now().difference(startTime);
      debugPrint('❌ [三步加载] 加载失败: $e, 耗时: ${duration.inMilliseconds}ms');

      return MarkerLoadResult.failure(
        error: e.toString(),
        stats: {
          'success': false,
          'duration_ms': duration.inMilliseconds,
          'error': e.toString(),
        },
      );
    } finally {
      _loadingStateController.add(false);
    }
  }

  /// 更新新地图区域的标记（智能增量更新）
  Future<MarkerLoadResult> updateMarkersForNewRegion({
    required List<MapRegion> newRegions,
    FilterConfig? config,
    Function(String)? onProgressUpdate,
  }) async {
    _ensureInitialized();

    final useConfig = config ?? _currentConfig;
    final startTime = DateTime.now();

    debugPrint('🔄 [增量更新] 开始智能增量更新: ${newRegions.length} 个新区域');

    try {
      _loadingStateController.add(true);
      onProgressUpdate?.call('分析区域变化...');

      // === 第一步：分析区域变化 ===
      final regionChanges = _regionTracker.updateRegions(newRegions);

      debugPrint('🔄 [增量更新] 区域变化分析: ${regionChanges.toString()}');

      if (!regionChanges.hasChanges) {
        // 没有区域变化，返回当前缓存的标记
        onProgressUpdate?.call('无变化，使用缓存数据');
        return await _returnCachedMarkers(useConfig, startTime);
      }

      // === 第二步：执行增量更新 ===
      onProgressUpdate?.call('加载新区域数据...');
      final incrementalResult = await _cache.performIncrementalUpdate(
        newRegions,
      );

      if (!incrementalResult.stats['success']) {
        return MarkerLoadResult.failure(
          error: incrementalResult.stats['error'] ?? '增量更新失败',
          stats: incrementalResult.stats,
        );
      }

      // === 第三步：获取所有有效标记 ===
      onProgressUpdate?.call('合并数据...');
      final allMarkers = incrementalResult.allValidMarkers;

      // === 第四步：应用过滤和排序 ===
      onProgressUpdate?.call('过滤和排序...');
      final filterResult = _filterService.filterMarkers(allMarkers, useConfig);
      final sortResult = _priorityCalculator.sortMarkers(
        filterResult.filteredMarkers,
        useConfig,
      );

      // === 第五步：清理过期区域 ===
      await _cleanupRemovedRegions(regionChanges.removedRegions);

      // 更新标记流
      _markersController.add(sortResult.sortedMarkers);

      final duration = DateTime.now().difference(startTime);
      final stats = {
        'success': true,
        'duration_ms': duration.inMilliseconds,
        'regions_added': regionChanges.addedRegions.length,
        'regions_removed': regionChanges.removedRegions.length,
        'regions_overlapped': regionChanges.overlappedRegions.length,
        'new_markers': incrementalResult.newMarkers.length,
        'updated_markers': incrementalResult.updatedMarkers.length,
        'cached_markers': incrementalResult.cachedMarkers.length,
        'final_markers': sortResult.sortedMarkers.length,
        'filter_stats': filterResult.stats.filterBreakdown,
        'sort_stats': sortResult.stats.averageScores,
      };

      onProgressUpdate?.call('更新完成');
      debugPrint(
        '✅ [增量更新] 更新完成: ${sortResult.sortedMarkers.length} 个标记, 耗时: ${duration.inMilliseconds}ms',
      );

      return MarkerLoadResult.success(
        markers: sortResult.sortedMarkers,
        stats: stats,
      );
    } catch (e) {
      final duration = DateTime.now().difference(startTime);
      debugPrint('❌ [区域更新] 更新失败: $e, 耗时: ${duration.inMilliseconds}ms');

      return MarkerLoadResult.failure(
        error: e.toString(),
        stats: {
          'success': false,
          'duration_ms': duration.inMilliseconds,
          'error': e.toString(),
        },
      );
    } finally {
      _loadingStateController.add(false);
    }
  }

  /// 获取标记详细信息（按需加载）
  Future<MarkerDetailsResult> getMarkerDetails(
    String markerId,
    MarkerType type, {
    bool forceReload = false,
    Function(String)? onProgressUpdate,
  }) async {
    _ensureInitialized();

    final startTime = DateTime.now();
    debugPrint('🔍 [标记详情] 开始获取标记详情: $markerId ($type)');

    try {
      onProgressUpdate?.call('检查缓存...');

      // === 第一步：检查缓存 ===
      if (!forceReload) {
        final cachedDetails = await _cache.getMarkerDetails(markerId, type);
        if (cachedDetails != null) {
          final duration = DateTime.now().difference(startTime);
          debugPrint(
            '📦 [标记详情] 从缓存获取: $markerId, 耗时: ${duration.inMilliseconds}ms',
          );

          return MarkerDetailsResult.success(
            details: cachedDetails,
            fromCache: true,
            stats: {'duration_ms': duration.inMilliseconds, 'from_cache': true},
          );
        }
      }

      // === 第二步：从服务加载详情 ===
      onProgressUpdate?.call('加载详情数据...');
      final details = await _loadMarkerDetailsFromService(markerId, type);

      if (details == null) {
        return MarkerDetailsResult.failure(
          error: '未找到标记详情: $markerId',
          stats: {
            'duration_ms': DateTime.now().difference(startTime).inMilliseconds,
            'from_cache': false,
          },
        );
      }

      // === 第三步：更新缓存 ===
      await _updateDetailsCache(markerId, type, details);

      final duration = DateTime.now().difference(startTime);
      debugPrint('✅ [标记详情] 加载成功: $markerId, 耗时: ${duration.inMilliseconds}ms');

      onProgressUpdate?.call('加载完成');
      return MarkerDetailsResult.success(
        details: details,
        fromCache: false,
        stats: {'duration_ms': duration.inMilliseconds, 'from_cache': false},
      );
    } catch (e) {
      final duration = DateTime.now().difference(startTime);
      debugPrint(
        '❌ [标记详情] 获取失败: $markerId, 错误: $e, 耗时: ${duration.inMilliseconds}ms',
      );

      return MarkerDetailsResult.failure(
        error: e.toString(),
        stats: {'duration_ms': duration.inMilliseconds, 'error': e.toString()},
      );
    }
  }

  /// 批量获取标记详情
  Future<Map<String, MarkerDetailsResult>> batchGetMarkerDetails(
    List<String> markerIds,
    Map<String, MarkerType> markerTypes, {
    bool forceReload = false,
    Function(String)? onProgressUpdate,
  }) async {
    _ensureInitialized();

    final results = <String, MarkerDetailsResult>{};
    final startTime = DateTime.now();

    debugPrint('🔍 [批量详情] 开始批量获取 ${markerIds.length} 个标记详情');

    try {
      onProgressUpdate?.call('批量加载详情...');

      // 并行加载所有标记详情
      final futures =
          markerIds.map((markerId) {
            final type = markerTypes[markerId] ?? MarkerType.spot;
            return getMarkerDetails(markerId, type, forceReload: forceReload);
          }).toList();

      final detailResults = await Future.wait(futures);

      // 组装结果
      for (int i = 0; i < markerIds.length; i++) {
        results[markerIds[i]] = detailResults[i];
      }

      final duration = DateTime.now().difference(startTime);
      final successCount = results.values.where((r) => r.success).length;

      debugPrint(
        '✅ [批量详情] 批量加载完成: $successCount/${markerIds.length} 成功, 耗时: ${duration.inMilliseconds}ms',
      );

      onProgressUpdate?.call('批量加载完成');
      return results;
    } catch (e) {
      debugPrint('❌ [批量详情] 批量加载失败: $e');

      // 返回失败结果
      for (final markerId in markerIds) {
        results[markerId] = MarkerDetailsResult.failure(
          error: e.toString(),
          stats: {'error': e.toString()},
        );
      }

      return results;
    }
  }

  // ==================== 辅助方法 ====================

  /// 获取所有缓存的标记
  List<UnifiedMarker> getAllCachedMarkers() {
    final bounds = LatLngBounds(LatLng(-90, -180), LatLng(90, 180));
    return _cache.getCachedMarkersInRegion(bounds);
  }

  /// 获取过滤服务
  MarkerFilterService get filterService => _filterService;

  /// 重新应用当前过滤和排序
  Future<List<UnifiedMarker>> reapplyFilterAndSort(
    List<UnifiedMarker> markers,
  ) async {
    final filterResult = _filterService.filterMarkers(markers, _currentConfig);
    final sortResult = _priorityCalculator.sortMarkers(
      filterResult.filteredMarkers,
      _currentConfig,
    );

    _markersController.add(sortResult.sortedMarkers);
    return sortResult.sortedMarkers;
  }

  /// 预缓存标记详情
  Future<void> precacheMarkerDetails(List<String> markerIds) async {
    _ensureInitialized();
    await _cache.precacheMarkerDetails(markerIds);
  }

  /// 清理超出边界的缓存
  Future<void> cleanupOutOfBoundsCache(LatLngBounds bounds) async {
    _ensureInitialized();
    await _cache.cleanupOutOfBoundsCache(bounds);
  }

  // ==================== 统计和调试 ====================

  /// 获取管理器统计信息
  Map<String, dynamic> getManagerStats() {
    return {
      'is_initialized': _isInitialized,
      'current_user_id': _currentUserId,
      'has_current_location': _currentLocation != null,
      'current_config': _currentConfig.getSummary(),
      'cache_stats': _cache.getCacheStats(),
      'filter_stats': _filterService.getFilterStats(),
      'calculator_stats': _priorityCalculator.getCalculatorStats(),
      'debounce_throttle_stats': _debounceThrottleManager.getStats(),
      'memory_stats': _memoryStats,
      'last_memory_cleanup': _lastMemoryCleanup?.toIso8601String(),
      'active_operations': _debounceThrottleManager.activeOperationsCount,
    };
  }

  /// 打印管理器统计信息
  void printManagerStats() {
    final stats = getManagerStats();
    debugPrint('📊 [管理器统计] ========== 统一标记管理器统计 ==========');
    debugPrint('📊 [管理器统计] 初始化状态: ${stats['is_initialized']}');
    debugPrint('📊 [管理器统计] 当前用户: ${stats['current_user_id'] ?? '未设置'}');
    debugPrint(
      '📊 [管理器统计] 位置信息: ${stats['has_current_location'] ? '已设置' : '未设置'}',
    );
    debugPrint('📊 [管理器统计] 过滤配置: ${stats['current_config']}');
    debugPrint('📊 [管理器统计] 活跃操作: ${stats['active_operations']}');
    debugPrint('📊 [管理器统计] 内存统计: ${stats['memory_stats']}');
    debugPrint('📊 [管理器统计] 最后清理: ${stats['last_memory_cleanup'] ?? '无'}');
    _cache.printCacheStats();
    _filterService.printFilterStats();
    _priorityCalculator.printCalculatorStats();
    _debounceThrottleManager.printStats();
    debugPrint('📊 [管理器统计] =============================================');
  }

  /// 清空所有缓存
  Future<void> clearAllCaches() async {
    await _cache.clearAllCaches();
    debugPrint('🧹 [统一管理器] 所有缓存已清空');
  }

  // ==================== 内存管理实现 ====================

  /// 启动内存监控
  void _startMemoryMonitoring() {
    _memoryMonitorTimer?.cancel();
    _memoryMonitorTimer = Timer.periodic(_memoryMonitorInterval, (_) {
      _monitorMemoryUsage();
    });

    _cacheCleanupTimer?.cancel();
    _cacheCleanupTimer = Timer.periodic(_cacheCleanupInterval, (_) {
      _performScheduledCacheCleanup();
    });

    debugPrint('🔍 [内存监控] 内存监控已启动');
  }

  /// 停止内存监控
  void _stopMemoryMonitoring() {
    _memoryMonitorTimer?.cancel();
    _cacheCleanupTimer?.cancel();
    debugPrint('⏹️ [内存监控] 内存监控已停止');
  }

  /// 监控内存使用情况
  void _monitorMemoryUsage() {
    try {
      // 更新内存统计
      _updateMemoryStats();

      final cacheSize = _memoryStats['cache_size'] ?? 0;
      final estimatedMemoryMB = cacheSize ~/ (1024 * 1024); // 转换为MB

      debugPrint('📊 [内存监控] 估计内存使用: ${estimatedMemoryMB}MB');

      // 检查内存压力
      if (estimatedMemoryMB > _memoryPressureThresholdMB) {
        debugPrint('⚠️ [内存压力] 内存使用超过阈值，开始清理');
        _handleMemoryPressure();
      }

      // 检查是否超过最大限制
      if (estimatedMemoryMB > _maxMemoryUsageMB) {
        debugPrint('🚨 [内存超限] 内存使用超过最大限制，执行紧急清理');
        _handleMemoryOverflow();
      }
    } catch (e) {
      debugPrint('❌ [内存监控] 内存监控失败: $e');
    }
  }

  /// 更新内存统计
  void _updateMemoryStats() {
    final cacheStats = _cache.getCacheStats();
    _memoryStats['cache_size'] = cacheStats['total_size'] ?? 0;
    _memoryStats['marker_count'] = cacheStats['total_markers'] ?? 0;
    _memoryStats['region_count'] = cacheStats['region_count'] ?? 0;
  }

  /// 处理内存压力
  void _handleMemoryPressure() {
    _debounceThrottleManager.throttleCacheCleanup(() async {
      try {
        // 清理过期缓存
        await _cleanupExpiredCache();

        // 清理远距离缓存
        await _cleanupDistantCache();

        // 更新统计
        _updateMemoryStats();

        debugPrint('✅ [内存压力] 内存压力处理完成');
      } catch (e) {
        debugPrint('❌ [内存压力] 内存压力处理失败: $e');
      }
    });
  }

  /// 处理内存溢出
  void _handleMemoryOverflow() async {
    try {
      debugPrint('🚨 [内存溢出] 开始紧急内存清理');

      // 取消所有防抖节流操作
      _debounceThrottleManager.cancelAll();

      // 清理所有缓存
      await _cache.clearAllCaches();

      // 重置统计
      _memoryStats.clear();
      _lastMemoryCleanup = DateTime.now();

      debugPrint('✅ [内存溢出] 紧急内存清理完成');
    } catch (e) {
      debugPrint('❌ [内存溢出] 紧急内存清理失败: $e');
    }
  }

  /// 定期缓存清理
  void _performScheduledCacheCleanup() {
    _debounceThrottleManager.throttleCacheCleanup(() async {
      try {
        debugPrint('🧹 [定期清理] 开始定期缓存清理');

        // 清理过期缓存
        await _cleanupExpiredCache();

        // 清理超出边界的缓存
        if (_currentLocation != null) {
          final bounds = LatLngBounds(
            LatLng(
              _currentLocation!.latitude - 0.1,
              _currentLocation!.longitude - 0.1,
            ),
            LatLng(
              _currentLocation!.latitude + 0.1,
              _currentLocation!.longitude + 0.1,
            ),
          );
          await _cache.cleanupOutOfBoundsCache(bounds);
        }

        _lastMemoryCleanup = DateTime.now();
        debugPrint('✅ [定期清理] 定期缓存清理完成');
      } catch (e) {
        debugPrint('❌ [定期清理] 定期缓存清理失败: $e');
      }
    });
  }

  /// 清理过期缓存
  Future<void> _cleanupExpiredCache() async {
    // 这里应该调用缓存系统的过期清理方法
    debugPrint('🧹 [过期清理] 清理过期缓存');
  }

  /// 清理远距离缓存
  Future<void> _cleanupDistantCache() async {
    if (_currentLocation == null) return;

    // 清理距离当前位置较远的缓存
    final bounds = LatLngBounds(
      LatLng(
        _currentLocation!.latitude - 0.05,
        _currentLocation!.longitude - 0.05,
      ),
      LatLng(
        _currentLocation!.latitude + 0.05,
        _currentLocation!.longitude + 0.05,
      ),
    );

    await _cache.cleanupOutOfBoundsCache(bounds);
    debugPrint('🧹 [距离清理] 清理远距离缓存');
  }

  // ==================== 增量更新辅助方法 ====================

  /// 返回缓存的标记（无变化时使用）
  Future<MarkerLoadResult> _returnCachedMarkers(
    FilterConfig config,
    DateTime startTime,
  ) async {
    try {
      // 获取当前视图范围内的缓存标记
      final bounds = LatLngBounds(LatLng(-90, -180), LatLng(90, 180));
      final cachedMarkers = _cache.getCachedMarkersInRegion(bounds);

      // 应用过滤和排序
      final filterResult = _filterService.filterMarkers(cachedMarkers, config);
      final sortResult = _priorityCalculator.sortMarkers(
        filterResult.filteredMarkers,
        config,
      );

      // 更新标记流
      _markersController.add(sortResult.sortedMarkers);

      final duration = DateTime.now().difference(startTime);
      final stats = {
        'success': true,
        'duration_ms': duration.inMilliseconds,
        'cached_markers': cachedMarkers.length,
        'final_markers': sortResult.sortedMarkers.length,
        'from_cache': true,
      };

      debugPrint('📦 [缓存返回] 使用缓存数据: ${sortResult.sortedMarkers.length} 个标记');

      return MarkerLoadResult.success(
        markers: sortResult.sortedMarkers,
        stats: stats,
      );
    } catch (e) {
      final duration = DateTime.now().difference(startTime);
      return MarkerLoadResult.failure(
        error: '缓存数据获取失败: $e',
        stats: {
          'success': false,
          'duration_ms': duration.inMilliseconds,
          'error': e.toString(),
        },
      );
    }
  }

  /// 清理已移除区域的缓存
  Future<void> _cleanupRemovedRegions(List<MapRegion> removedRegions) async {
    if (removedRegions.isEmpty) return;

    try {
      for (final region in removedRegions) {
        // 清理区域相关的缓存
        // 这里应该调用缓存系统的清理方法
        debugPrint('🧹 [区域清理] 清理已移除区域: ${region.id}');
      }

      debugPrint('🧹 [区域清理] 清理了 ${removedRegions.length} 个已移除区域的缓存');
    } catch (e) {
      debugPrint('⚠️ [区域清理] 清理失败: $e');
    }
  }

  /// 从服务加载标记详情
  Future<dynamic> _loadMarkerDetailsFromService(
    String markerId,
    MarkerType type,
  ) async {
    try {
      switch (type) {
        case MarkerType.spot:
          // 从钓点服务获取详情
          final spot = await _spotService.getSpotById(markerId);
          return spot;
        case MarkerType.activity:
          // 从活动服务获取详情
          final activity = await _activityService.getActivityById(markerId);
          return activity;
      }
    } catch (e) {
      debugPrint('❌ [服务加载] 从服务加载详情失败: $markerId ($type), 错误: $e');
      return null;
    }
  }

  /// 更新详情缓存
  Future<void> _updateDetailsCache(
    String markerId,
    MarkerType type,
    dynamic details,
  ) async {
    try {
      // 通过缓存系统更新详情缓存
      // 这里应该调用缓存系统的更新方法
      debugPrint('💾 [详情缓存] 更新详情缓存: $markerId ($type)');
    } catch (e) {
      debugPrint('⚠️ [详情缓存] 更新缓存失败: $markerId, 错误: $e');
    }
  }

  // ==================== 三步加载实现 ====================

  /// 第一步：加载区域内标记ID
  Future<_Step1Result> _performStep1LoadIds(
    MapRegion region,
    bool forceReload,
  ) async {
    try {
      // 并行获取钓点和活动ID
      // TODO: 实现getRegionSpotIds和getRegionActivityIds方法
      final futures = await Future.wait([
        // _spotService.getRegionSpotIds(
        //   center: region.center,
        //   radiusKm: region.radiusKm,
        // ),
        Future.value(<MarkerIdInfo>[]), // 临时返回空列表
        // _activityService.getRegionActivityIds(
        //   center: region.center,
        //   radiusKm: region.radiusKm,
        // ),
        Future.value(<MarkerIdInfo>[]), // 临时返回空列表
      ]);

      final spotIds = futures[0];
      final activityIds = futures[1];

      final allMarkerIds = <MarkerIdInfo>[];
      allMarkerIds.addAll(spotIds);
      allMarkerIds.addAll(activityIds);

      return _Step1Result(
        success: true,
        markerIds: allMarkerIds,
        stats: {
          'spot_ids': spotIds.length,
          'activity_ids': activityIds.length,
          'total_ids': allMarkerIds.length,
        },
      );
    } catch (e) {
      return _Step1Result(
        success: false,
        markerIds: [],
        error: e.toString(),
        stats: {'error': e.toString()},
      );
    }
  }

  /// 第二步：检查缓存状态
  Future<_Step2Result> _performStep2CheckCache(
    List<MarkerIdInfo> markerIds,
    bool forceReload,
  ) async {
    final outdatedIds = <String>[];
    final cachedMarkers = <UnifiedMarker>[];

    if (forceReload) {
      // 强制重新加载，所有ID都视为过期
      outdatedIds.addAll(markerIds.map((info) => info.id));
    } else {
      // 通过缓存系统检查是否过期
      final outdatedMarkerIds = await _cache.getOutdatedMarkerIds(markerIds);
      outdatedIds.addAll(outdatedMarkerIds);

      // 获取未过期的缓存标记
      for (final markerInfo in markerIds) {
        if (!outdatedMarkerIds.contains(markerInfo.id)) {
          // 这个标记在缓存中且未过期，但我们需要从缓存中获取它
          // 由于UnifiedMarkerCache的接口限制，我们暂时将其标记为需要加载
          // 在实际实现中，应该有更直接的方法从缓存获取单个标记
          outdatedIds.add(markerInfo.id);
        }
      }
    }

    return _Step2Result(outdatedIds: outdatedIds, cachedMarkers: cachedMarkers);
  }

  /// 第三步：批量加载标记摘要
  Future<_Step3Result> _performStep3LoadSummaries(
    _Step2Result step2Result,
  ) async {
    if (step2Result.outdatedIds.isEmpty) {
      return _Step3Result(
        success: true,
        newMarkers: [],
        stats: {'loaded_count': 0},
      );
    }

    try {
      // 按类型分组ID
      final spotIds = <String>[];
      final activityIds = <String>[];

      // 简化处理：根据ID模式或其他规则分类，或者都当作钓点处理
      // 在实际实现中，可以通过其他方式确定类型
      for (final markerId in step2Result.outdatedIds) {
        // 简化处理：默认都当作钓点，实际应用中需要更智能的类型判断
        spotIds.add(markerId);
      }

      // 并行加载钓点和活动摘要
      final futures = <Future<List<UnifiedMarker>>>[];

      if (spotIds.isNotEmpty) {
        // TODO: 实现getSpotSummaries方法
        futures.add(
          // _spotService
          //     .getSpotSummaries(spotIds)
          //     .then((spots) => spots.cast<UnifiedMarker>()),
          Future.value(<UnifiedMarker>[]), // 临时返回空列表
        );
      }

      if (activityIds.isNotEmpty) {
        futures.add(
          _activityService
              .getActivitySummaries(activityIds)
              .then((activities) => activities.cast<UnifiedMarker>()),
        );
      }

      final results = await Future.wait(futures);
      final newMarkers = results.expand((list) => list).toList();

      // 更新缓存
      await _updateCacheWithNewMarkers(newMarkers);

      return _Step3Result(
        success: true,
        newMarkers: newMarkers,
        stats: {
          'loaded_count': newMarkers.length,
          'spot_count': spotIds.length,
          'activity_count': activityIds.length,
        },
      );
    } catch (e) {
      return _Step3Result(
        success: false,
        newMarkers: [],
        error: e.toString(),
        stats: {'error': e.toString()},
      );
    }
  }

  /// 更新缓存中的新标记
  Future<void> _updateCacheWithNewMarkers(List<UnifiedMarker> markers) async {
    // 缓存更新由UnifiedMarkerCache内部处理
    // 这里可以添加额外的处理逻辑，如统计、日志等
    debugPrint('💾 [缓存更新] 更新了 ${markers.length} 个标记的缓存');
  }

  /// 更新区域索引
  Future<void> _updateRegionIndex(
    MapRegion region,
    List<UnifiedMarker> markers,
  ) async {
    // 区域索引更新由缓存系统内部处理
    // 这里可以添加额外的处理逻辑
    debugPrint('🗺️ [区域索引] 为区域 ${region.id} 更新了 ${markers.length} 个标记的索引');
  }

  // ==================== 资源管理 ====================

  /// 确保已初始化
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('UnifiedMarkerManager 尚未初始化，请先调用 initialize()');
    }
  }

  /// 清理资源
  Future<void> dispose() async {
    debugPrint('🧹 [统一管理器] 开始清理资源');

    // 停止内存监控
    _stopMemoryMonitoring();

    // 清理防抖节流管理器
    _debounceThrottleManager.dispose();

    await _loadingStateController.close();
    await _markersController.close();

    await _cache.dispose();
    _filterService.dispose();
    _priorityCalculator.dispose();

    _isInitialized = false;
    debugPrint('✅ [统一管理器] 资源清理完成');
  }
}

// ==================== 内部结果类 ====================

/// 第一步加载结果
class _Step1Result {
  final bool success;
  final List<MarkerIdInfo> markerIds;
  final String? error;
  final Map<String, dynamic> stats;

  const _Step1Result({
    required this.success,
    required this.markerIds,
    this.error,
    required this.stats,
  });
}

/// 第二步检查结果
class _Step2Result {
  final List<String> outdatedIds;
  final List<UnifiedMarker> cachedMarkers;

  const _Step2Result({required this.outdatedIds, required this.cachedMarkers});
}

/// 第三步加载结果
class _Step3Result {
  final bool success;
  final List<UnifiedMarker> newMarkers;
  final String? error;
  final Map<String, dynamic> stats;

  const _Step3Result({
    required this.success,
    required this.newMarkers,
    this.error,
    required this.stats,
  });
}
