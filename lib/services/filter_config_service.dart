import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../config/filter_config.dart';

/// 过滤配置服务
///
/// 负责过滤配置的持久化存储和管理
/// 支持自动保存、手动保存、配置恢复等功能
class FilterConfigService {
  static const String _configKey = 'marker_filter_config';
  static const String _lastSavedKey = 'marker_filter_last_saved';
  static const String _autoSaveEnabledKey = 'marker_filter_auto_save';

  static FilterConfigService? _instance;
  SharedPreferences? _prefs;

  /// 当前配置
  FilterConfig _currentConfig = FilterConfig.defaultConfig();

  /// 是否启用自动保存
  bool _autoSaveEnabled = true;

  /// 配置变更监听器
  final List<Function(FilterConfig)> _listeners = [];

  /// 获取单例实例
  static FilterConfigService get instance {
    _instance ??= FilterConfigService._();
    return _instance!;
  }

  FilterConfigService._();

  /// 初始化服务
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();

      // 加载自动保存设置
      _autoSaveEnabled = _prefs?.getBool(_autoSaveEnabledKey) ?? true;

      // 加载保存的配置
      await _loadSavedConfig();

      debugPrint('✅ [配置服务] 初始化完成');
    } catch (e) {
      debugPrint('❌ [配置服务] 初始化失败: $e');
    }
  }

  /// 获取当前配置
  FilterConfig get currentConfig => _currentConfig;

  /// 是否启用自动保存
  bool get autoSaveEnabled => _autoSaveEnabled;

  /// 获取最后保存时间
  DateTime? get lastSavedTime {
    final timestamp = _prefs?.getInt(_lastSavedKey);
    return timestamp != null
        ? DateTime.fromMillisecondsSinceEpoch(timestamp)
        : null;
  }

  // ==================== 配置管理 ====================

  /// 更新配置
  Future<void> updateConfig(FilterConfig config, {bool autoSave = true}) async {
    _currentConfig = config;

    // 通知监听器
    for (final listener in _listeners) {
      try {
        listener(config);
      } catch (e) {
        debugPrint('⚠️ [配置服务] 监听器回调失败: $e');
      }
    }

    // 自动保存
    if (autoSave && _autoSaveEnabled) {
      await _saveConfigInternal(config, isAutoSave: true);
    }

    debugPrint('🔄 [配置服务] 配置已更新: ${config.getSummary()}');
  }

  /// 手动保存配置
  Future<bool> saveConfig([FilterConfig? config]) async {
    final configToSave = config ?? _currentConfig;
    return await _saveConfigInternal(configToSave, isAutoSave: false);
  }

  /// 加载保存的配置
  Future<FilterConfig?> loadSavedConfig() async {
    await _loadSavedConfig();
    return _currentConfig;
  }

  /// 重置为默认配置
  Future<void> resetToDefault({bool save = true}) async {
    final defaultConfig = FilterConfig.defaultConfig();
    await updateConfig(defaultConfig, autoSave: save);
    debugPrint('🔄 [配置服务] 已重置为默认配置');
  }

  /// 应用预设配置
  Future<void> applyPresetConfig(String preset, {bool save = true}) async {
    FilterConfig newConfig;

    switch (preset) {
      case 'default':
        newConfig = FilterConfig.defaultConfig();
        break;
      case 'relaxed':
        newConfig = FilterConfig.relaxedConfig();
        break;
      case 'strict':
        newConfig = FilterConfig.strictConfig();
        break;
      case 'personalized':
        newConfig = FilterConfig.personalizedConfig();
        break;
      default:
        debugPrint('⚠️ [配置服务] 未知的预设配置: $preset');
        return;
    }

    await updateConfig(newConfig, autoSave: save);
    debugPrint('🔄 [配置服务] 已应用预设配置: $preset');
  }

  // ==================== 自动保存设置 ====================

  /// 设置自动保存
  Future<void> setAutoSaveEnabled(bool enabled) async {
    _autoSaveEnabled = enabled;
    await _prefs?.setBool(_autoSaveEnabledKey, enabled);
    debugPrint('⚙️ [配置服务] 自动保存已${enabled ? '启用' : '禁用'}');
  }

  // ==================== 监听器管理 ====================

  /// 添加配置变更监听器
  void addListener(Function(FilterConfig) listener) {
    _listeners.add(listener);
  }

  /// 移除配置变更监听器
  void removeListener(Function(FilterConfig) listener) {
    _listeners.remove(listener);
  }

  /// 清除所有监听器
  void clearListeners() {
    _listeners.clear();
  }

  // ==================== 配置比较和验证 ====================

  /// 检查配置是否有变更
  bool hasConfigChanged(FilterConfig config) {
    return _currentConfig != config;
  }

  /// 检查是否有未保存的变更
  bool get hasUnsavedChanges {
    final lastSaved = lastSavedTime;
    if (lastSaved == null) return true;

    // 简单的变更检测：如果配置不是默认配置且最后保存时间超过5分钟
    return !_currentConfig.isDefault &&
        DateTime.now().difference(lastSaved).inMinutes > 5;
  }

  /// 验证配置
  List<String> validateConfig([FilterConfig? config]) {
    return (config ?? _currentConfig).validate();
  }

  // ==================== 配置导入导出 ====================

  /// 导出配置为JSON字符串
  String exportConfig([FilterConfig? config]) {
    final configToExport = config ?? _currentConfig;
    final json = configToExport.toJson();
    return jsonEncode(json);
  }

  /// 从JSON字符串导入配置
  Future<bool> importConfig(String jsonString, {bool save = true}) async {
    try {
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      final config = FilterConfig.fromJson(json);

      // 验证配置
      final errors = config.validate();
      if (errors.isNotEmpty) {
        debugPrint('❌ [配置服务] 导入的配置无效: ${errors.join(', ')}');
        return false;
      }

      await updateConfig(config, autoSave: save);
      debugPrint('✅ [配置服务] 配置导入成功');
      return true;
    } catch (e) {
      debugPrint('❌ [配置服务] 配置导入失败: $e');
      return false;
    }
  }

  // ==================== 内部方法 ====================

  /// 内部保存配置方法
  Future<bool> _saveConfigInternal(
    FilterConfig config, {
    required bool isAutoSave,
  }) async {
    try {
      if (_prefs == null) {
        debugPrint('⚠️ [配置服务] SharedPreferences 未初始化');
        return false;
      }

      final json = config.toJson();
      final jsonString = jsonEncode(json);

      await _prefs!.setString(_configKey, jsonString);
      await _prefs!.setInt(
        _lastSavedKey,
        DateTime.now().millisecondsSinceEpoch,
      );

      debugPrint('💾 [配置服务] 配置已${isAutoSave ? '自动' : '手动'}保存');
      return true;
    } catch (e) {
      debugPrint('❌ [配置服务] 配置保存失败: $e');
      return false;
    }
  }

  /// 内部加载配置方法
  Future<void> _loadSavedConfig() async {
    try {
      if (_prefs == null) return;

      final jsonString = _prefs!.getString(_configKey);
      if (jsonString == null) {
        debugPrint('ℹ️ [配置服务] 未找到保存的配置，使用默认配置');
        return;
      }

      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      final config = FilterConfig.fromJson(json);

      // 验证配置
      final errors = config.validate();
      if (errors.isNotEmpty) {
        debugPrint('⚠️ [配置服务] 保存的配置无效，使用默认配置: ${errors.join(', ')}');
        return;
      }

      _currentConfig = config;
      debugPrint('✅ [配置服务] 已加载保存的配置: ${config.getSummary()}');
    } catch (e) {
      debugPrint('❌ [配置服务] 配置加载失败，使用默认配置: $e');
    }
  }

  // ==================== 统计和调试 ====================

  /// 获取服务统计信息
  Map<String, dynamic> getServiceStats() {
    return {
      'current_config_summary': _currentConfig.getSummary(),
      'auto_save_enabled': _autoSaveEnabled,
      'last_saved_time': lastSavedTime?.toIso8601String(),
      'has_unsaved_changes': hasUnsavedChanges,
      'listeners_count': _listeners.length,
      'config_is_valid': _currentConfig.isValid,
      'config_is_default': _currentConfig.isDefault,
    };
  }

  /// 打印服务统计信息
  void printServiceStats() {
    final stats = getServiceStats();
    debugPrint('📊 [配置服务统计] ========== 过滤配置服务统计 ==========');
    debugPrint('📊 [配置服务统计] 当前配置: ${stats['current_config_summary']}');
    debugPrint('📊 [配置服务统计] 自动保存: ${stats['auto_save_enabled']}');
    debugPrint('📊 [配置服务统计] 最后保存: ${stats['last_saved_time'] ?? '无'}');
    debugPrint('📊 [配置服务统计] 未保存变更: ${stats['has_unsaved_changes']}');
    debugPrint('📊 [配置服务统计] 监听器数量: ${stats['listeners_count']}');
    debugPrint('📊 [配置服务统计] 配置有效: ${stats['config_is_valid']}');
    debugPrint('📊 [配置服务统计] 默认配置: ${stats['config_is_default']}');
    debugPrint('📊 [配置服务统计] =============================================');
  }

  /// 清理资源
  void dispose() {
    clearListeners();
    debugPrint('🧹 [配置服务] 资源已清理');
  }
}
