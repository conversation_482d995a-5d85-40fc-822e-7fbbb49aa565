import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;
import 'package:http/http.dart' as http;
import 'encrypted_r2_service.dart';
import '../config/r2_config.dart' hide ImageUploadResult;
import '../models/upload_result.dart';
import 'webp_image_service.dart';

/// 安全的图片上传服务
/// 使用加密的 R2 凭据和 Flutter 端签名生成
class SecureImageUploadService {
  final EncryptedR2Service _r2Service = EncryptedR2Service();

  /// 上传单张视频（独立上传版本，不需要钓点ID）
  Future<ImageUploadResult?> uploadVideoIndependent({
    required File videoFile,
    required String userId,
    String? customFileName, // 可选的自定义文件名基础
  }) async {
    try {
      debugPrint('🎥 [独立视频上传] 开始独立视频上传');
      debugPrint('🎥 [独立视频上传] 用户ID: $userId');
      debugPrint('🎥 [独立视频上传] 视频文件: ${videoFile.path}');
      debugPrint('🎥 [独立视频上传] 文件大小: ${await videoFile.length()} bytes');

      // 1. 验证视频文件
      if (!await _validateImage(videoFile)) {
        debugPrint('❌ [独立视频上传] 视频验证失败');
        return null;
      }

      // 2. 读取视频文件（不进行图片解码）
      final videoBytes = await videoFile.readAsBytes();
      debugPrint('🎥 [独立视频上传] 视频文件读取成功');

      // 3. 生成文件路径
      final extension = path
          .extension(videoFile.path)
          .toLowerCase()
          .replaceAll('.', '');

      final String videoPath;
      if (customFileName != null) {
        // 使用自定义文件名基础
        videoPath = 'fishing_app/$userId/$customFileName.$extension';
        debugPrint('🎥 [独立视频上传] 使用自定义文件名: $customFileName');
      } else {
        // 使用默认的文件名生成逻辑
        videoPath = R2Config.generateImagePath(
          userId,
          'independent',
          extension,
        );
      }

      debugPrint('🎥 [独立视频上传] 生成存储路径: $videoPath');

      // 4. 生成预签名URL
      final presignedUrl = await _r2Service.generatePresignedUrl(
        objectKey: videoPath,
        method: 'PUT',
        expiresIn: 3600,
      );

      if (presignedUrl == null) {
        debugPrint('❌ [独立视频上传] 生成预签名URL失败');
        return null;
      }

      debugPrint('✅ [独立视频上传] 预签名URL生成成功');

      // 5. 上传视频文件
      debugPrint('🎥 [独立视频上传] 开始上传视频文件...');
      final uploadSuccess = await _uploadToR2(
        presignedUrl: presignedUrl,
        fileBytes: videoBytes,
        contentType: 'video/$extension',
      );

      if (!uploadSuccess) {
        debugPrint('❌ [独立视频上传] 视频上传失败');
        return null;
      }

      // 6. 获取 R2 凭据以构建公开URL
      final credentials = await _r2Service.getR2Credentials();
      if (credentials == null) {
        debugPrint('❌ [独立视频上传] 无法获取R2凭据构建公开URL');
        return null;
      }

      // 7. 构建公开访问URL
      final videoUrl =
          '${credentials.endpoint}/${credentials.bucketName}/$videoPath';

      debugPrint('✅ [独立视频上传] 视频上传成功: $videoUrl');

      // 8. 返回结果
      return ImageUploadResult(
        originalUrl: videoUrl,
        thumbnailUrl: videoUrl, // 视频使用相同的URL作为缩略图URL
        fileName: path.basename(videoFile.path),
        fileSize: await videoFile.length(),
        width: 0, // 视频没有宽度概念，设为0
        height: 0, // 视频没有高度概念，设为0
      );
    } catch (e) {
      debugPrint('❌ [独立视频上传] 视频上传异常: $e');
      return null;
    }
  }

  /// 上传单张图片（独立上传版本，不需要钓点ID）
  Future<ImageUploadResult?> uploadImageIndependent({
    required File imageFile,
    required String userId,
    String? customFileName, // 可选的自定义文件名基础
  }) async {
    try {
      if (customFileName != null) {
        debugPrint('🖼️ [缩略图上传] 开始独立缩略图上传');
        debugPrint('🖼️ [缩略图上传] 用户ID: $userId');
        debugPrint('🖼️ [缩略图上传] 自定义文件名: $customFileName');
      } else {
        debugPrint('🔐 [独立上传] 开始独立图片上传');
        debugPrint('🔐 [独立上传] 用户ID: $userId');
      }

      // 1. 验证文件
      if (!await _validateImage(imageFile)) {
        if (customFileName != null) {
          debugPrint('❌ [缩略图上传] 缩略图验证失败');
        } else {
          debugPrint('❌ [独立上传] 图片验证失败');
        }
        return null;
      }

      // 2. 读取和处理图片
      final imageBytes = await imageFile.readAsBytes();
      final originalImage = img.decodeImage(imageBytes);

      if (originalImage == null) {
        if (customFileName != null) {
          debugPrint('❌ [缩略图上传] 无法解码缩略图');
        } else {
          debugPrint('❌ [独立上传] 无法解码图片');
        }
        return null;
      }

      // 3. 使用WebP服务处理原图
      final originalResult = await WebPImageService.processImage(
        imageBytes: imageBytes,
        quality: R2Config.imageQuality,
        maxWidth: R2Config.maxImageWidth,
        maxHeight: R2Config.maxImageHeight,
      );

      // 4. 生成缩略图
      final thumbnailResult = await WebPImageService.processImage(
        imageBytes: imageBytes,
        quality: 80,
        forThumbnail: true,
      );

      debugPrint('🔐 [独立上传] 图片处理完成');
      debugPrint(
        '🔐 [独立上传] 压缩后大小: ${originalResult.compressedSize} bytes (${originalResult.format})',
      );
      debugPrint(
        '🔐 [独立上传] 缩略图大小: ${thumbnailResult.compressedSize} bytes (${thumbnailResult.format})',
      );
      debugPrint(
        '🔐 [独立上传] 压缩率: ${originalResult.compressionRatio.toStringAsFixed(1)}%',
      );

      // 5. 生成文件路径（使用统一的fishing_app路径）
      final String originalPath;
      final String thumbnailPath;

      if (customFileName != null) {
        // 使用自定义文件名基础
        originalPath =
            'fishing_app/$userId/$customFileName.${originalResult.extension}';
        thumbnailPath =
            'fishing_app/$userId/${customFileName}_thumb.${thumbnailResult.extension}';
        debugPrint('🖼️ [缩略图上传] 使用自定义文件名: $customFileName');
        debugPrint('🖼️ [缩略图上传] 原图路径: $originalPath');
        debugPrint('🖼️ [缩略图上传] 缩略图路径: $thumbnailPath');
      } else {
        // 使用默认的文件名生成逻辑
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final uuid = '${timestamp}_${_generateRandomString(8)}';
        originalPath =
            'fishing_app/$userId/${timestamp}_$uuid.${originalResult.extension}';
        thumbnailPath =
            'fishing_app/$userId/${timestamp}_${uuid}_thumb.${thumbnailResult.extension}';
      }

      debugPrint('🔐 [独立上传] 原图路径: $originalPath');
      debugPrint('🔐 [独立上传] 缩略图路径: $thumbnailPath');

      // 6. 生成预签名URL
      debugPrint('🔐 [独立上传] 开始生成预签名URL...');

      final originalPresignedUrl = await _r2Service.generatePresignedUrl(
        objectKey: originalPath,
        method: 'PUT',
        expiresIn: 3600,
      );

      final thumbnailPresignedUrl = await _r2Service.generatePresignedUrl(
        objectKey: thumbnailPath,
        method: 'PUT',
        expiresIn: 3600,
      );

      if (originalPresignedUrl == null || thumbnailPresignedUrl == null) {
        debugPrint('❌ [独立上传] 生成预签名URL失败');
        return null;
      }

      debugPrint('✅ [独立上传] 预签名URL生成成功');

      // 7. 上传原图
      debugPrint('🔐 [独立上传] 开始上传原图...');
      final originalUploadSuccess = await _uploadToR2(
        presignedUrl: originalPresignedUrl,
        fileBytes: originalResult.imageBytes,
        contentType: 'image/${originalResult.extension}',
      );

      if (!originalUploadSuccess) {
        debugPrint('❌ [独立上传] 原图上传失败');
        return null;
      }

      debugPrint('✅ [独立上传] 原图上传成功');

      // 8. 上传缩略图
      debugPrint('🔐 [独立上传] 开始上传缩略图...');
      final thumbnailUploadSuccess = await _uploadToR2(
        presignedUrl: thumbnailPresignedUrl,
        fileBytes: thumbnailResult.imageBytes,
        contentType: 'image/${thumbnailResult.extension}',
      );

      if (!thumbnailUploadSuccess) {
        debugPrint('⚠️ [独立上传] 缩略图上传失败，但原图已成功');
      } else {
        debugPrint('✅ [独立上传] 缩略图上传成功');
      }

      // 9. 获取 R2 凭据以构建公开URL
      final credentials = await _r2Service.getR2Credentials();
      if (credentials == null) {
        debugPrint('❌ [独立上传] 无法获取R2凭据构建公开URL');
        return null;
      }

      // 10. 构建公开访问URL
      final originalUrl =
          '${credentials.endpoint}/${credentials.bucketName}/$originalPath';
      final thumbnailUrl =
          thumbnailUploadSuccess
              ? '${credentials.endpoint}/${credentials.bucketName}/$thumbnailPath'
              : originalUrl;

      if (customFileName != null) {
        debugPrint('✅ [缩略图上传] 缩略图上传完成');
        debugPrint('🖼️ [缩略图上传] 原图URL: $originalUrl');
        debugPrint('🖼️ [缩略图上传] 缩略图URL: $thumbnailUrl');
      } else {
        debugPrint('✅ [独立上传] 上传完成');
        debugPrint('🔐 [独立上传] 原图URL: $originalUrl');
        debugPrint('🔐 [独立上传] 缩略图URL: $thumbnailUrl');
      }

      return ImageUploadResult(
        originalUrl: originalUrl,
        thumbnailUrl: thumbnailUrl,
        fileName: path.basename(imageFile.path),
        fileSize: originalResult.compressedSize,
        width: originalResult.width,
        height: originalResult.height,
      );
    } catch (e) {
      debugPrint('❌ [独立上传] 上传异常: $e');
      return null;
    }
  }

  /// 上传单张图片（安全版本）
  Future<ImageUploadResult?> uploadImageSecure({
    required File imageFile,
    required String userId,
    required String spotId,
  }) async {
    try {
      debugPrint('🔐 [安全上传] 开始安全图片上传');
      debugPrint('🔐 [安全上传] 用户ID: $userId');
      debugPrint('🔐 [安全上传] 钓点ID: $spotId');

      // 1. 验证文件
      if (!await _validateImage(imageFile)) {
        debugPrint('❌ [安全上传] 图片验证失败');
        return null;
      }

      // 2. 检查文件类型并处理
      final extension = path
          .extension(imageFile.path)
          .toLowerCase()
          .replaceAll('.', '');
      final isVideo = R2Config.allowedVideoExtensions.contains(extension);

      if (isVideo) {
        // 视频文件：直接上传，不进行图片处理
        debugPrint('🔐 [安全上传] 检测到视频文件，跳过图片处理');
        return await _uploadVideoFile(imageFile, userId, spotId);
      }

      // 图片文件：进行正常的图片处理
      final imageBytes = await imageFile.readAsBytes();
      final originalImage = img.decodeImage(imageBytes);

      if (originalImage == null) {
        debugPrint('❌ [安全上传] 无法解码图片');
        return null;
      }

      debugPrint(
        '🔐 [安全上传] 图片解码成功: ${originalImage.width}x${originalImage.height}',
      );

      // 3. 使用WebP服务处理原图
      final originalResult = await WebPImageService.processImage(
        imageBytes: imageBytes,
        quality: R2Config.imageQuality,
        maxWidth: R2Config.maxImageWidth,
        maxHeight: R2Config.maxImageHeight,
      );

      // 4. 生成缩略图
      final thumbnailResult = await WebPImageService.processImage(
        imageBytes: imageBytes,
        quality: 80,
        forThumbnail: true,
      );

      debugPrint('🔐 [安全上传] 图片处理完成');
      debugPrint(
        '🔐 [安全上传] 压缩后大小: ${originalResult.compressedSize} bytes (${originalResult.format})',
      );
      debugPrint(
        '🔐 [安全上传] 缩略图大小: ${thumbnailResult.compressedSize} bytes (${thumbnailResult.format})',
      );
      debugPrint(
        '🔐 [安全上传] 压缩率: ${originalResult.compressionRatio.toStringAsFixed(1)}%',
      );

      // 5. 生成文件路径（使用统一的fishing_app路径）
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final uuid = '${timestamp}_${_generateRandomString(8)}';

      final originalPath =
          'fishing_app/$userId/${timestamp}_$uuid.${originalResult.extension}';
      final thumbnailPath =
          'fishing_app/$userId/${timestamp}_${uuid}_thumb.${thumbnailResult.extension}';

      debugPrint('🔐 [安全上传] 原图路径: $originalPath');
      debugPrint('🔐 [安全上传] 缩略图路径: $thumbnailPath');

      // 6. 生成预签名URL
      debugPrint('🔐 [安全上传] 开始生成预签名URL...');

      final originalPresignedUrl = await _r2Service.generatePresignedUrl(
        objectKey: originalPath,
        method: 'PUT',
        expiresIn: 3600,
      );

      final thumbnailPresignedUrl = await _r2Service.generatePresignedUrl(
        objectKey: thumbnailPath,
        method: 'PUT',
        expiresIn: 3600,
      );

      if (originalPresignedUrl == null || thumbnailPresignedUrl == null) {
        debugPrint('❌ [安全上传] 生成预签名URL失败');
        return null;
      }

      debugPrint('✅ [安全上传] 预签名URL生成成功');

      // 7. 上传原图
      debugPrint('🔐 [安全上传] 开始上传原图...');
      final originalUploadSuccess = await _uploadToR2(
        presignedUrl: originalPresignedUrl,
        fileBytes: originalResult.imageBytes,
        contentType: 'image/${originalResult.extension}',
      );

      if (!originalUploadSuccess) {
        debugPrint('❌ [安全上传] 原图上传失败');
        return null;
      }

      debugPrint('✅ [安全上传] 原图上传成功');

      // 8. 上传缩略图
      debugPrint('🔐 [安全上传] 开始上传缩略图...');
      final thumbnailUploadSuccess = await _uploadToR2(
        presignedUrl: thumbnailPresignedUrl,
        fileBytes: thumbnailResult.imageBytes,
        contentType: 'image/${thumbnailResult.extension}',
      );

      if (!thumbnailUploadSuccess) {
        debugPrint('⚠️ [安全上传] 缩略图上传失败，但原图已成功');
        // 缩略图失败不影响主要功能
      } else {
        debugPrint('✅ [安全上传] 缩略图上传成功');
      }

      // 9. 获取 R2 凭据以构建公开URL
      final credentials = await _r2Service.getR2Credentials();
      if (credentials == null) {
        debugPrint('❌ [安全上传] 无法获取R2凭据构建公开URL');
        return null;
      }

      // 10. 构建公开访问URL
      final originalUrl =
          '${credentials.endpoint}/${credentials.bucketName}/$originalPath';
      final thumbnailUrl =
          thumbnailUploadSuccess
              ? '${credentials.endpoint}/${credentials.bucketName}/$thumbnailPath'
              : originalUrl;

      debugPrint('✅ [安全上传] 上传完成');
      debugPrint('🔐 [安全上传] 原图URL: $originalUrl');
      debugPrint('🔐 [安全上传] 缩略图URL: $thumbnailUrl');

      // 11. 返回结果
      return ImageUploadResult(
        originalUrl: originalUrl,
        thumbnailUrl: thumbnailUrl,
        fileName: path.basename(originalPath),
        fileSize: originalResult.compressedSize,
        width: originalResult.width,
        height: originalResult.height,
      );
    } catch (e) {
      debugPrint('❌ [安全上传] 上传异常: $e');
      return null;
    }
  }

  /// 上传到 R2
  Future<bool> _uploadToR2({
    required String presignedUrl,
    required Uint8List fileBytes,
    required String contentType,
  }) async {
    try {
      debugPrint('🔐 [R2上传] 开始上传文件');
      debugPrint('🔐 [R2上传] 文件大小: ${fileBytes.length} bytes');
      debugPrint('🔐 [R2上传] Content-Type: $contentType');
      debugPrint('🔐 [R2上传] URL: ${presignedUrl.substring(0, 100)}...');

      final response = await http.put(
        Uri.parse(presignedUrl),
        headers: {
          'Content-Type': contentType,
          'Content-Length': fileBytes.length.toString(),
        },
        body: fileBytes,
      );

      debugPrint('🔐 [R2上传] 响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint('✅ [R2上传] 上传成功');
        return true;
      } else {
        debugPrint('❌ [R2上传] 上传失败: ${response.statusCode}');
        debugPrint('❌ [R2上传] 响应体: ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [R2上传] 上传异常: $e');
      return false;
    }
  }

  /// 验证媒体文件（支持图片和视频）
  Future<bool> _validateImage(File mediaFile) async {
    try {
      // Web平台的特殊处理
      if (kIsWeb) {
        final extension = path
            .extension(mediaFile.path)
            .toLowerCase()
            .replaceAll('.', '');

        // 检查是否为支持的图片或视频格式
        final isImage = R2Config.allowedExtensions.contains(extension);
        final isVideo = R2Config.allowedVideoExtensions.contains(extension);

        if (!isImage && !isVideo) {
          debugPrint('❌ [验证] 不支持的媒体格式: $extension');
          return false;
        }
        return true;
      }

      // 移动平台的完整验证
      if (!await mediaFile.exists()) {
        debugPrint('❌ [验证] 文件不存在');
        return false;
      }

      final fileSize = await mediaFile.length();
      final extension = path
          .extension(mediaFile.path)
          .toLowerCase()
          .replaceAll('.', '');

      // 区分图片和视频的验证规则
      final isImage = R2Config.allowedExtensions.contains(extension);
      final isVideo = R2Config.allowedVideoExtensions.contains(extension);

      if (isImage) {
        // 图片文件验证
        if (fileSize > R2Config.maxImageSize) {
          debugPrint('❌ [验证] 图片文件过大: ${fileSize}bytes');
          return false;
        }
      } else if (isVideo) {
        // 视频文件验证
        if (fileSize > R2Config.maxVideoSize) {
          debugPrint('❌ [验证] 视频文件过大: ${fileSize}bytes');
          return false;
        }
      } else {
        debugPrint('❌ [验证] 不支持的媒体格式: $extension');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('❌ [验证] 图片验证异常: $e');
      return false;
    }
  }

  /// 生成随机字符串
  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    String result = '';

    for (int i = 0; i < length; i++) {
      result += chars[(random + i) % chars.length];
    }

    return result;
  }

  /// 上传视频文件（直接上传，不进行压缩处理）
  Future<ImageUploadResult?> _uploadVideoFile(
    File videoFile,
    String userId,
    String spotId,
  ) async {
    try {
      debugPrint('🎥 [视频上传] 开始上传视频文件');
      debugPrint('🎥 [视频上传] 文件路径: ${videoFile.path}');
      debugPrint('🎥 [视频上传] 文件大小: ${await videoFile.length()} bytes');

      // 1. 读取视频文件
      final videoBytes = await videoFile.readAsBytes();

      // 2. 生成文件路径
      final extension = path
          .extension(videoFile.path)
          .toLowerCase()
          .replaceAll('.', '');
      final videoPath = R2Config.generateImagePath(userId, spotId, extension);

      debugPrint('🎥 [视频上传] 生成存储路径: $videoPath');

      // 3. 生成预签名URL
      final presignedUrl = await _r2Service.generatePresignedUrl(
        objectKey: videoPath,
        method: 'PUT',
        expiresIn: 3600,
      );

      if (presignedUrl == null) {
        debugPrint('❌ [视频上传] 生成预签名URL失败');
        return null;
      }

      // 4. 上传视频文件
      final uploadSuccess = await _uploadToR2(
        presignedUrl: presignedUrl,
        fileBytes: videoBytes,
        contentType: 'video/$extension',
      );

      if (!uploadSuccess) {
        debugPrint('❌ [视频上传] 视频上传失败');
        return null;
      }

      // 5. 生成访问URL
      final videoUrl = '${R2Config.endpoint}/${R2Config.bucketName}/$videoPath';

      debugPrint('✅ [视频上传] 视频上传成功: $videoUrl');

      // 6. 返回结果（视频没有缩略图URL，使用相同的URL）
      return ImageUploadResult(
        originalUrl: videoUrl,
        thumbnailUrl: videoUrl, // 视频使用相同的URL
        fileName: path.basename(videoFile.path),
        fileSize: await videoFile.length(),
        width: 0, // 视频没有宽度概念，设为0
        height: 0, // 视频没有高度概念，设为0
      );
    } catch (e) {
      debugPrint('❌ [视频上传] 视频上传异常: $e');
      return null;
    }
  }
}
