import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';

import '../models/fishing_activity.dart';
import '../services/service_locator.dart';
import '../widgets/add_spot_form/image_upload_widget.dart';

/// 活动创建结果
class ActivityCreationResult {
  final bool success;
  final FishingActivity? activity;
  final String? errorMessage;
  final String? errorCode;

  const ActivityCreationResult({
    required this.success,
    this.activity,
    this.errorMessage,
    this.errorCode,
  });

  factory ActivityCreationResult.success(FishingActivity activity) {
    return ActivityCreationResult(
      success: true,
      activity: activity,
    );
  }

  factory ActivityCreationResult.failure(String message, [String? code]) {
    return ActivityCreationResult(
      success: false,
      errorMessage: message,
      errorCode: code,
    );
  }
}

/// 活动创建服务
/// 
/// 专门处理活动创建的业务逻辑，包括：
/// - 数据验证
/// - 图片处理
/// - 位置验证
/// - 活动创建
/// - 错误处理
class ActivityCreationService {
  /// 创建活动
  Future<ActivityCreationResult> createActivity({
    required FishingActivity activity,
    required List<ImageUploadItem> images,
    Position? publishLocation,
  }) async {
    try {
      debugPrint('🔍 [活动创建服务] 开始创建活动');
      debugPrint('🔍 [活动创建服务] 活动标题: ${activity.title}');
      debugPrint('🔍 [活动创建服务] 开始时间: ${activity.startTime}');
      debugPrint('🔍 [活动创建服务] 图片数量: ${images.length}');

      // 1. 验证用户登录状态
      final user = Services.auth.currentUser;
      if (user == null) {
        return ActivityCreationResult.failure('用户未登录', 'AUTH_REQUIRED');
      }

      // 2. 验证活动数据
      final validationResult = _validateActivityData(activity);
      if (!validationResult.success) {
        return validationResult;
      }

      // 3. 处理图片上传
      final imageProcessResult = await _processImages(images);
      if (!imageProcessResult.success) {
        return imageProcessResult;
      }

      // 4. 准备最终的活动数据
      final finalActivity = _prepareActivityData(
        activity, 
        imageProcessResult.activity!.images,
        publishLocation,
      );

      // 5. 调用服务创建活动
      final result = await Services.fishingActivity.addActivity(finalActivity);

      if (result != null) {
        debugPrint('✅ [活动创建服务] 活动创建成功: ${result.id}');
        return ActivityCreationResult.success(result);
      } else {
        return ActivityCreationResult.failure('活动创建失败，请重试', 'CREATE_FAILED');
      }

    } catch (e) {
      debugPrint('❌ [活动创建服务] 创建活动异常: $e');
      return _handleException(e);
    }
  }

  /// 验证活动数据
  ActivityCreationResult _validateActivityData(FishingActivity activity) {
    // 验证标题
    if (activity.title.trim().isEmpty) {
      return ActivityCreationResult.failure('请输入活动名称', 'TITLE_REQUIRED');
    }

    if (activity.title.trim().length > 100) {
      return ActivityCreationResult.failure('活动名称不能超过100个字符', 'TITLE_TOO_LONG');
    }

    // 验证描述长度
    if (activity.description.length > 500) {
      return ActivityCreationResult.failure('活动描述不能超过500个字符', 'DESCRIPTION_TOO_LONG');
    }

    // 验证时间
    if (activity.startTime.isBefore(DateTime.now())) {
      return ActivityCreationResult.failure('钓鱼时间不能是过去的时间', 'INVALID_TIME');
    }

    final maxFutureTime = DateTime.now().add(const Duration(days: 30));
    if (activity.startTime.isAfter(maxFutureTime)) {
      return ActivityCreationResult.failure('钓鱼时间不能超过30天后', 'TIME_TOO_FAR');
    }

    // 验证持续时间
    if (activity.duration <= 0 || activity.duration > 24) {
      return ActivityCreationResult.failure('持续时间必须在0.5-24小时之间', 'INVALID_DURATION');
    }

    // 验证位置
    if (activity.location == null) {
      return ActivityCreationResult.failure('活动位置信息缺失', 'LOCATION_REQUIRED');
    }

    // 验证参与人数
    if (activity.maxParticipants < 2 || activity.maxParticipants > 50) {
      return ActivityCreationResult.failure('参与人数必须在2-50人之间', 'INVALID_PARTICIPANTS');
    }

    return ActivityCreationResult.success(activity);
  }

  /// 处理图片
  Future<ActivityCreationResult> _processImages(List<ImageUploadItem> images) async {
    try {
      // 检查是否有图片正在上传
      final uploadingImages = images.where((img) => img.isUploading).toList();
      if (uploadingImages.isNotEmpty) {
        return ActivityCreationResult.failure(
          '还有${uploadingImages.length}张图片正在上传，请等待完成', 
          'IMAGES_UPLOADING'
        );
      }

      // 检查上传失败的图片
      final failedImages = images.where((img) => 
        !img.isCompleted && !img.isUploading && img.errorMessage != null
      ).toList();
      
      if (failedImages.isNotEmpty) {
        return ActivityCreationResult.failure(
          '有${failedImages.length}张图片上传失败，请重新上传', 
          'IMAGES_FAILED'
        );
      }

      // 获取成功上传的图片
      final completedImages = images
          .where((img) => img.isCompleted && img.uploadedUrl != null)
          .toList();

      // 准备图片数据
      Map<String, dynamic>? imagesData;
      if (completedImages.isNotEmpty) {
        imagesData = {
          'images': completedImages.map((img) => {
            'url': img.uploadedUrl,
            'thumbnail_url': img.thumbnailUrl,
            'is_from_camera': img.isFromCamera,
            'upload_time': DateTime.now().toIso8601String(),
          }).toList(),
          'count': completedImages.length,
          'total_size': completedImages.fold<int>(0, (sum, img) =>
            sum + img.file.lengthSync()
          ),
        };

        debugPrint('🔍 [活动创建服务] 处理了${completedImages.length}张图片');
      }

      // 创建包含图片数据的活动副本
      final activityWithImages = FishingActivity(
        id: '',
        title: '',
        description: '',
        location: null,
        startTime: DateTime.now(),
        duration: 0,
        maxParticipants: 0,
        currentParticipants: 0,
        creatorId: '',
        created: DateTime.now(),
        updated: DateTime.now(),
        images: imagesData,
      );

      return ActivityCreationResult.success(activityWithImages);

    } catch (e) {
      debugPrint('❌ [活动创建服务] 处理图片失败: $e');
      return ActivityCreationResult.failure('图片处理失败，请重试', 'IMAGE_PROCESS_FAILED');
    }
  }

  /// 准备最终的活动数据
  FishingActivity _prepareActivityData(
    FishingActivity activity,
    Map<String, dynamic>? imagesData,
    Position? publishLocation,
  ) {
    return FishingActivity(
      id: activity.id,
      title: activity.title.trim(),
      description: activity.description.trim().isEmpty 
          ? '${activity.title.trim()} - 一起去钓鱼'
          : activity.description.trim(),
      location: activity.location,
      startTime: activity.startTime,
      duration: activity.duration,
      maxParticipants: activity.maxParticipants,
      currentParticipants: activity.currentParticipants,
      creatorId: activity.creatorId,
      creatorName: activity.creatorName,
      status: 'active',
      images: imagesData,
      created: DateTime.now(),
      updated: DateTime.now(),
    );
  }

  /// 处理异常
  ActivityCreationResult _handleException(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('network') || errorString.contains('connection')) {
      return ActivityCreationResult.failure(
        '网络连接失败，请检查网络后重试', 
        'NETWORK_ERROR'
      );
    }
    
    if (errorString.contains('timeout')) {
      return ActivityCreationResult.failure(
        '请求超时，请重试', 
        'TIMEOUT_ERROR'
      );
    }
    
    if (errorString.contains('unauthorized') || errorString.contains('401')) {
      return ActivityCreationResult.failure(
        '登录已过期，请重新登录', 
        'AUTH_EXPIRED'
      );
    }
    
    if (errorString.contains('forbidden') || errorString.contains('403')) {
      return ActivityCreationResult.failure(
        '没有权限执行此操作', 
        'PERMISSION_DENIED'
      );
    }
    
    if (errorString.contains('server') || errorString.contains('500')) {
      return ActivityCreationResult.failure(
        '服务器错误，请稍后重试', 
        'SERVER_ERROR'
      );
    }
    
    // 默认错误
    return ActivityCreationResult.failure(
      '创建活动失败，请重试', 
      'UNKNOWN_ERROR'
    );
  }

  /// 获取友好的错误提示
  static String getFriendlyErrorMessage(String? errorCode, String? errorMessage) {
    switch (errorCode) {
      case 'AUTH_REQUIRED':
        return '请先登录后再创建活动';
      case 'TITLE_REQUIRED':
        return '请输入活动名称';
      case 'TITLE_TOO_LONG':
        return '活动名称太长了，请简化一下';
      case 'DESCRIPTION_TOO_LONG':
        return '活动描述太长了，请简化一下';
      case 'INVALID_TIME':
        return '钓鱼时间不能选择过去的时间';
      case 'TIME_TOO_FAR':
        return '钓鱼时间不能超过30天后';
      case 'INVALID_DURATION':
        return '持续时间请选择0.5-24小时之间';
      case 'LOCATION_REQUIRED':
        return '请选择活动位置';
      case 'INVALID_PARTICIPANTS':
        return '参与人数请选择2-50人之间';
      case 'IMAGES_UPLOADING':
        return '图片还在上传中，请稍等';
      case 'IMAGES_FAILED':
        return '有图片上传失败，请重新上传';
      case 'NETWORK_ERROR':
        return '网络连接失败，请检查网络';
      case 'TIMEOUT_ERROR':
        return '网络超时，请重试';
      case 'AUTH_EXPIRED':
        return '登录已过期，请重新登录';
      case 'PERMISSION_DENIED':
        return '权限不足，无法创建活动';
      case 'SERVER_ERROR':
        return '服务器繁忙，请稍后重试';
      default:
        return errorMessage ?? '创建活动失败，请重试';
    }
  }
}
