import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:latlong2/latlong.dart';

import '../config/pocketbase_config.dart';
import '../models/fishing_activity.dart';
import '../models/user.dart';
import '../models/marker_id_info.dart';
import '../models/unified_marker.dart';
import '../services/service_locator.dart';

/// 钓鱼活动服务
///
/// 管理"一起钓鱼"约钓活动的创建、查询、更新等操作
/// 复用钓点服务的核心逻辑，但简化验证要求
class FishingActivityService extends ChangeNotifier {
  static const String _cacheKey = 'fishing_activities_cache';
  static const Duration _cacheExpiry = Duration(minutes: 5);

  // 缓存相关
  List<FishingActivity> _activitiesCache = [];
  DateTime? _lastAllActivitiesLoadTime;
  final Map<String, List<FishingActivity>> _regionCache = {};
  final Map<String, DateTime> _regionCacheTime = {};

  // 数据版本号，用于追踪数据变化
  int _dataVersion = 0;
  int get dataVersion => _dataVersion;

  /// 获取当前用户
  User? get currentUser => Services.auth.currentUser;

  /// 获取PocketBase客户端
  PocketBase get pb => PocketBaseConfig.instance.client;

  /// 获取所有活动（带缓存）
  Future<List<FishingActivity>> getAllActivities({
    bool forceRefresh = false,
  }) async {
    final now = DateTime.now();

    // 检查缓存是否有效
    if (!forceRefresh &&
        _activitiesCache.isNotEmpty &&
        _lastAllActivitiesLoadTime != null &&
        now.difference(_lastAllActivitiesLoadTime!).inMinutes <
            _cacheExpiry.inMinutes) {
      debugPrint('🔍 [活动服务] 使用缓存的活动数据');
      return _filterActiveActivities(_activitiesCache);
    }

    debugPrint('🔍 [活动服务] 从服务器获取活动数据');

    try {
      // 从 PocketBase 获取活动数据
      final records = await pb
          .collection('fishing_activities')
          .getFullList(
            sort: '-created',
            expand: 'creator_id',
            filter: 'status = "active"', // 只获取活跃的活动
          );

      // 转换为FishingActivity对象并更新缓存
      _activitiesCache = _convertRecordsToActivities(records);
      _lastAllActivitiesLoadTime = now;

      debugPrint('🔍 [活动服务] 从后端获取到 ${records.length} 个活动记录');
      debugPrint('🔍 [活动服务] 转换后缓存中有 ${_activitiesCache.length} 个活动');

      // 打印所有活动的状态
      for (final activity in _activitiesCache) {
        debugPrint(
          '🔍 [活动服务] 缓存活动: ${activity.id} - ${activity.title} (状态: ${activity.status})',
        );
      }

      // 清理区域缓存，因为全局数据已更新
      _regionCache.clear();
      _regionCacheTime.clear();

      // 异步保存到本地
      _saveActivitiesToLocal().catchError((e) => debugPrint('保存活动数据失败: $e'));

      final filteredActivities = _filterActiveActivities(_activitiesCache);
      debugPrint('🔍 [活动服务] 过滤后返回 ${filteredActivities.length} 个活跃活动');

      return filteredActivities;
    } catch (e) {
      debugPrint('获取活动失败: $e');

      // 如果API调用失败但有缓存，返回缓存
      if (_activitiesCache.isNotEmpty) {
        debugPrint('使用过期的活动缓存数据');
        return _filterActiveActivities(_activitiesCache);
      }

      // 如果没有缓存，尝试从本地加载
      await _loadActivitiesFromLocal();
      return _filterActiveActivities(_activitiesCache);
    }
  }

  /// 过滤掉已过期的活动
  List<FishingActivity> _filterActiveActivities(
    List<FishingActivity> activities,
  ) {
    debugPrint('🔍 [活动服务] 开始过滤活跃活动，输入 ${activities.length} 个活动');

    final activeActivities = <FishingActivity>[];
    final inactiveActivities = <FishingActivity>[];

    for (final activity in activities) {
      if (activity.status == 'active' && !activity.isExpired) {
        activeActivities.add(activity);
        debugPrint('✅ [活动服务] 活跃活动: ${activity.id} - ${activity.title}');
      } else {
        inactiveActivities.add(activity);
        debugPrint(
          '❌ [活动服务] 非活跃活动: ${activity.id} - ${activity.title} (状态: ${activity.status}, 过期: ${activity.isExpired})',
        );
      }
    }

    debugPrint(
      '🔍 [活动服务] 过滤结果: ${activeActivities.length} 个活跃, ${inactiveActivities.length} 个非活跃',
    );
    return activeActivities;
  }

  /// 添加新活动
  Future<FishingActivity?> addActivity(FishingActivity activity) async {
    try {
      // 检查用户是否已登录
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      final userId = currentUser!.id;

      debugPrint('🔍 [活动服务] 准备创建钓鱼活动');
      debugPrint('🔍 [活动服务] 活动标题: ${activity.title}');
      debugPrint('🔍 [活动服务] 开始时间: ${activity.startTime}');
      debugPrint('🔍 [活动服务] 持续时长: ${activity.duration}小时');
      debugPrint('🔍 [活动服务] 用户ID: $userId');

      // 确保描述不为空
      final finalDescription =
          activity.description.trim().isEmpty
              ? '${activity.title} - 一起去钓鱼'
              : activity.description;

      debugPrint('🔍 [活动服务] 开始创建群聊');

      // 1. 先创建群聊
      debugPrint('🔍 [活动服务] 创建群聊，用户ID: $userId');
      final chatGroup = await pb
          .collection('activity_chat_groups')
          .create(
            body: {
              'name': '${activity.title} - 群聊', // 使用 name 而不是 group_name
              'creator_id': userId,
              'member_count': 1,
              'status': 'active',
              'description': '${activity.title}的活动群聊',
              'created_at': DateTime.now().toIso8601String(),
            },
          );

      final groupChatId = chatGroup.id;
      debugPrint('🔍 [活动服务] 群聊创建成功: $groupChatId');

      try {
        // 2. 创建活动记录，关联群聊ID
        debugPrint('🔍 [活动服务] 开始创建活动记录');
        final record = await pb
            .collection('fishing_activities')
            .create(
              body: {
                'creator_id': userId,
                'title': activity.title,
                'description': finalDescription,
                'location': activity.location,
                'start_time': activity.startTime.toIso8601String(),
                'duration': activity.duration,
                'max_participants': activity.maxParticipants,
                'current_participants': activity.currentParticipants,
                'status': activity.status,
                'images': activity.images,
                'group_chat_id': groupChatId,
              },
            );

        debugPrint('🔍 [活动服务] 活动记录创建成功: ${record.id}');

        // 3. 更新群聊记录，关联活动ID
        await pb
            .collection('activity_chat_groups')
            .update(groupChatId, body: {'activity_id': record.id});

        debugPrint('🔍 [活动服务] 群聊关联活动成功');

        // 4. 创建者自动参与活动
        try {
          await pb
              .collection('activity_participants')
              .create(
                body: {
                  'activity_id': record.id,
                  'user_id': userId,
                  'join_time': DateTime.now().toIso8601String(),
                  'status': 'joined',
                },
              );
          debugPrint('✅ [活动服务] 创建者已自动参与活动');
        } catch (e) {
          debugPrint('⚠️ [活动服务] 创建者自动参与失败: $e');
        }

        // 5. 创建者自动加入群聊
        try {
          await pb
              .collection('activity_chat_members')
              .create(
                body: {
                  'group_id': groupChatId,
                  'user_id': userId,
                  'join_time': DateTime.now().toIso8601String(),
                  'status': 'joined',
                },
              );
          debugPrint('✅ [活动服务] 创建者已自动加入群聊');
        } catch (e) {
          debugPrint('⚠️ [活动服务] 创建者自动加入群聊失败: $e');
        }

        // 获取完整的活动数据
        debugPrint('🔍 [活动服务] 获取完整活动数据: ${record.id}');
        final newActivity = await getActivityById(record.id);

        if (newActivity != null) {
          debugPrint('🔍 [活动服务] 获取完整数据成功');
          debugPrint('🔍 [活动服务] 最终返回的ID: ${newActivity.id}');

          // 更新缓存
          _activitiesCache.insert(0, newActivity);
          await _saveActivitiesToLocal();
        } else {
          debugPrint('❌ [活动服务] 获取完整数据失败');
        }

        debugPrint('✅ [活动服务] 添加活动成功: ${record.id}');
        return newActivity;
      } catch (e) {
        // 如果活动创建失败，删除已创建的群聊
        debugPrint('❌ [活动服务] 活动创建失败，清理群聊: $e');
        try {
          await pb.collection('activity_chat_groups').delete(groupChatId);
          debugPrint('🔍 [活动服务] 群聊清理成功');
        } catch (cleanupError) {
          debugPrint('❌ [活动服务] 群聊清理失败: $cleanupError');
        }
        rethrow;
      }
    } catch (e) {
      debugPrint('添加活动失败: $e');
      return null;
    }
  }

  /// 根据ID获取活动
  Future<FishingActivity?> getActivityById(String id) async {
    try {
      debugPrint('🔍 [活动服务] 开始获取活动详情: $id');

      final record = await pb
          .collection('fishing_activities')
          .getOne(id, expand: 'creator_id');

      debugPrint('✅ [活动服务] 成功获取活动记录: ${record.id}');
      final activity = _convertRecordToActivity(record);
      debugPrint('✅ [活动服务] 活动转换完成: ${activity.title}');

      return activity;
    } catch (e) {
      debugPrint('❌ [活动服务] 获取活动详情失败: $id, 错误: $e');

      // 检查是否是权限问题
      if (e.toString().contains('404')) {
        debugPrint('❌ [活动服务] 404错误 - 活动不存在或权限不足');
      } else if (e.toString().contains('403')) {
        debugPrint('❌ [活动服务] 403错误 - 权限被拒绝');
      }

      return null;
    }
  }

  /// 更新活动
  Future<bool> updateActivity(FishingActivity activity) async {
    try {
      // 检查用户是否已登录
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      // 先检查活动是否属于当前用户
      final existingRecord = await pb
          .collection('fishing_activities')
          .getOne(activity.id);

      if (existingRecord.data['creator_id'] != currentUser!.id) {
        throw Exception('无权限更新此活动');
      }

      // 更新活动数据
      await pb
          .collection('fishing_activities')
          .update(
            activity.id,
            body: {
              'title': activity.title,
              'description': activity.description,
              'location': activity.location,
              'start_time': activity.startTime.toIso8601String(),
              'duration': activity.duration,
              'max_participants': activity.maxParticipants,
              'current_participants': activity.currentParticipants,
              'status': activity.status,
              'images': activity.images,
            },
          );

      // 更新缓存
      final index = _activitiesCache.indexWhere((a) => a.id == activity.id);
      if (index != -1) {
        _activitiesCache[index] = activity;
        await _saveActivitiesToLocal();
      }

      debugPrint('更新活动成功: ${activity.id}');
      return true;
    } catch (e) {
      debugPrint('更新活动失败: $e');
      return false;
    }
  }

  /// 删除活动
  Future<bool> deleteActivity(String id) async {
    try {
      // 检查用户是否已登录
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      // 先检查活动是否属于当前用户
      final existingRecord = await pb
          .collection('fishing_activities')
          .getOne(id);

      if (existingRecord.data['creator_id'] != currentUser!.id) {
        throw Exception('无权限删除此活动');
      }

      final groupChatId = existingRecord.data['group_chat_id'];

      // 软删除：更新状态为cancelled
      await pb
          .collection('fishing_activities')
          .update(id, body: {'status': 'cancelled'});

      // 同时删除对应的群聊
      if (groupChatId != null) {
        try {
          await pb
              .collection('activity_chat_groups')
              .update(groupChatId, body: {'status': 'deleted'});
          debugPrint('🔍 [活动服务] 群聊删除成功: $groupChatId');
        } catch (e) {
          debugPrint('⚠️ [活动服务] 群聊删除失败: $e');
          // 群聊删除失败不影响活动删除
        }
      }

      // 从缓存中移除
      _activitiesCache.removeWhere((a) => a.id == id);
      await _saveActivitiesToLocal();

      // 通知数据变化
      _notifyDataChanged();

      debugPrint('删除活动成功: $id');
      return true;
    } catch (e) {
      debugPrint('删除活动失败: $e');
      return false;
    }
  }

  /// 根据地理位置获取活动
  Future<List<FishingActivity>> getActivitiesInRegion({
    required LatLng center,
    required double radiusKm,
  }) async {
    try {
      debugPrint('🔍 [活动服务] 获取区域内活动: center=$center, radius=${radiusKm}km');

      // 构建基础过滤条件（与钓点服务保持一致的格式）
      String filter = 'status = "active"';

      // 添加地理位置过滤（使用与钓点服务相同的语法）
      filter +=
          ' && geoDistance(location.lon, location.lat, ${center.longitude}, ${center.latitude}) <= $radiusKm';

      debugPrint(
        '🔍 [活动服务] 地理位置过滤条件: geoDistance(location.lon, location.lat, ${center.longitude}, ${center.latitude}) <= $radiusKm',
      );

      late final dynamic records;
      try {
        records = await pb
            .collection('fishing_activities')
            .getList(
              page: 1,
              perPage: 100,
              filter: filter,
              sort: 'start_time',
              expand: 'creator_id',
            );
      } catch (e) {
        debugPrint('❌ [活动服务] 地理查询失败，尝试使用简化查询: $e');
        // 降级方案：如果地理查询失败，只查询活跃状态的活动
        records = await pb
            .collection('fishing_activities')
            .getList(
              page: 1,
              perPage: 100,
              filter: 'status = "active"',
              sort: 'start_time',
              expand: 'creator_id',
            );
      }

      final activities = _convertRecordsToActivities(records.items);
      debugPrint('✅ [活动服务] 查询到 ${activities.length} 个区域内活动');

      // 详细输出每个活动的信息
      for (final activity in activities) {
        debugPrint('🔍 [活动服务] 活动详情: ${activity.id} - ${activity.title}');
        debugPrint('🔍 [活动服务] 活动位置: ${activity.location}');
        debugPrint('🔍 [活动服务] 活动类型: ${activity.activityType}');
        debugPrint('🔍 [活动服务] 活动状态: ${activity.status}');
        debugPrint('🔍 [活动服务] 开始时间: ${activity.startTime}');
      }

      return activities;
    } catch (e) {
      debugPrint('❌ [活动服务] 获取区域内活动失败: $e');
      return [];
    }
  }

  /// 获取区域内活动的ID和更新时间信息（第一步懒加载）
  ///
  /// 只查询id和updated字段，用于与缓存对比
  /// [center] 中心点坐标
  /// [radiusKm] 半径（公里）
  /// [limit] 最大返回数量，默认1000
  Future<List<MarkerIdInfo>> getRegionActivityIds({
    required LatLng center,
    required double radiusKm,
    int limit = 1000,
  }) async {
    try {
      debugPrint(
        '🔍 [活动ID查询] 开始查询区域内活动ID: center=$center, radius=${radiusKm}km',
      );

      // 构建基础过滤条件
      String filter = 'status = "active"';

      // 添加地理位置过滤
      filter +=
          ' && geoDistance(location.lon, location.lat, ${center.longitude}, ${center.latitude}) <= $radiusKm';

      debugPrint('🔍 [活动ID查询] 过滤条件: $filter');

      // 只查询id和updated字段，不需要expand
      final records = await pb
          .collection('fishing_activities')
          .getList(
            page: 1,
            perPage: limit,
            filter: filter,
            sort: '-updated', // 按更新时间排序
            fields: 'id,updated', // 只查询需要的字段
          );

      final List<MarkerIdInfo> result = [];
      for (final record in records.items) {
        try {
          result.add(
            MarkerIdInfo(
              id: record.id,
              updated: DateTime.parse(record.data['updated']),
              type: MarkerType.activity,
            ),
          );
        } catch (e) {
          debugPrint('⚠️ [活动ID查询] 解析记录失败: ${record.id}, 错误: $e');
        }
      }

      debugPrint('✅ [活动ID查询] 查询完成，获取到 ${result.length} 个活动ID');
      return result;
    } catch (e) {
      debugPrint('❌ [活动ID查询] 查询失败: $e');
      return [];
    }
  }

  /// 批量获取活动摘要信息（第二步懒加载）
  ///
  /// 根据ID列表获取用于显示和排序的摘要信息
  /// [activityIds] 活动ID列表
  Future<List<ActivityMarker>> getActivitySummaries(
    List<String> activityIds,
  ) async {
    if (activityIds.isEmpty) return [];

    try {
      debugPrint('🔍 [活动摘要查询] 开始批量查询活动摘要: ${activityIds.length} 个活动');

      final currentUser = Services.auth.currentUser;
      final currentUserId = currentUser?.id ?? '';

      // 构建ID过滤条件
      final idFilter = activityIds.map((id) => "id = '$id'").join(' || ');

      // 构建完整过滤条件
      String filter = '($idFilter) && status = "active"';

      debugPrint('🔍 [活动摘要查询] 过滤条件: $filter');

      // 查询摘要信息，包含排序和过滤所需的字段
      final records = await pb
          .collection('fishing_activities')
          .getList(
            page: 1,
            perPage: activityIds.length,
            filter: filter,
            sort: '-updated',
            // 只查询摘要所需的字段，不包含详细信息
            fields:
                'id,title,location,creator_id,start_time,created,updated,status,activity_type,current_participants,max_participants,images',
          );

      final List<ActivityMarker> result = [];
      for (final record in records.items) {
        try {
          // 解析位置信息
          final locationData = record.data['location'] as Map<String, dynamic>?;
          if (locationData == null) continue;

          final location = LatLng(
            locationData['lat'] as double,
            locationData['lon'] as double,
          );

          // 检查是否有图片
          final imagesData = record.data['images'];
          bool hasImages = false;
          String? thumbnailUrl;

          if (imagesData != null && imagesData is Map<String, dynamic>) {
            final imagesList = imagesData['images'];
            if (imagesList is List && imagesList.isNotEmpty) {
              hasImages = true;
              // 获取第一张图片作为缩略图
              final firstImage = imagesList.first;
              if (firstImage is Map<String, dynamic>) {
                final imageUrl =
                    firstImage['url'] ?? firstImage['thumbnail_url'];
                if (imageUrl != null) {
                  thumbnailUrl = imageUrl;
                }
              }
            }
          }

          result.add(
            ActivityMarker(
              id: record.id,
              name: record.data['title'] ?? '',
              location: location,
              userId: record.data['creator_id'] ?? '',
              created: DateTime.parse(record.data['created']),
              updated: DateTime.parse(record.data['updated']),
              status: record.data['status'] ?? 'active',
              activityType: record.data['activity_type'] ?? '',
              startTime: DateTime.parse(record.data['start_time']),
              currentParticipants: record.data['current_participants'] ?? 0,
              maxParticipants: record.data['max_participants'] ?? 0,
              hasImages: hasImages,
              thumbnailUrl: thumbnailUrl,
              isMyActivity: record.data['creator_id'] == currentUserId,
              isJoined: false, // TODO: 需要查询用户参与状态
              isFavorited: false, // TODO: 需要查询用户收藏状态
            ),
          );
        } catch (e) {
          debugPrint('⚠️ [活动摘要查询] 解析记录失败: ${record.id}, 错误: $e');
        }
      }

      debugPrint('✅ [活动摘要查询] 查询完成，获取到 ${result.length} 个活动摘要');
      return result;
    } catch (e) {
      debugPrint('❌ [活动摘要查询] 查询失败: $e');
      return [];
    }
  }

  /// 转换记录为活动对象
  FishingActivity _convertRecordToActivity(dynamic record) {
    try {
      debugPrint('🔍 [活动服务] 开始转换记录: ${record.id}');

      final data = record.data;
      final expand = record.expand;

      debugPrint('🔍 [活动服务] 原始data类型: ${data.runtimeType}');
      debugPrint('🔍 [活动服务] data是否为Map: ${data is Map}');

      // 获取创建者信息
      String? creatorName;
      try {
        if (expand != null && expand['creator_id'] != null) {
          final userData = expand['creator_id'];
          creatorName = userData['username'] ?? userData['name'];
        }
        debugPrint('🔍 [活动服务] 创建者信息获取完成: $creatorName');
      } catch (e) {
        debugPrint('⚠️ [活动服务] 获取创建者信息失败: $e');
      }

      // 安全地处理数据，确保所有字段都是正确的类型
      final safeData = <String, dynamic>{};

      try {
        // 逐个复制字段，避免类型转换问题
        if (data is Map) {
          for (final entry in data.entries) {
            safeData[entry.key.toString()] = entry.value;
          }
        } else {
          debugPrint('❌ [活动服务] data不是Map类型: ${data.runtimeType}');
          throw Exception('Record data is not a Map');
        }
        safeData['creator_name'] = creatorName;
        debugPrint('🔍 [活动服务] 数据复制完成');
      } catch (e) {
        debugPrint('❌ [活动服务] 数据复制失败: $e');
        rethrow;
      }

      // 特别处理可能有问题的字段
      if (safeData['images'] != null) {
        debugPrint('🔍 [活动服务] 原始images数据: ${safeData['images']}');
        debugPrint('🔍 [活动服务] images数据类型: ${safeData['images'].runtimeType}');
      }

      // 添加更详细的调试信息
      debugPrint('🔍 [活动服务] 准备转换活动数据: ${safeData.keys}');
      debugPrint(
        '🔍 [活动服务] created字段: ${safeData['created']} (${safeData['created'].runtimeType})',
      );
      debugPrint(
        '🔍 [活动服务] updated字段: ${safeData['updated']} (${safeData['updated'].runtimeType})',
      );
      debugPrint(
        '🔍 [活动服务] start_time字段: ${safeData['start_time']} (${safeData['start_time'].runtimeType})',
      );

      return FishingActivity.fromJson(safeData);
    } catch (e) {
      debugPrint('❌ [活动服务] 转换活动记录失败: $e');
      debugPrint('❌ [活动服务] 错误堆栈: ${StackTrace.current}');
      debugPrint('❌ [活动服务] 记录数据: $record');
      rethrow;
    }
  }

  /// 转换记录列表为活动对象列表
  List<FishingActivity> _convertRecordsToActivities(List<dynamic> records) {
    final activities = <FishingActivity>[];

    for (final record in records) {
      try {
        final activity = _convertRecordToActivity(record);
        activities.add(activity);
      } catch (e) {
        debugPrint('⚠️ [活动服务] 跳过无法转换的活动记录: $e');
        // 继续处理其他记录，不让一个错误影响整个列表
        continue;
      }
    }

    return activities;
  }

  /// 保存活动到本地缓存
  Future<void> _saveActivitiesToLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final activitiesJson = _activitiesCache.map((a) => a.toJson()).toList();
      await prefs.setString(_cacheKey, jsonEncode(activitiesJson));
      await prefs.setString(
        '${_cacheKey}_time',
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      debugPrint('保存活动缓存失败: $e');
    }
  }

  /// 从本地缓存加载活动
  Future<void> _loadActivitiesFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final activitiesJsonString = prefs.getString(_cacheKey);
      final cacheTimeString = prefs.getString('${_cacheKey}_time');

      if (activitiesJsonString != null && cacheTimeString != null) {
        final cacheTime = DateTime.parse(cacheTimeString);
        final now = DateTime.now();

        // 检查缓存是否过期
        if (now.difference(cacheTime).inMinutes < _cacheExpiry.inMinutes * 2) {
          final activitiesJson = jsonDecode(activitiesJsonString) as List;
          _activitiesCache =
              activitiesJson
                  .map((json) => FishingActivity.fromJson(json))
                  .toList();
          debugPrint('从本地缓存加载了 ${_activitiesCache.length} 个活动');
        }
      }
    } catch (e) {
      debugPrint('加载活动缓存失败: $e');
    }
  }

  /// 清理过期活动（定期调用）
  Future<void> cleanupExpiredActivities() async {
    try {
      final now = DateTime.now();

      // 获取所有活跃的活动并检查是否过期
      final activeActivities = await pb
          .collection('fishing_activities')
          .getFullList(filter: 'status = "active"');

      // 更新过期的活动状态
      for (final record in activeActivities) {
        final data = record.data;
        if (data['start_time'] != null && data['duration'] != null) {
          final startTime = DateTime.parse(data['start_time']);
          final duration = (data['duration'] as num).toDouble();
          final endTime = startTime.add(
            Duration(
              hours: duration.toInt(),
              minutes: ((duration % 1) * 60).toInt(),
            ),
          );

          if (now.isAfter(endTime)) {
            await pb
                .collection('fishing_activities')
                .update(record.id, body: {'status': 'completed'});
          }
        }
      }

      // 清理本地缓存
      _activitiesCache.removeWhere((activity) => activity.isExpired);
      await _saveActivitiesToLocal();

      debugPrint('清理过期活动完成');
    } catch (e) {
      debugPrint('清理过期活动失败: $e');
    }
  }

  // ========== 用户参与活动相关方法 ==========

  /// 检查用户是否已参与活动
  Future<bool> isUserParticipating(String activityId, String userId) async {
    try {
      final records = await pb
          .collection('activity_participants')
          .getList(
            page: 1,
            perPage: 1,
            filter:
                'activity_id = "$activityId" && user_id = "$userId" && status = "joined"',
          );

      return records.items.isNotEmpty;
    } catch (e) {
      debugPrint('❌ [活动服务] 检查参与状态失败: $e');
      return false;
    }
  }

  /// 用户加入活动
  Future<void> joinActivity(String activityId, String userId) async {
    try {
      debugPrint('🔍 [活动服务] 用户加入活动: $activityId, $userId');

      // 获取活动信息
      final activity = await getActivityById(activityId);
      if (activity == null) {
        debugPrint('❌ [活动服务] 无法获取活动详情: $activityId');

        // 检查是否存在孤立的参与者记录并清理
        await _cleanupOrphanedParticipants(activityId);

        throw Exception('活动不存在或已被删除');
      }

      // 检查是否是创建者（创建者不能重复加入）
      if (activity.creatorId == userId) {
        throw Exception('您是活动创建者，已自动参与活动');
      }

      // 检查用户是否已经参与
      final isAlreadyParticipating = await isUserParticipating(
        activityId,
        userId,
      );
      if (isAlreadyParticipating) {
        throw Exception('您已经参与了这个活动');
      }

      // 检查用户是否曾被踢出
      debugPrint('🔍 [活动服务] 检查用户是否曾被踢出: $userId');
      final kickedRecords = await pb
          .collection('activity_participants')
          .getList(
            page: 1,
            perPage: 1,
            filter:
                'activity_id = "$activityId" && user_id = "$userId" && status = "kicked"',
          );

      if (kickedRecords.items.isNotEmpty) {
        debugPrint('❌ [活动服务] 用户曾被踢出，禁止重新加入');
        throw Exception('您已被移出此活动，无法重新加入');
      }

      debugPrint('✅ [活动服务] 用户未被踢出，可以加入活动');

      // 检查活动是否已满员
      if (activity.currentParticipants >= activity.maxParticipants) {
        throw Exception('活动人数已满');
      }

      // 检查活动是否已过期
      if (activity.isExpired) {
        throw Exception('活动已结束');
      }

      // 创建参与记录
      await pb
          .collection('activity_participants')
          .create(
            body: {
              'activity_id': activityId,
              'user_id': userId,
              'join_time': DateTime.now().toIso8601String(),
              'status': 'joined',
            },
          );

      // 更新活动的参与人数（临时跳过，避免权限问题）
      debugPrint('🔍 [活动服务] 准备更新活动参与人数');
      debugPrint('🔍 [活动服务] 活动ID: $activityId');
      debugPrint('🔍 [活动服务] 活动创建者: ${activity.creatorId}');
      debugPrint('🔍 [活动服务] 当前用户: $userId');
      debugPrint('🔍 [活动服务] 当前参与人数: ${activity.currentParticipants}');

      // 只有创建者才能更新活动记录
      if (activity.creatorId == userId) {
        try {
          await pb
              .collection('fishing_activities')
              .update(
                activityId,
                body: {
                  'current_participants': activity.currentParticipants + 1,
                },
              );
          debugPrint('✅ [活动服务] 活动参与人数更新成功');
        } catch (e) {
          debugPrint('❌ [活动服务] 更新活动参与人数失败: $e');
          // 不抛出异常，允许加入活动继续进行
        }
      } else {
        debugPrint('⚠️ [活动服务] 非创建者无法更新活动记录，跳过参与人数更新');
        debugPrint('💡 [活动服务] 建议：修改后端权限规则允许更新 current_participants 字段');
      }

      // 如果活动有群聊，将用户加入群聊
      if (activity.groupChatId != null) {
        try {
          // 先检查群聊记录是否存在
          final groupRecord = await pb
              .collection('activity_chat_groups')
              .getOne(activity.groupChatId!);

          // 创建群聊成员记录
          await pb
              .collection('activity_chat_members')
              .create(
                body: {
                  'group_id': activity.groupChatId,
                  'user_id': userId,
                  'join_time': DateTime.now().toIso8601String(),
                  'status': 'joined',
                },
              );

          // 更新群聊成员数量
          final currentMemberCount = groupRecord.data['member_count'] ?? 0;
          await pb
              .collection('activity_chat_groups')
              .update(
                activity.groupChatId!,
                body: {'member_count': currentMemberCount + 1},
              );

          debugPrint('✅ [活动服务] 用户已加入群聊');
        } catch (e) {
          debugPrint('⚠️ [活动服务] 加入群聊失败: $e');

          // 如果是群聊记录不存在，说明数据不一致
          if (e.toString().contains('404')) {
            debugPrint('⚠️ [活动服务] 群聊记录不存在，可能是数据不一致问题');
            debugPrint(
              '💡 [活动服务] 建议：检查活动 ${activity.id} 的群聊记录 ${activity.groupChatId}',
            );

            // 可以考虑清空活动的 group_chat_id 字段（只有创建者可以操作）
            try {
              if (activity.creatorId == userId) {
                await pb
                    .collection('fishing_activities')
                    .update(activity.id, body: {'group_chat_id': null});
                debugPrint('🔧 [活动服务] 已清空无效的群聊ID');
              } else {
                debugPrint('💡 [活动服务] 非创建者无法清空群聊ID，建议联系活动创建者');
              }
            } catch (clearError) {
              debugPrint('⚠️ [活动服务] 清空群聊ID失败: $clearError');
            }
          }

          // 群聊加入失败不影响活动参与
        }
      }

      // 清理缓存
      _activitiesCache.clear();
      _regionCache.clear();

      debugPrint('✅ [活动服务] 用户成功加入活动');
    } catch (e) {
      debugPrint('❌ [活动服务] 加入活动失败: $e');
      rethrow;
    }
  }

  /// 用户退出活动
  Future<void> leaveActivity(String activityId, String userId) async {
    try {
      debugPrint('🔍 [活动服务] 用户退出活动: $activityId, $userId');

      // 检查用户是否参与了活动
      final participantRecords = await pb
          .collection('activity_participants')
          .getList(
            page: 1,
            perPage: 1,
            filter:
                'activity_id = "$activityId" && user_id = "$userId" && status = "joined"',
          );

      if (participantRecords.items.isEmpty) {
        throw Exception('您没有参与这个活动');
      }

      // 获取活动信息
      final activity = await getActivityById(activityId);
      if (activity == null) {
        throw Exception('活动不存在');
      }

      // 检查是否是活动创建者
      if (activity.creatorId == userId) {
        throw Exception('活动创建者不能退出活动');
      }

      // 更新参与记录状态
      final participantRecord = participantRecords.items.first;
      await pb
          .collection('activity_participants')
          .update(participantRecord.id, body: {'status': 'left'});

      // 更新活动的参与人数（只有创建者才能更新）
      if (activity.creatorId == userId) {
        try {
          await pb
              .collection('fishing_activities')
              .update(
                activityId,
                body: {
                  'current_participants': activity.currentParticipants - 1,
                },
              );
          debugPrint('✅ [活动服务] 活动参与人数更新成功');
        } catch (e) {
          debugPrint('❌ [活动服务] 更新活动参与人数失败: $e');
          // 不抛出异常，允许退出活动继续进行
        }
      } else {
        debugPrint('⚠️ [活动服务] 非创建者无法更新活动记录，跳过参与人数更新');
      }

      // 如果活动有群聊，将用户从群聊中移除
      if (activity.groupChatId != null) {
        try {
          // 先检查群聊记录是否存在
          final groupRecord = await pb
              .collection('activity_chat_groups')
              .getOne(activity.groupChatId!);

          final chatMemberRecords = await pb
              .collection('activity_chat_members')
              .getList(
                page: 1,
                perPage: 1,
                filter:
                    'group_id = "${activity.groupChatId}" && user_id = "$userId" && status = "joined"',
              );

          if (chatMemberRecords.items.isNotEmpty) {
            await pb
                .collection('activity_chat_members')
                .update(
                  chatMemberRecords.items.first.id,
                  body: {'status': 'left'},
                );

            // 更新群聊成员数量
            final currentMemberCount = groupRecord.data['member_count'] ?? 0;
            await pb
                .collection('activity_chat_groups')
                .update(
                  activity.groupChatId!,
                  body: {'member_count': math.max(0, currentMemberCount - 1)},
                );

            debugPrint('✅ [活动服务] 用户已退出群聊');
          }
        } catch (e) {
          debugPrint('⚠️ [活动服务] 退出群聊失败: $e');

          // 如果是群聊记录不存在，记录但不影响退出活动
          if (e.toString().contains('404')) {
            debugPrint('⚠️ [活动服务] 群聊记录不存在，可能已被删除');
          }

          // 群聊退出失败不影响活动退出
        }
      }

      // 清理缓存
      _activitiesCache.clear();
      _regionCache.clear();

      debugPrint('✅ [活动服务] 用户成功退出活动');
    } catch (e) {
      debugPrint('❌ [活动服务] 退出活动失败: $e');
      rethrow;
    }
  }

  /// 获取活动参与者列表
  Future<List<Map<String, dynamic>>> getActivityParticipants(
    String activityId,
  ) async {
    try {
      debugPrint('🔍 [活动服务] 获取活动参与者: $activityId');

      // 获取所有参与者记录，包括已加入和被踢出的
      final records = await pb
          .collection('activity_participants')
          .getList(
            page: 1,
            perPage: 100,
            filter:
                'activity_id = "$activityId" && (status = "joined" || status = "kicked")',
            expand: 'user_id',
            sort: '-join_time', // 按加入时间倒序
          );

      final participants = <Map<String, dynamic>>[];
      for (final record in records.items) {
        try {
          // 使用新的 PocketBase API 获取扩展的用户数据
          final userData = record.get<RecordModel?>('expand.user_id');
          if (userData != null) {
            participants.add({
              'id': userData.id,
              'username': userData.getStringValue('username', '未知用户'),
              'avatar': userData.getStringValue('avatar', ''),
              'joined_at': record.getStringValue('join_time', ''),
              'status': record.getStringValue('status', 'joined'),
              'kicked_at': record.getStringValue('kicked_at', ''),
              'kicked_by': record.getStringValue('kicked_by', ''),
            });
          }
        } catch (e) {
          debugPrint('⚠️ [活动服务] 解析参与者数据失败: $e');
          continue;
        }
      }

      debugPrint('✅ [活动服务] 获取到 ${participants.length} 个参与者（包含被踢出的）');
      return participants;
    } catch (e) {
      debugPrint('❌ [活动服务] 获取参与者失败: $e');
      return [];
    }
  }

  /// 踢出参与者（仅活动创建者可操作）
  Future<void> kickParticipant(
    String activityId,
    String participantUserId,
    String operatorUserId,
  ) async {
    try {
      debugPrint(
        '🔍 [活动服务] 踢出参与者: $activityId, $participantUserId, 操作者: $operatorUserId',
      );

      // 获取活动信息
      final activity = await getActivityById(activityId);
      if (activity == null) {
        throw Exception('活动不存在');
      }

      // 检查操作权限：只有活动创建者可以踢出参与者
      if (activity.creatorId != operatorUserId) {
        throw Exception('只有活动创建者可以管理参与者');
      }

      // 不能踢出自己
      if (participantUserId == operatorUserId) {
        throw Exception('不能踢出自己');
      }

      // 检查被踢用户是否参与了活动
      debugPrint('🔍 [活动服务] 查找参与者记录: 活动=$activityId, 用户=$participantUserId');
      final participantRecords = await pb
          .collection('activity_participants')
          .getList(
            page: 1,
            perPage: 1,
            filter:
                'activity_id = "$activityId" && user_id = "$participantUserId" && status = "joined"',
          );

      if (participantRecords.items.isEmpty) {
        debugPrint('⚠️ [活动服务] 未找到参与者记录，可能用户已退出或被踢出');
        throw Exception('该用户未参与此活动或已被移除');
      }

      debugPrint('✅ [活动服务] 找到参与者记录: ${participantRecords.items.first.id}');

      // 更新参与记录状态为被踢出
      final participantRecord = participantRecords.items.first;
      debugPrint('🔍 [活动服务] 准备更新参与者记录: ${participantRecord.id}');

      try {
        await pb
            .collection('activity_participants')
            .update(
              participantRecord.id,
              body: {
                'status': 'kicked',
                // 可以添加踢出时间和操作者信息
                'kicked_at': DateTime.now().toIso8601String(),
                'kicked_by': operatorUserId,
              },
            );
        debugPrint('✅ [活动服务] 参与者记录更新成功');
      } catch (updateError) {
        debugPrint('❌ [活动服务] 更新参与者记录失败: $updateError');

        // 如果记录不存在，可能已经被删除，直接跳过
        if (updateError.toString().contains('404')) {
          debugPrint('⚠️ [活动服务] 参与者记录不存在，可能已被删除，跳过更新');
        } else {
          // 其他错误重新抛出
          rethrow;
        }
      }

      // 更新活动的参与人数（创建者执行踢出操作）
      debugPrint(
        '🔍 [活动服务] 准备更新活动参与人数: ${activity.currentParticipants} -> ${activity.currentParticipants - 1}',
      );
      try {
        await pb
            .collection('fishing_activities')
            .update(
              activityId,
              body: {'current_participants': activity.currentParticipants - 1},
            );
        debugPrint('✅ [活动服务] 活动参与人数更新成功');
      } catch (e) {
        debugPrint('❌ [活动服务] 更新活动参与人数失败: $e');
        // 踢出操作由创建者执行，应该有权限更新，如果失败记录详细信息
        if (e.toString().contains('403')) {
          debugPrint(
            '❌ [活动服务] 权限被拒绝，检查创建者身份: 操作者=$operatorUserId, 创建者=${activity.creatorId}',
          );
        }
        // 不抛出异常，允许踢出操作继续进行
      }

      // 如果活动有群聊，将用户从群聊中移除
      if (activity.groupChatId != null) {
        try {
          // 先检查群聊记录是否存在
          final groupRecord = await pb
              .collection('activity_chat_groups')
              .getOne(activity.groupChatId!);

          final chatMemberRecords = await pb
              .collection('activity_chat_members')
              .getList(
                page: 1,
                perPage: 1,
                filter:
                    'group_id = "${activity.groupChatId}" && user_id = "$participantUserId" && status = "joined"',
              );

          if (chatMemberRecords.items.isNotEmpty) {
            await pb
                .collection('activity_chat_members')
                .update(
                  chatMemberRecords.items.first.id,
                  body: {
                    'status': 'kicked',
                    'kicked_at': DateTime.now().toIso8601String(),
                    'kicked_by': operatorUserId,
                  },
                );

            // 更新群聊成员数量
            final currentMemberCount = groupRecord.data['member_count'] ?? 0;
            await pb
                .collection('activity_chat_groups')
                .update(
                  activity.groupChatId!,
                  body: {'member_count': math.max(0, currentMemberCount - 1)},
                );

            debugPrint('✅ [活动服务] 用户已被移出群聊');
          }
        } catch (e) {
          debugPrint('⚠️ [活动服务] 移出群聊失败: $e');

          // 如果是群聊记录不存在，记录但不影响踢出操作
          if (e.toString().contains('404')) {
            debugPrint('⚠️ [活动服务] 群聊记录不存在，可能已被删除');
          }

          // 群聊移除失败不影响踢出操作
        }
      }

      // 清理缓存并通知数据变化
      _activitiesCache.clear();
      _regionCache.clear();
      _notifyDataChanged();

      debugPrint('✅ [活动服务] 成功踢出参与者: $participantUserId');
      debugPrint('🔄 [活动服务] 已清理缓存并通知数据变化');
    } catch (e) {
      debugPrint('❌ [活动服务] 踢出参与者失败: $e');
      rethrow;
    }
  }

  /// 清理缓存并通知数据变化
  void clearCache() {
    _activitiesCache.clear();
    _regionCache.clear();
    _dataVersion++;
    debugPrint('✅ [活动服务] 缓存已清理，数据版本: v$_dataVersion');

    // 通知监听者数据已变化
    notifyListeners();
  }

  /// 通知数据变化（用于删除、更新操作后）
  void _notifyDataChanged() {
    _dataVersion++;
    debugPrint('📢 [活动服务] 数据变化通知，版本: v$_dataVersion');
    notifyListeners();
  }

  /// 清理孤立的参与者记录（当活动不存在时）
  Future<void> _cleanupOrphanedParticipants(String activityId) async {
    try {
      debugPrint('🧹 [活动服务] 开始清理孤立的参与者记录: $activityId');

      // 查找该活动的所有参与者记录
      final participantRecords = await pb
          .collection('activity_participants')
          .getList(
            page: 1,
            perPage: 100,
            filter: 'activity_id = "$activityId"',
          );

      if (participantRecords.items.isNotEmpty) {
        debugPrint('🧹 [活动服务] 发现 ${participantRecords.items.length} 个孤立的参与者记录');

        // 删除所有孤立的参与者记录
        for (final record in participantRecords.items) {
          try {
            await pb.collection('activity_participants').delete(record.id);
            debugPrint('🧹 [活动服务] 已删除孤立参与者记录: ${record.id}');
          } catch (e) {
            debugPrint('⚠️ [活动服务] 删除孤立参与者记录失败: ${record.id}, $e');
          }
        }

        // 同时清理相关的群聊成员记录
        await _cleanupOrphanedChatMembers(activityId);

        debugPrint('✅ [活动服务] 孤立参与者记录清理完成');
      } else {
        debugPrint('🧹 [活动服务] 未发现孤立的参与者记录');
      }
    } catch (e) {
      debugPrint('❌ [活动服务] 清理孤立参与者记录失败: $e');
    }
  }

  /// 清理孤立的群聊成员记录
  Future<void> _cleanupOrphanedChatMembers(String activityId) async {
    try {
      // 查找与该活动相关的群聊记录
      final chatGroupRecords = await pb
          .collection('activity_chat_groups')
          .getList(page: 1, perPage: 10, filter: 'activity_id = "$activityId"');

      for (final groupRecord in chatGroupRecords.items) {
        final groupId = groupRecord.id;

        // 查找该群聊的所有成员记录
        final memberRecords = await pb
            .collection('activity_chat_members')
            .getList(page: 1, perPage: 100, filter: 'group_id = "$groupId"');

        // 删除所有成员记录
        for (final memberRecord in memberRecords.items) {
          try {
            await pb
                .collection('activity_chat_members')
                .delete(memberRecord.id);
            debugPrint('🧹 [活动服务] 已删除孤立群聊成员记录: ${memberRecord.id}');
          } catch (e) {
            debugPrint('⚠️ [活动服务] 删除孤立群聊成员记录失败: ${memberRecord.id}, $e');
          }
        }

        // 删除群聊记录
        try {
          await pb.collection('activity_chat_groups').delete(groupId);
          debugPrint('🧹 [活动服务] 已删除孤立群聊记录: $groupId');
        } catch (e) {
          debugPrint('⚠️ [活动服务] 删除孤立群聊记录失败: $groupId, $e');
        }
      }
    } catch (e) {
      debugPrint('❌ [活动服务] 清理孤立群聊记录失败: $e');
    }
  }

  /// 动态计算活动的实际参与人数
  Future<int> getActualParticipantCount(String activityId) async {
    try {
      final participantRecords = await pb
          .collection('activity_participants')
          .getList(
            page: 1,
            perPage: 500, // 假设单个活动不会超过500人
            filter: 'activity_id = "$activityId" && status = "joined"',
          );

      final actualCount = participantRecords.items.length;
      debugPrint('🔍 [活动服务] 活动 $activityId 实际参与人数: $actualCount');
      return actualCount;
    } catch (e) {
      debugPrint('❌ [活动服务] 计算参与人数失败: $e');
      return 0;
    }
  }

  /// 修复活动的参与人数（仅创建者可调用）
  Future<void> fixParticipantCount(
    String activityId,
    String operatorUserId,
  ) async {
    try {
      // 获取活动信息
      final activity = await getActivityById(activityId);
      if (activity == null) {
        throw Exception('活动不存在');
      }

      // 检查权限
      if (activity.creatorId != operatorUserId) {
        throw Exception('只有活动创建者可以修复参与人数');
      }

      // 计算实际参与人数
      final actualCount = await getActualParticipantCount(activityId);

      // 更新活动记录
      await pb
          .collection('fishing_activities')
          .update(activityId, body: {'current_participants': actualCount});

      debugPrint('✅ [活动服务] 参与人数修复完成: $actualCount');
    } catch (e) {
      debugPrint('❌ [活动服务] 修复参与人数失败: $e');
      rethrow;
    }
  }

  /// 数据一致性检查和清理（可定期调用）
  Future<void> performDataConsistencyCheck() async {
    try {
      debugPrint('🔍 [活动服务] 开始数据一致性检查');

      // 获取所有参与者记录
      final allParticipants =
          await pb.collection('activity_participants').getFullList();

      // 检查每个参与者记录对应的活动是否存在
      final orphanedActivityIds = <String>{};

      for (final participant in allParticipants) {
        final activityId = participant.data['activity_id'] as String?;
        if (activityId != null && !orphanedActivityIds.contains(activityId)) {
          try {
            await pb.collection('fishing_activities').getOne(activityId);
          } catch (e) {
            if (e.toString().contains('404')) {
              orphanedActivityIds.add(activityId);
              debugPrint('🔍 [活动服务] 发现孤立活动ID: $activityId');
            }
          }
        }
      }

      // 清理所有孤立的记录
      for (final activityId in orphanedActivityIds) {
        await _cleanupOrphanedParticipants(activityId);
      }

      if (orphanedActivityIds.isNotEmpty) {
        debugPrint(
          '✅ [活动服务] 数据一致性检查完成，清理了 ${orphanedActivityIds.length} 个孤立活动的相关记录',
        );
      } else {
        debugPrint('✅ [活动服务] 数据一致性检查完成，未发现问题');
      }
    } catch (e) {
      debugPrint('❌ [活动服务] 数据一致性检查失败: $e');
    }
  }
}
