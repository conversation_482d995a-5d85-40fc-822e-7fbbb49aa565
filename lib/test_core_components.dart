import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';

import 'models/unified_marker.dart';
import 'config/filter_config.dart';
import 'services/marker_filter_service.dart';
import 'services/marker_priority_calculator.dart';
import 'services/debounce_throttle_manager.dart';

/// 核心组件功能验证
///
/// 这个文件用于验证我们的核心管理组件是否正常工作
/// 可以在开发过程中快速测试各个组件的基本功能
class CoreComponentsValidator {
  static Future<void> validateAllComponents() async {
    debugPrint('🚀 开始验证核心管理组件...\n');

    try {
      await _validateFilterConfig();
      await _validateMarkerFilterService();
      await _validateMarkerPriorityCalculator();
      await _validateDebounceThrottleManager();
      await _validateIntegration();

      debugPrint('🎉 所有核心组件验证通过！');
      debugPrint('✅ 系统已准备好进入UI集成阶段');
    } catch (e) {
      debugPrint('❌ 核心组件验证失败: $e');
      rethrow;
    }
  }

  static Future<void> _validateFilterConfig() async {
    debugPrint('🔧 验证 FilterConfig 配置系统...');

    // 测试默认配置
    final defaultConfig = FilterConfig.defaultConfig();
    assert(defaultConfig.isValid, 'FilterConfig: 默认配置应该有效');
    assert(defaultConfig.maxDisplayCount == 200, 'FilterConfig: 默认显示数量应为200');

    // 测试严格配置
    final strictConfig = FilterConfig.strictConfig();
    assert(strictConfig.requireOnSite, 'FilterConfig: 严格配置应要求实地');
    assert(strictConfig.requireRealPhotos, 'FilterConfig: 严格配置应要求实拍');

    // 测试序列化
    final json = strictConfig.toJson();
    final restored = FilterConfig.fromJson(json);
    assert(
      restored.requireOnSite == strictConfig.requireOnSite,
      'FilterConfig: 序列化应保持一致',
    );

    // 测试配置摘要
    final summary = defaultConfig.getSummary();
    assert(summary.isNotEmpty, 'FilterConfig: 配置摘要不应为空');

    debugPrint('✅ FilterConfig 验证通过');
  }

  static Future<void> _validateMarkerFilterService() async {
    debugPrint('🔍 验证 MarkerFilterService 过滤服务...');

    final filterService = MarkerFilterService();
    final testLocation = LatLng(39.9042, 116.4074);

    try {
      // 设置用户信息
      filterService.setCurrentUserId('test_user');
      filterService.setCurrentLocation(testLocation);

      // 创建测试数据
      final testMarkers = _createTestMarkers();
      final defaultConfig = FilterConfig.defaultConfig();

      // 测试过滤功能
      final filterResult = filterService.filterMarkers(
        testMarkers,
        defaultConfig,
      );
      assert(
        filterResult.filteredMarkers.isNotEmpty,
        'MarkerFilterService: 过滤结果不应为空',
      );
      assert(
        filterResult.stats.originalCount == testMarkers.length,
        'MarkerFilterService: 原始数量应正确',
      );

      // 测试单个标记过滤
      final firstMarker = testMarkers.first;
      filterService.checkMarkerPassesFilter(firstMarker, defaultConfig);

      // 测试统计信息
      final stats = filterService.getFilterStats();
      assert(stats.isNotEmpty, 'MarkerFilterService: 统计信息不应为空');

      debugPrint('✅ MarkerFilterService 验证通过');
    } finally {
      filterService.dispose();
    }
  }

  static Future<void> _validateMarkerPriorityCalculator() async {
    debugPrint('📊 验证 MarkerPriorityCalculator 排序算法...');

    final priorityCalculator = MarkerPriorityCalculator();
    final testLocation = LatLng(39.9042, 116.4074);

    try {
      // 设置位置信息
      priorityCalculator.setCurrentLocation(testLocation);

      // 创建测试数据
      final testMarkers = _createTestMarkers();
      final defaultConfig = FilterConfig.defaultConfig();

      // 测试评分计算
      final firstMarker = testMarkers.first;
      final score = priorityCalculator.calculateMarkerScore(
        firstMarker,
        defaultConfig,
      );
      assert(score.totalScore >= 0, 'MarkerPriorityCalculator: 总分应大于等于0');
      assert(score.personalScore >= 0, 'MarkerPriorityCalculator: 个人分数应大于等于0');
      assert(score.qualityScore >= 0, 'MarkerPriorityCalculator: 质量分数应大于等于0');

      // 测试排序功能
      final sortResult = priorityCalculator.sortMarkers(
        testMarkers,
        defaultConfig,
      );
      assert(
        sortResult.sortedMarkers.length == testMarkers.length,
        'MarkerPriorityCalculator: 排序后数量应一致',
      );
      assert(
        sortResult.stats.processingTimeMs >= 0,
        'MarkerPriorityCalculator: 处理时间应大于等于0',
      );

      // 测试按维度排序
      final personalSorted = priorityCalculator.sortByDimension(
        testMarkers,
        defaultConfig,
        'personal',
      );
      assert(
        personalSorted.length == testMarkers.length,
        'MarkerPriorityCalculator: 按维度排序数量应一致',
      );

      // 测试排序建议
      final recommendation = priorityCalculator.getSortingRecommendation(
        testMarkers,
        defaultConfig,
      );
      assert(recommendation.isNotEmpty, 'MarkerPriorityCalculator: 排序建议不应为空');

      // 测试统计信息
      final stats = priorityCalculator.getCalculatorStats();
      assert(stats.isNotEmpty, 'MarkerPriorityCalculator: 统计信息不应为空');

      debugPrint('✅ MarkerPriorityCalculator 验证通过');
    } finally {
      priorityCalculator.dispose();
    }
  }

  static Future<void> _validateDebounceThrottleManager() async {
    debugPrint('⏱️ 验证 DebounceThrottleManager 防抖节流机制...');

    final debounceThrottleManager = DebounceThrottleManager();

    try {
      // 测试防抖功能
      for (int i = 0; i < 5; i++) {
        debounceThrottleManager.debounceMapMove(() {
          // 防抖回调
        });
      }

      // 测试是否有活跃操作
      assert(
        debounceThrottleManager.hasActiveOperations,
        'DebounceThrottleManager: 应该有活跃操作',
      );

      // 测试立即执行
      int immediateCallCount = 0;
      debounceThrottleManager.executeMapMoveNow(() {
        immediateCallCount++;
      });
      assert(immediateCallCount == 1, 'DebounceThrottleManager: 立即执行应该生效');

      // 测试统计信息
      final stats = debounceThrottleManager.getStats();
      assert(stats.isNotEmpty, 'DebounceThrottleManager: 统计信息不应为空');
      assert(
        stats['debounced_calls'] != null,
        'DebounceThrottleManager: 应该有防抖调用统计',
      );

      // 测试取消操作
      debounceThrottleManager.cancelAll();

      debugPrint('✅ DebounceThrottleManager 验证通过');
    } finally {
      debounceThrottleManager.dispose();
    }
  }

  static Future<void> _validateIntegration() async {
    debugPrint('🔄 验证组件集成...');

    final filterService = MarkerFilterService();
    final priorityCalculator = MarkerPriorityCalculator();
    final debounceThrottleManager = DebounceThrottleManager();
    final testLocation = LatLng(39.9042, 116.4074);

    try {
      // 设置服务
      filterService.setCurrentUserId('test_user');
      filterService.setCurrentLocation(testLocation);
      priorityCalculator.setCurrentLocation(testLocation);

      // 创建测试数据和配置
      final testMarkers = _createTestMarkers();
      final config = FilterConfig(
        requirePhotos: true,
        minLikesCount: 2,
        personalWeight: 0.4,
        qualityWeight: 0.3,
        socialWeight: 0.2,
        freshnessWeight: 0.1,
        maxDisplayCount: 50,
      );

      assert(config.isValid, 'Integration: 测试配置应该有效');

      // 模拟完整的处理流程
      debounceThrottleManager.debounceDataLoad(() {
        // 执行过滤
        final filterResult = filterService.filterMarkers(testMarkers, config);
        assert(
          filterResult.filteredMarkers.isNotEmpty,
          'Integration: 过滤结果不应为空',
        );

        // 执行排序
        final sortResult = priorityCalculator.sortMarkers(
          filterResult.filteredMarkers,
          config,
        );
        assert(sortResult.sortedMarkers.isNotEmpty, 'Integration: 排序结果不应为空');
      });

      // 等待防抖完成
      await Future.delayed(const Duration(milliseconds: 400));

      // 验证集成统计
      final filterStats = filterService.getFilterStats();
      final calculatorStats = priorityCalculator.getCalculatorStats();
      final debounceStats = debounceThrottleManager.getStats();

      assert(filterStats.isNotEmpty, 'Integration: 过滤统计不应为空');
      assert(calculatorStats.isNotEmpty, 'Integration: 计算器统计不应为空');
      assert(debounceStats.isNotEmpty, 'Integration: 防抖统计不应为空');

      debugPrint('✅ 组件集成验证通过');
    } finally {
      filterService.dispose();
      priorityCalculator.dispose();
      debounceThrottleManager.dispose();
    }
  }

  static List<UnifiedMarker> _createTestMarkers() {
    final now = DateTime.now();
    return [
      SpotMarker(
        id: 'spot1',
        name: '优质钓点',
        location: LatLng(39.9042, 116.4074),
        userId: 'user1',
        created: now.subtract(const Duration(days: 1)),
        updated: now,
        status: 'active',
        spotType: '水库',
        isOnSite: true,
        hasRealPhotos: true,
        hasPhotos: true,
        likesCount: 15,
        isFavorited: true,
        isMine: true,
        isReported: false,
      ),
      SpotMarker(
        id: 'spot2',
        name: '普通钓点',
        location: LatLng(39.9052, 116.4084),
        userId: 'user2',
        created: now.subtract(const Duration(days: 5)),
        updated: now.subtract(const Duration(days: 2)),
        status: 'active',
        spotType: '河流',
        isOnSite: false,
        hasRealPhotos: false,
        hasPhotos: true,
        likesCount: 3,
        isFavorited: false,
        isMine: false,
        isReported: false,
      ),
      ActivityMarker(
        id: 'activity1',
        name: '热门活动',
        location: LatLng(39.9032, 116.4064),
        userId: 'user1',
        created: now.subtract(const Duration(hours: 2)),
        updated: now,
        status: 'active',
        activityType: '钓鱼比赛',
        startTime: now.add(const Duration(hours: 2)),
        maxParticipants: 20,
        currentParticipants: 8,
        hasImages: true,
        isFavorited: false,
        isMyActivity: false,
        isJoined: true,
        isReported: false,
      ),
    ];
  }
}

/// 快速验证函数，可以在应用启动时调用
Future<bool> quickValidateCoreComponents() async {
  try {
    await CoreComponentsValidator.validateAllComponents();
    return true;
  } catch (e) {
    debugPrint('❌ 核心组件验证失败: $e');
    return false;
  }
}
