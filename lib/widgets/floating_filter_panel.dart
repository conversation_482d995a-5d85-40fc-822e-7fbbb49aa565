import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../config/filter_config.dart';
import 'bottom_filter_sheet.dart';

/// 浮动过滤面板
///
/// 提供简洁的过滤控制界面，悬浮在地图页面上
class FloatingFilterPanel extends StatefulWidget {
  /// 当前过滤配置
  final FilterConfig currentConfig;

  /// 配置变更回调
  final Function(FilterConfig) onConfigChanged;

  /// 关闭回调
  final VoidCallback onClose;

  /// 更多设置回调
  final VoidCallback onMoreSettings;

  /// 当前地图范围内的统计数据
  final FilterStatistics statistics;

  const FloatingFilterPanel({
    super.key,
    required this.currentConfig,
    required this.onConfigChanged,
    required this.onClose,
    required this.onMoreSettings,
    required this.statistics,
  });

  @override
  State<FloatingFilterPanel> createState() => _FloatingFilterPanelState();
}

class _FloatingFilterPanelState extends State<FloatingFilterPanel> {
  late FilterConfig _config;

  @override
  void initState() {
    super.initState();
    _config = widget.currentConfig;
  }

  @override
  void didUpdateWidget(FloatingFilterPanel oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.currentConfig != oldWidget.currentConfig) {
      setState(() {
        _config = widget.currentConfig;
      });
    }
  }

  void _updateConfig(FilterConfig newConfig) {
    setState(() {
      _config = newConfig;
    });
    widget.onConfigChanged(newConfig);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 半透明背景
        GestureDetector(
          onTap: widget.onClose,
          child: Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.black.withValues(alpha: 0.3),
          ),
        ),

        // 浮动面板
        Center(
          child: Container(
            width: MediaQuery.of(context).size.width * 0.8,
            height: MediaQuery.of(context).size.height * 0.7,
            margin: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(),
                Expanded(child: SingleChildScrollView(child: _buildContent())),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Icon(
            FontAwesomeIcons.filter,
            size: 20,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 8),
          Text(
            '过滤设置',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: widget.onClose,
            icon: const Icon(Icons.close),
            iconSize: 20,
          ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatistics(),
          _buildDivider(),
          _buildAlwaysOnSection(),
          _buildDivider(),
          _buildAlwaysOffSection(),
          _buildDivider(),
          _buildMaxCountSlider(),
          _buildDivider(),
          _buildMoreSettingsButton(),
        ],
      ),
    );
  }

  /// 构建统计信息
  Widget _buildStatistics() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '${widget.statistics.filteredCount}/${widget.statistics.totalCount}',
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          if (widget.statistics.priorityCount > 0) ...[
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.star, size: 12, color: Colors.orange),
                  const SizedBox(width: 2),
                  Text(
                    '${widget.statistics.priorityCount}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.orange,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建分隔线
  Widget _buildDivider() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6),
      height: 1,
      color: Colors.grey.withValues(alpha: 0.3),
    );
  }

  /// 构建常开项目区域
  Widget _buildAlwaysOnSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '显示类型',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 6),
        _buildSwitchTile('钓点分享', _config.showSpots, (value) {
          _updateConfig(_config.copyWithShowSpots(value));
        }),
        _buildSwitchTile('活动分享', _config.showActivities, (value) {
          _updateConfig(_config.copyWithShowActivities(value));
        }),
        _buildSwitchTile('路亚标点', _config.showLureSpots, (value) {
          _updateConfig(_config.copyWith(showLureSpots: value));
        }),
        _buildSwitchTile('传统钓点', _config.showTraditionalSpots, (value) {
          _updateConfig(_config.copyWith(showTraditionalSpots: value));
        }),
        _buildSwitchTile('收费钓点', _config.showPaidSpots, (value) {
          _updateConfig(_config.copyWith(showPaidSpots: value));
        }),
      ],
    );
  }

  /// 构建常关项目区域
  Widget _buildAlwaysOffSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '质量筛选',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 6),
        _buildSwitchTile('实地标签', _config.requireFieldTag, (value) {
          _updateConfig(_config.copyWithRequireFieldTag(value));
        }),
        _buildSwitchTile('照片标签', _config.requirePhotoTag, (value) {
          _updateConfig(_config.copyWithRequirePhotoTag(value));
        }),
        _buildSwitchTile('过期活动', _config.showExpired, (value) {
          _updateConfig(_config.copyWith(showExpired: value));
        }),
        _buildSwitchTile('较多点赞', _config.requireHighLikes, (value) {
          _updateConfig(_config.copyWithRequireHighLikes(value));
        }),
      ],
    );
  }

  /// 构建开关项
  Widget _buildSwitchTile(String title, bool value, Function(bool) onChanged) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Expanded(child: Text(title, style: const TextStyle(fontSize: 14))),
          Transform.scale(
            scale: 0.8,
            child: Switch(
              value: value,
              onChanged: onChanged,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建最大数量滑动条
  Widget _buildMaxCountSlider() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              '最大显示数量',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            const Spacer(),
            Text(
              '${_config.maxDisplayCount}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Slider(
          value: _config.maxDisplayCount.toDouble(),
          min: 10,
          max: 200,
          divisions: 19,
          onChanged: (value) {
            _updateConfig(_config.copyWith(maxDisplayCount: value.round()));
          },
        ),
      ],
    );
  }

  /// 构建更多设置按钮
  Widget _buildMoreSettingsButton() {
    return SizedBox(
      width: double.infinity,
      child: TextButton.icon(
        onPressed: widget.onMoreSettings,
        icon: const Icon(Icons.settings),
        label: const Text('更多设置'),
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 8),
        ),
      ),
    );
  }
}
