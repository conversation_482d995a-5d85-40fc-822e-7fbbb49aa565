import 'package:flutter/material.dart';

/// 滚动行为混入
/// 
/// 提供通用的滚动控制逻辑，包括滚动到指定组件、错误字段等
mixin ScrollBehaviorMixin<T extends StatefulWidget> on State<T> {
  
  /// 当前的滚动控制器
  ScrollController? get currentScrollController;

  /// 表单键映射
  Map<String, GlobalKey> get formKeys;

  /// 滚动到指定的组件
  Future<void> scrollToWidget(GlobalKey key, {
    Duration duration = const Duration(milliseconds: 500),
    Curve curve = Curves.easeInOut,
    double alignment = 0.1,
  }) async {
    if (currentScrollController == null) {
      debugPrint('❌ [滚动] ScrollController为空');
      return;
    }

    // 延迟执行，确保布局完成
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final context = key.currentContext;
      if (context != null) {
        try {
          await Scrollable.ensureVisible(
            context,
            duration: duration,
            curve: curve,
            alignment: alignment,
          );
          debugPrint('✅ [滚动] 成功滚动到目标组件');
        } catch (e) {
          debugPrint('❌ [滚动] ensureVisible失败: $e');
          // 使用备用滚动方案
          await _fallbackScroll(key, duration, curve);
        }
      } else {
        debugPrint('❌ [滚动] 目标组件context为空');
      }
    });
  }

  /// 备用滚动方案
  Future<void> _fallbackScroll(
    GlobalKey key, 
    Duration duration, 
    Curve curve
  ) async {
    final context = key.currentContext;
    if (context == null || currentScrollController == null) return;

    try {
      final renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final position = renderBox.localToGlobal(Offset.zero);
        final scrollPosition = (position.dy - 100).clamp(
          0.0,
          currentScrollController!.position.maxScrollExtent,
        );

        await currentScrollController!.animateTo(
          scrollPosition,
          duration: duration,
          curve: curve,
        );
        debugPrint('✅ [滚动] 备用方案滚动成功');
      }
    } catch (e) {
      debugPrint('❌ [滚动] 备用方案也失败: $e');
    }
  }

  /// 滚动到指定名称的表单字段
  Future<void> scrollToFormField(String fieldName, {
    Duration duration = const Duration(milliseconds: 500),
    Curve curve = Curves.easeInOut,
    double alignment = 0.1,
  }) async {
    final key = formKeys[fieldName];
    if (key != null) {
      await scrollToWidget(key, duration: duration, curve: curve, alignment: alignment);
    } else {
      debugPrint('❌ [滚动] 未找到字段: $fieldName');
    }
  }

  /// 滚动到第一个错误字段
  Future<void> scrollToFirstError({
    Duration duration = const Duration(milliseconds: 500),
    Curve curve = Curves.easeInOut,
  }) async {
    if (formKeys.isNotEmpty) {
      final firstKey = formKeys.values.first;
      await scrollToWidget(firstKey, duration: duration, curve: curve);
    } else {
      debugPrint('❌ [滚动] 没有可滚动的表单字段');
    }
  }

  /// 滚动到顶部
  Future<void> scrollToTop({
    Duration duration = const Duration(milliseconds: 500),
    Curve curve = Curves.easeInOut,
  }) async {
    if (currentScrollController != null) {
      await currentScrollController!.animateTo(
        0.0,
        duration: duration,
        curve: curve,
      );
    }
  }

  /// 滚动到底部
  Future<void> scrollToBottom({
    Duration duration = const Duration(milliseconds: 500),
    Curve curve = Curves.easeInOut,
  }) async {
    if (currentScrollController != null) {
      await currentScrollController!.animateTo(
        currentScrollController!.position.maxScrollExtent,
        duration: duration,
        curve: curve,
      );
    }
  }

  /// 检查组件是否在视口中可见
  bool isWidgetVisible(GlobalKey key) {
    final context = key.currentContext;
    if (context == null || currentScrollController == null) return false;

    try {
      final renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox == null) return false;

      final position = renderBox.localToGlobal(Offset.zero);
      final size = renderBox.size;
      
      // 获取视口信息
      final scrollPosition = currentScrollController!.position;
      final viewportHeight = scrollPosition.viewportDimension;
      final scrollOffset = scrollPosition.pixels;

      // 检查组件是否在视口范围内
      final widgetTop = position.dy;
      final widgetBottom = position.dy + size.height;
      final viewportTop = scrollOffset;
      final viewportBottom = scrollOffset + viewportHeight;

      return widgetBottom > viewportTop && widgetTop < viewportBottom;
    } catch (e) {
      debugPrint('❌ [滚动] 检查可见性失败: $e');
      return false;
    }
  }

  /// 平滑滚动到指定偏移量
  Future<void> smoothScrollTo(
    double offset, {
    Duration duration = const Duration(milliseconds: 500),
    Curve curve = Curves.easeInOut,
  }) async {
    if (currentScrollController != null) {
      final clampedOffset = offset.clamp(
        0.0,
        currentScrollController!.position.maxScrollExtent,
      );
      
      await currentScrollController!.animateTo(
        clampedOffset,
        duration: duration,
        curve: curve,
      );
    }
  }

  /// 获取当前滚动位置
  double get currentScrollOffset {
    return currentScrollController?.position.pixels ?? 0.0;
  }

  /// 获取最大滚动范围
  double get maxScrollExtent {
    return currentScrollController?.position.maxScrollExtent ?? 0.0;
  }

  /// 获取视口高度
  double get viewportHeight {
    return currentScrollController?.position.viewportDimension ?? 0.0;
  }

  /// 检查是否可以向上滚动
  bool get canScrollUp {
    final controller = currentScrollController;
    return controller != null && controller.position.pixels > 0;
  }

  /// 检查是否可以向下滚动
  bool get canScrollDown {
    final controller = currentScrollController;
    return controller != null && 
           controller.position.pixels < controller.position.maxScrollExtent;
  }

  /// 检查是否滚动到顶部
  bool get isAtTop {
    final controller = currentScrollController;
    return controller != null && controller.position.pixels <= 0;
  }

  /// 检查是否滚动到底部
  bool get isAtBottom {
    final controller = currentScrollController;
    return controller != null && 
           controller.position.pixels >= controller.position.maxScrollExtent;
  }
}
