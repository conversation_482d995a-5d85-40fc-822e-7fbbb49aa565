import 'package:flutter/material.dart';

import '../snackbar.dart';
import '../add_spot_form/image_upload_widget.dart';

/// 表单验证混入
/// 
/// 提供通用的表单验证逻辑和错误处理
mixin FormValidationMixin<T extends StatefulWidget> on State<T> {
  
  /// 验证图片是否为空
  bool validateImages(List<ImageUploadItem> images, {String? errorMessage}) {
    if (images.isEmpty) {
      SnackBarService.showError(
        context, 
        errorMessage ?? '请添加照片'
      );
      return false;
    }
    return true;
  }

  /// 验证是否有图片正在上传
  bool validateImageUploading(
    List<ImageUploadItem> images, 
    bool Function(List<ImageUploadItem>) hasUploadingImages,
    {String? errorMessage}
  ) {
    if (hasUploadingImages(images)) {
      SnackBarService.showError(
        context, 
        errorMessage ?? '请等待图片上传完成'
      );
      return false;
    }
    return true;
  }

  /// 验证文本字段是否为空
  bool validateTextNotEmpty(String? text, {String? errorMessage}) {
    if (text == null || text.trim().isEmpty) {
      SnackBarService.showError(
        context, 
        errorMessage ?? '请填写必填字段'
      );
      return false;
    }
    return true;
  }

  /// 验证文本最小长度
  bool validateTextMinLength(
    String? text, 
    int minLength, 
    {String? errorMessage}
  ) {
    if (text == null || text.trim().length < minLength) {
      SnackBarService.showError(
        context, 
        errorMessage ?? '内容至少需要$minLength个字符'
      );
      return false;
    }
    return true;
  }

  /// 验证文本最大长度
  bool validateTextMaxLength(
    String? text, 
    int maxLength, 
    {String? errorMessage}
  ) {
    if (text != null && text.trim().length > maxLength) {
      SnackBarService.showError(
        context, 
        errorMessage ?? '内容不能超过$maxLength个字符'
      );
      return false;
    }
    return true;
  }

  /// 验证时间不能是过去时间
  bool validateTimeNotPast(DateTime? time, {String? errorMessage}) {
    if (time == null) {
      SnackBarService.showError(
        context, 
        errorMessage ?? '请选择时间'
      );
      return false;
    }
    
    if (time.isBefore(DateTime.now())) {
      SnackBarService.showError(
        context, 
        errorMessage ?? '时间不能是过去的时间'
      );
      return false;
    }
    return true;
  }

  /// 验证时间不能太远的未来
  bool validateTimeNotTooFuture(
    DateTime? time, 
    Duration maxFuture, 
    {String? errorMessage}
  ) {
    if (time == null) return true;
    
    final maxFutureTime = DateTime.now().add(maxFuture);
    if (time.isAfter(maxFutureTime)) {
      SnackBarService.showError(
        context, 
        errorMessage ?? '时间不能超过${maxFuture.inDays}天后'
      );
      return false;
    }
    return true;
  }

  /// 验证时间至少提前指定时长
  bool validateTimeAdvance(
    DateTime? time, 
    Duration minAdvance, 
    {String? errorMessage, bool isWarning = false}
  ) {
    if (time == null) return true;
    
    final minAdvanceTime = DateTime.now().add(minAdvance);
    if (time.isBefore(minAdvanceTime)) {
      final message = errorMessage ?? 
          '建议至少提前${minAdvance.inHours}小时安排，以便有充足的准备时间';
      
      if (isWarning) {
        SnackBarService.showWarning(context, message);
      } else {
        SnackBarService.showError(context, message);
      }
      return false;
    }
    return true;
  }

  /// 组合验证器 - 验证所有条件
  bool validateAll(List<bool Function()> validators) {
    for (final validator in validators) {
      if (!validator()) {
        return false;
      }
    }
    return true;
  }

  /// 验证表单字段（使用FormKey）
  bool validateFormFields(GlobalKey<FormState> formKey) {
    return formKey.currentState?.validate() ?? false;
  }

  /// 显示成功消息
  void showSuccessMessage(String message) {
    SnackBarService.showSuccess(context, message);
  }

  /// 显示错误消息
  void showErrorMessage(String message) {
    SnackBarService.showError(context, message);
  }

  /// 显示警告消息
  void showWarningMessage(String message) {
    SnackBarService.showWarning(context, message);
  }
}

/// 钓点表单验证混入
/// 
/// 专门用于钓点表单的验证逻辑
mixin SpotFormValidationMixin<T extends StatefulWidget> on State<T> {
  
  /// 验证钓点名称
  bool validateSpotName(String? name) {
    if (name == null || name.trim().isEmpty) {
      SnackBarService.showError(context, '请输入钓点名称');
      return false;
    }
    return true;
  }

  /// 验证钓点描述
  bool validateSpotDescription(String? description, {int minLength = 15}) {
    if (description == null || description.trim().isEmpty) {
      SnackBarService.showError(context, '请输入钓点描述');
      return false;
    }
    
    if (description.trim().length < minLength) {
      SnackBarService.showError(context, '钓点描述至少需要${minLength}个字符');
      return false;
    }
    
    return true;
  }
}

/// 活动表单验证混入
/// 
/// 专门用于活动表单的验证逻辑
mixin ActivityFormValidationMixin<T extends StatefulWidget> on State<T> {
  
  /// 验证活动名称
  bool validateActivityName(String? name, {int maxLength = 50}) {
    if (name == null || name.trim().isEmpty) {
      SnackBarService.showError(context, '请输入活动名称');
      return false;
    }
    
    if (name.trim().length > maxLength) {
      SnackBarService.showError(context, '活动名称不能超过${maxLength}个字符');
      return false;
    }
    
    return true;
  }

  /// 验证活动描述（可选）
  bool validateActivityDescription(String? description, {int minLength = 15}) {
    // 活动描述是可选的，但如果填写了就要满足最小长度
    if (description != null && 
        description.trim().isNotEmpty && 
        description.trim().length < minLength) {
      SnackBarService.showError(context, '活动描述至少需要${minLength}个字符');
      return false;
    }
    return true;
  }

  /// 验证钓鱼时间
  bool validateFishingTime(DateTime? fishingTime) {
    if (fishingTime == null) {
      SnackBarService.showError(context, '请选择钓鱼时间');
      return false;
    }

    // 检查时间不能是过去
    if (fishingTime.isBefore(DateTime.now())) {
      SnackBarService.showError(context, '钓鱼时间不能是过去的时间');
      return false;
    }

    // 检查时间不能超过30天
    final maxFutureTime = DateTime.now().add(const Duration(days: 30));
    if (fishingTime.isAfter(maxFutureTime)) {
      SnackBarService.showError(context, '钓鱼时间不能超过30天后');
      return false;
    }

    // 建议至少提前1小时（警告，不阻止提交）
    final oneHourLater = DateTime.now().add(const Duration(hours: 1));
    if (fishingTime.isBefore(oneHourLater)) {
      SnackBarService.showWarning(
        context, 
        '活动开始时间建议至少提前1小时安排，以便钓友有充足的准备时间'
      );
      return false; // 这里返回false是为了阻止提交，让用户重新考虑时间
    }

    return true;
  }
}
