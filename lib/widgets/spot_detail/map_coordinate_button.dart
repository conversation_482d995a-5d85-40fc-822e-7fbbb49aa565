import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../models/fishing_spot.dart';
import '../../models/spot_visibility.dart';
import '../../services/service_locator.dart';
import '../../utils/visibility_condition_translator.dart';

/// 地图坐标按钮组件
///
/// 显示钓点坐标信息，并根据用户权限控制是否可以跳转到地图页面
class MapCoordinateButton extends StatefulWidget {
  final FishingSpot spot;
  final VoidCallback? onTap;

  const MapCoordinateButton({super.key, required this.spot, this.onTap});

  @override
  State<MapCoordinateButton> createState() => _MapCoordinateButtonState();
}

class _MapCoordinateButtonState extends State<MapCoordinateButton> {
  bool _canViewMap = false;
  bool _isCheckingPermission = true;

  @override
  void initState() {
    super.initState();
    _checkMapPermission();
  }

  /// 检查地图访问权限
  Future<void> _checkMapPermission() async {
    try {
      final currentUser = Services.auth.currentUser;

      // 简单权限检查
      final canView = VisibilityConditionTranslator.canCurrentUserView(
        widget.spot.visibility,
        widget.spot.visibilityConditions,
        currentUser?.id,
        widget.spot.userId,
      );

      // 如果简单检查通过，或者需要复杂检查
      if (canView || currentUser != null) {
        // 对于复杂的权限检查，调用SpotVisibilityService
        if (currentUser != null) {
          final hasAccess = await Services.spotVisibility.canUserViewSpot(
            currentUser.id,
            widget.spot,
          );

          if (mounted) {
            setState(() {
              _canViewMap = hasAccess;
              _isCheckingPermission = false;
            });
          }
        } else {
          // 未登录用户只能查看公开钓点
          if (mounted) {
            setState(() {
              _canViewMap = widget.spot.visibility == SpotVisibility.public;
              _isCheckingPermission = false;
            });
          }
        }
      } else {
        if (mounted) {
          setState(() {
            _canViewMap = false;
            _isCheckingPermission = false;
          });
        }
      }
    } catch (e) {
      debugPrint('❌ [地图权限] 检查权限失败: $e');
      if (mounted) {
        setState(() {
          _canViewMap = false;
          _isCheckingPermission = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isCheckingPermission) {
      return _buildLoadingButton();
    }

    return _buildCoordinateButton();
  }

  /// 构建加载状态按钮
  Widget _buildLoadingButton() {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
          ),
        ),
      ),
    );
  }

  /// 构建坐标按钮
  Widget _buildCoordinateButton() {
    final location = widget.spot.locationLatLng;
    final isEnabled = _canViewMap;

    return GestureDetector(
      onTap: isEnabled ? _handleTap : _handleDisabledTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isEnabled ? Colors.blue[50] : Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isEnabled ? Colors.blue[300]! : Colors.grey[300]!,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            FaIcon(
              FontAwesomeIcons.mapLocationDot,
              size: 16,
              color: isEnabled ? Colors.blue[600] : Colors.grey[500],
            ),
            const SizedBox(width: 6),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '坐标位置',
                  style: TextStyle(
                    fontSize: 11,
                    color: isEnabled ? Colors.blue[700] : Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '${location.latitude.toStringAsFixed(4)}, ${location.longitude.toStringAsFixed(4)}',
                  style: TextStyle(
                    fontSize: 10,
                    color: isEnabled ? Colors.blue[600] : Colors.grey[500],
                  ),
                ),
              ],
            ),
            const SizedBox(width: 4),
            Icon(
              isEnabled ? Icons.arrow_forward_ios : Icons.lock,
              size: 12,
              color: isEnabled ? Colors.blue[600] : Colors.grey[500],
            ),
          ],
        ),
      ),
    );
  }

  /// 处理按钮点击（有权限）
  void _handleTap() {
    if (widget.onTap != null) {
      widget.onTap!();
    } else {
      _navigateToMap();
    }
  }

  /// 处理按钮点击（无权限）
  void _handleDisabledTap() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_getPermissionMessage()),
        backgroundColor: Colors.orange[600],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// 获取权限提示信息
  String _getPermissionMessage() {
    final currentUser = Services.auth.currentUser;

    if (currentUser == null) {
      return '请先登录以查看地图位置';
    }

    final visibilityText = VisibilityConditionTranslator.translateConditions(
      widget.spot.visibility,
      widget.spot.visibilityConditions,
    );

    return '无法查看地图位置：$visibilityText';
  }

  /// 导航到地图页面 ⭐ 修改为使用统一导航服务
  void _navigateToMap() {
    try {
      // 使用统一导航服务，强制跳转到地图页面
      Services.mapNavigation.navigateToSpot(
        spotLocation: widget.spot.locationLatLng,
        context: context,
        forceNavigateToMapPage: true, // 从钓点详情页面强制跳转到地图页面
      );
    } catch (e) {
      debugPrint('❌ [地图导航] 导航失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('导航到地图失败，请重试'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
