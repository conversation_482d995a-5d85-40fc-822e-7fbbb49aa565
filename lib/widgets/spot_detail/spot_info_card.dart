import 'package:flutter/material.dart';
import '../../models/fishing_spot.dart';
import '../../models/spot_visibility.dart';
import '../../utils/visibility_condition_translator.dart';

/// 钓点信息卡片组件
///
/// 用于在钓点详情页展示各种信息的现代化卡片
class SpotInfoCard extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color iconColor;
  final List<Widget> children;
  final EdgeInsetsGeometry? padding;
  final bool showDivider;

  const SpotInfoCard({
    super.key,
    required this.title,
    required this.icon,
    required this.iconColor,
    required this.children,
    this.padding,
    this.showDivider = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题区域
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.shade200,
                  width: 0.5,
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: iconColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),

          // 内容区域
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children,
          ),
        ],
      ),
    );
  }
}

/// 信息行组件
class InfoRow extends StatelessWidget {
  final String label;
  final String value;
  final String? emoji;
  final Widget? trailing;
  final VoidCallback? onTap;
  final bool showDivider;

  const InfoRow({
    super.key,
    required this.label,
    required this.value,
    this.emoji,
    this.trailing,
    this.onTap,
    this.showDivider = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget content = Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: showDivider ? Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 0.5,
          ),
        ) : null,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (emoji != null) ...[
            Text(emoji!, style: const TextStyle(fontSize: 16)),
            const SizedBox(width: 8),
          ],
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          if (trailing != null) ...[const SizedBox(width: 8), trailing!],
        ],
      ),
    );

    if (onTap != null) {
      content = InkWell(
        onTap: onTap,
        child: content,
      );
    }

    return content;
  }
}

/// 实地发布徽标组件
class OnSiteBadge extends StatelessWidget {
  final bool isOnSite;

  const OnSiteBadge({super.key, required this.isOnSite});

  @override
  Widget build(BuildContext context) {
    if (!isOnSite) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.green[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green[300]!, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.verified, size: 14, color: Colors.green[700]),
          const SizedBox(width: 4),
          Text(
            '实地发布',
            style: TextStyle(
              fontSize: 12,
              color: Colors.green[700],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

/// 可见性信息组件
class VisibilityInfo extends StatelessWidget {
  final FishingSpot spot;

  const VisibilityInfo({super.key, required this.spot});

  @override
  Widget build(BuildContext context) {
    final visibilityText = VisibilityConditionTranslator.translateConditions(
      spot.visibility,
      spot.visibilityConditions,
    );

    return Row(
      children: [
        Icon(
          _getVisibilityIcon(spot.visibility),
          size: 16,
          color: _getVisibilityColor(spot.visibility),
        ),
        const SizedBox(width: 6),
        Text(
          visibilityText,
          style: TextStyle(fontSize: 13, color: Colors.grey[600]),
        ),
      ],
    );
  }

  IconData _getVisibilityIcon(SpotVisibility visibility) {
    switch (visibility) {
      case SpotVisibility.public:
        return Icons.public;
      case SpotVisibility.private:
        return Icons.lock;
      case SpotVisibility.friendsOnly:
        return Icons.group;
      case SpotVisibility.conditional:
        return Icons.star;
    }
  }

  Color _getVisibilityColor(SpotVisibility visibility) {
    switch (visibility) {
      case SpotVisibility.public:
        return Colors.green;
      case SpotVisibility.private:
        return Colors.red;
      case SpotVisibility.friendsOnly:
        return Colors.blue;
      case SpotVisibility.conditional:
        return Colors.orange;
    }
  }
}
