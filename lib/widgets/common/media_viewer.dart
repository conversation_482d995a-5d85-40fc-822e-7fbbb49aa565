import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:photo_view/photo_view.dart';
import 'package:chewie/chewie.dart';
import 'package:video_player/video_player.dart';
import '../add_spot_form/image_upload_widget.dart';

/// 媒体查看器 - 支持图片和视频的全屏查看
///
/// 功能特性：
/// - 图片：支持缩放、拖拽、Hero动画
/// - 视频：支持播放控制、音量调节
/// - 手势：下滑关闭、左右滑动切换
/// - 沉浸式体验：自动隐藏状态栏和导航栏
class MediaViewer extends StatefulWidget {
  /// 媒体列表
  final List<ImageUploadItem> mediaItems;

  /// 初始显示的媒体索引
  final int initialIndex;

  /// Hero标签前缀（用于Hero动画）
  final String heroTagPrefix;

  /// 原始索引列表（用于正确的Hero tag匹配）
  final List<int>? originalIndices;

  const MediaViewer({
    super.key,
    required this.mediaItems,
    this.initialIndex = 0,
    this.heroTagPrefix = 'media_hero',
    this.originalIndices,
  });

  @override
  State<MediaViewer> createState() => _MediaViewerState();
}

class _MediaViewerState extends State<MediaViewer>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late int _currentIndex;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  // 视频播放器相关
  VideoPlayerController? _videoController;
  ChewieController? _chewieController;

  // UI状态
  bool _isUIVisible = true;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);

    // 初始化动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    // 设置沉浸式模式
    _setImmersiveMode();

    // 初始化当前媒体
    _initializeCurrentMedia();

    // 开始淡入动画
    _fadeController.forward();
  }

  @override
  void dispose() {
    // 确保在销毁前暂停视频
    _pauseCurrentVideo();
    _pageController.dispose();
    _fadeController.dispose();
    _disposeVideoPlayer();
    _restoreSystemUI();
    super.dispose();
  }

  /// 设置沉浸式模式
  void _setImmersiveMode() {
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
      overlays: [],
    );
  }

  /// 恢复系统UI
  void _restoreSystemUI() {
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.manual,
      overlays: SystemUiOverlay.values,
    );
  }

  /// 初始化当前媒体
  void _initializeCurrentMedia() {
    final currentItem = widget.mediaItems[_currentIndex];
    if (currentItem.isVideo) {
      // 视频自动播放
      _initializeVideoPlayer(currentItem, autoPlay: true);
    }
  }

  /// 初始化视频播放器
  void _initializeVideoPlayer(
    ImageUploadItem videoItem, {
    bool autoPlay = true,
  }) {
    _disposeVideoPlayer();

    _videoController = VideoPlayerController.file(videoItem.file);
    _videoController!.initialize().then((_) {
      if (mounted) {
        _chewieController = ChewieController(
          videoPlayerController: _videoController!,
          autoPlay: autoPlay,
          looping: false,
          showControls: true,
          allowFullScreen: false,
          allowMuting: true,
          allowPlaybackSpeedChanging: false,
          // 设置控制器自动隐藏时间为0.5秒，提供更沉浸的观看体验
          hideControlsTimer: const Duration(milliseconds: 1000),
          // 播放开始时不显示控制器
          showControlsOnInitialize: false,
          materialProgressColors: ChewieProgressColors(
            playedColor: Theme.of(context).primaryColor,
            handleColor: Theme.of(context).primaryColor,
            backgroundColor: Colors.grey,
            bufferedColor: Colors.grey.shade300,
          ),
        );
        setState(() {});
      }
    });
  }

  /// 释放视频播放器
  void _disposeVideoPlayer() {
    _chewieController?.dispose();
    _videoController?.dispose();
    _chewieController = null;
    _videoController = null;
  }

  /// 暂停当前视频
  void _pauseCurrentVideo() {
    if (_videoController != null && _videoController!.value.isPlaying) {
      _videoController!.pause();
    }
  }

  /// 播放当前视频
  void _playCurrentVideo() {
    if (_videoController != null && !_videoController!.value.isPlaying) {
      _videoController!.play();
    }
  }

  /// 切换UI可见性
  void _toggleUIVisibility() {
    setState(() {
      _isUIVisible = !_isUIVisible;
    });
  }

  /// 关闭查看器
  void _closeViewer() {
    _fadeController.reverse().then((_) {
      if (mounted) {
        Navigator.of(context).pop();
      }
    });
  }

  /// 页面切换回调
  void _onPageChanged(int index) {
    if (_currentIndex != index) {
      // 如果之前的页面是视频，暂停播放
      if (_currentIndex < widget.mediaItems.length &&
          widget.mediaItems[_currentIndex].isVideo) {
        _pauseCurrentVideo();
      }

      setState(() {
        _currentIndex = index;
      });

      // 初始化新页面的媒体
      _initializeCurrentMedia();

      // 如果新页面是视频，自动播放
      if (widget.mediaItems[index].isVideo) {
        // 延迟一小段时间确保视频播放器已初始化
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            _playCurrentVideo();
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Stack(
          children: [
            // 主要内容区域
            GestureDetector(
              onTap: _toggleUIVisibility,
              onVerticalDragStart: (_) => _isDragging = true,
              onVerticalDragUpdate: (details) {
                if (_isDragging && details.delta.dy > 5) {
                  _closeViewer();
                }
              },
              onVerticalDragEnd: (_) => _isDragging = false,
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: _onPageChanged,
                itemCount: widget.mediaItems.length,
                itemBuilder: (context, index) {
                  return _buildMediaItem(widget.mediaItems[index], index);
                },
              ),
            ),

            // 顶部工具栏
            if (_isUIVisible) _buildTopBar(),

            // 底部指示器（多个媒体时显示）
            if (_isUIVisible && widget.mediaItems.length > 1)
              _buildBottomIndicator(),
          ],
        ),
      ),
    );
  }

  /// 构建媒体项目
  Widget _buildMediaItem(ImageUploadItem item, int index) {
    if (item.isVideo) {
      return _buildVideoItem(item, index);
    } else {
      return _buildImageItem(item, index);
    }
  }

  /// 构建图片项目
  Widget _buildImageItem(ImageUploadItem item, int index) {
    // 使用原始索引来生成Hero tag，确保与缩略图的tag匹配
    final originalIndex = widget.originalIndices?[index] ?? index;

    return PhotoView(
      imageProvider: FileImage(item.file),
      minScale: PhotoViewComputedScale.contained,
      maxScale: PhotoViewComputedScale.covered * 3.0,
      initialScale: PhotoViewComputedScale.contained,
      heroAttributes: PhotoViewHeroAttributes(
        tag: '${widget.heroTagPrefix}_$originalIndex',
      ),
      backgroundDecoration: const BoxDecoration(color: Colors.transparent),
      loadingBuilder:
          (context, event) => const Center(
            child: CircularProgressIndicator(color: Colors.white),
          ),
      errorBuilder:
          (context, error, stackTrace) => const Center(
            child: Icon(Icons.error, color: Colors.white, size: 64),
          ),
    );
  }

  /// 构建视频项目
  Widget _buildVideoItem(ImageUploadItem item, int index) {
    // 使用原始索引来生成Hero tag，确保与缩略图的tag匹配
    final originalIndex = widget.originalIndices?[index] ?? index;

    return Hero(
      tag: '${widget.heroTagPrefix}_$originalIndex',
      child: Center(
        child:
            _currentIndex == index
                ? (_chewieController != null
                    ? Chewie(controller: _chewieController!)
                    : _buildVideoLoadingWidget(item))
                : _buildVideoThumbnail(item),
      ),
    );
  }

  /// 构建视频加载状态
  Widget _buildVideoLoadingWidget(ImageUploadItem item) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // 显示缩略图作为背景
        if (item.thumbnailFile != null)
          Image.file(item.thumbnailFile!, fit: BoxFit.contain)
        else
          Container(
            width: 200,
            height: 200,
            color: Colors.grey[800],
            child: const Icon(Icons.video_file, size: 64, color: Colors.white),
          ),
        // 显示加载指示器
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.7),
            shape: BoxShape.circle,
          ),
          child: const Center(
            child: CircularProgressIndicator(
              color: Colors.white,
              strokeWidth: 2,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建视频缩略图
  Widget _buildVideoThumbnail(ImageUploadItem item) {
    // 移除播放按钮覆盖层，因为视频会自动播放
    if (item.thumbnailFile != null) {
      return Image.file(item.thumbnailFile!, fit: BoxFit.contain);
    } else {
      return Container(
        width: 200,
        height: 200,
        color: Colors.grey[800],
        child: const Icon(Icons.video_file, size: 64, color: Colors.white),
      );
    }
  }

  /// 构建顶部工具栏
  Widget _buildTopBar() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        height: MediaQuery.of(context).padding.top + 56,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.black.withValues(alpha: 0.7), Colors.transparent],
          ),
        ),
        child: SafeArea(
          child: Row(
            children: [
              IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: _closeViewer,
              ),
              const Spacer(),
              Text(
                '${_currentIndex + 1} / ${widget.mediaItems.length}',
                style: const TextStyle(color: Colors.white, fontSize: 16),
              ),
              const SizedBox(width: 16),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建底部指示器
  Widget _buildBottomIndicator() {
    return Positioned(
      bottom: 50,
      left: 0,
      right: 0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          widget.mediaItems.length,
          (index) => Container(
            width: 8,
            height: 8,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color:
                  index == _currentIndex
                      ? Colors.white
                      : Colors.white.withValues(alpha: 0.4),
            ),
          ),
        ),
      ),
    );
  }
}
