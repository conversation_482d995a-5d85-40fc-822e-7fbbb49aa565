import 'package:flutter/material.dart';

/// 通用的提交按钮组件，带有提示信息
/// 可用于钓点发布和活动发布页面
class SubmitButtonWithHint extends StatelessWidget {
  /// 按钮文本
  final String buttonText;
  
  /// 是否可以提交
  final bool canSubmit;
  
  /// 禁用原因（当不能提交时显示）
  final String? disabledReason;
  
  /// 提交回调
  final VoidCallback? onSubmit;
  
  /// 是否正在提交
  final bool isSubmitting;
  
  /// 按钮颜色主题
  final Color? buttonColor;
  
  /// 提示文本颜色
  final Color? hintTextColor;

  const SubmitButtonWithHint({
    super.key,
    required this.buttonText,
    required this.canSubmit,
    this.disabledReason,
    this.onSubmit,
    this.isSubmitting = false,
    this.buttonColor,
    this.hintTextColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveButtonColor = buttonColor ?? theme.primaryColor;
    final effectiveHintColor = hintTextColor ?? theme.textTheme.bodySmall?.color;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // 提示信息区域
        if (disabledReason != null) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: theme.colorScheme.outline.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 20,
                  color: effectiveHintColor,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    disabledReason!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: effectiveHintColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
        
        // 提交按钮
        SizedBox(
          height: 50,
          child: ElevatedButton(
            onPressed: canSubmit && !isSubmitting ? onSubmit : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: effectiveButtonColor,
              foregroundColor: Colors.white,
              disabledBackgroundColor: theme.colorScheme.outline.withOpacity(0.3),
              disabledForegroundColor: theme.colorScheme.onSurface.withOpacity(0.5),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              elevation: canSubmit && !isSubmitting ? 2 : 0,
            ),
            child: isSubmitting
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white.withOpacity(0.8),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        '发布中...',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: Colors.white.withOpacity(0.8),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  )
                : Text(
                    buttonText,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ],
    );
  }
}

/// 专门用于活动发布的提交按钮
class ActivitySubmitButton extends StatelessWidget {
  final bool canSubmit;
  final String? disabledReason;
  final VoidCallback? onSubmit;
  final bool isSubmitting;

  const ActivitySubmitButton({
    super.key,
    required this.canSubmit,
    this.disabledReason,
    this.onSubmit,
    this.isSubmitting = false,
  });

  @override
  Widget build(BuildContext context) {
    return SubmitButtonWithHint(
      buttonText: '发布活动',
      canSubmit: canSubmit,
      disabledReason: disabledReason,
      onSubmit: onSubmit,
      isSubmitting: isSubmitting,
      buttonColor: const Color(0xFF4CAF50), // 绿色主题，适合活动
    );
  }
}

/// 专门用于钓点发布的提交按钮
class SpotSubmitButton extends StatelessWidget {
  final bool canSubmit;
  final String? disabledReason;
  final VoidCallback? onSubmit;
  final bool isSubmitting;

  const SpotSubmitButton({
    super.key,
    required this.canSubmit,
    this.disabledReason,
    this.onSubmit,
    this.isSubmitting = false,
  });

  @override
  Widget build(BuildContext context) {
    return SubmitButtonWithHint(
      buttonText: '发布钓点',
      canSubmit: canSubmit,
      disabledReason: disabledReason,
      onSubmit: onSubmit,
      isSubmitting: isSubmitting,
      buttonColor: const Color(0xFF2196F3), // 蓝色主题，适合钓点
    );
  }
}
