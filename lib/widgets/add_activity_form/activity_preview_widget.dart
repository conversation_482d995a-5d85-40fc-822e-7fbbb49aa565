import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';

import '../../config/activity_config.dart';
import '../../models/emoji_marker.dart';
import '../add_spot_form/image_upload_widget.dart';

/// 活动预览组件
/// 
/// 在用户提交前显示活动的完整预览信息
class ActivityPreviewWidget extends StatelessWidget {
  final String title;
  final String description;
  final DateTime startTime;
  final double duration;
  final String fishType;
  final LatLng location;
  final List<ImageUploadItem> images;
  final VoidCallback? onEdit;
  final VoidCallback? onConfirm;

  const ActivityPreviewWidget({
    super.key,
    required this.title,
    required this.description,
    required this.startTime,
    required this.duration,
    required this.fishType,
    required this.location,
    required this.images,
    this.onEdit,
    this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拖拽指示器
          Container(
            margin: const EdgeInsets.only(top: 12, bottom: 20),
            width: 48,
            height: 5,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(3),
            ),
          ),

          // 标题栏
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                const Icon(
                  Icons.preview,
                  color: Colors.blue,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  '活动预览',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: onEdit,
                  child: const Text('编辑'),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 预览内容
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 活动标题
                  _buildPreviewCard(
                    icon: Icons.title,
                    title: '活动名称',
                    child: Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // 时间信息
                  _buildPreviewCard(
                    icon: Icons.schedule,
                    title: '钓鱼时间',
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          ActivityConfig.formatDateTime(startTime),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '持续 ${ActivityConfig.getDurationText(duration)}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '预计结束：${ActivityConfig.formatDateTime(_getEndTime())}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // 鱼种信息
                  _buildPreviewCard(
                    icon: Icons.pets,
                    title: '目标鱼种',
                    child: Row(
                      children: [
                        Text(
                          _getFishEmoji(),
                          style: const TextStyle(fontSize: 20),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _getFishName(),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // 位置信息
                  _buildPreviewCard(
                    icon: Icons.location_on,
                    title: '活动位置',
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${location.latitude.toStringAsFixed(6)}, ${location.longitude.toStringAsFixed(6)}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '点击查看地图位置',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue.shade600,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ],
                    ),
                  ),

                  if (description.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    _buildPreviewCard(
                      icon: Icons.description,
                      title: '活动描述',
                      child: Text(
                        description,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],

                  if (images.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    _buildPreviewCard(
                      icon: Icons.photo_library,
                      title: '活动图片 (${images.length})',
                      child: _buildImagePreview(),
                    ),
                  ],

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),

          // 底部按钮
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(
                top: BorderSide(color: Colors.grey.shade200),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: onEdit,
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      side: BorderSide(color: Colors.grey.shade400),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      '返回编辑',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: onConfirm,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.group, size: 20),
                        SizedBox(width: 8),
                        Text(
                          '一起去钓鱼',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewCard({
    required IconData icon,
    required String title,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 18,
                color: Colors.blue.shade600,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          child,
        ],
      ),
    );
  }

  Widget _buildImagePreview() {
    final completedImages = images
        .where((img) => img.isCompleted && img.uploadedUrl != null)
        .toList();

    if (completedImages.isEmpty) {
      return Text(
        '暂无图片',
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey.shade500,
        ),
      );
    }

    return SizedBox(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: completedImages.length,
        itemBuilder: (context, index) {
          final image = completedImages[index];
          return Container(
            margin: EdgeInsets.only(right: index < completedImages.length - 1 ? 8 : 0),
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.file(
                image.file,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey.shade200,
                    child: const Icon(
                      Icons.broken_image,
                      color: Colors.grey,
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  DateTime _getEndTime() {
    return startTime.add(Duration(
      hours: duration.toInt(),
      minutes: ((duration % 1) * 60).toInt(),
    ));
  }

  String _getFishEmoji() {
    final fishType = FishingSpotMarkers.fishTypes
        .firstWhere((f) => f.type == this.fishType, orElse: () => FishingSpotMarkers.fishTypes.first);
    return fishType.emoji;
  }

  String _getFishName() {
    final fishType = FishingSpotMarkers.fishTypes
        .firstWhere((f) => f.type == this.fishType, orElse: () => FishingSpotMarkers.fishTypes.first);
    return fishType.name;
  }
}
