import 'package:flutter/material.dart';

/// 活动类型选择器组件
///
/// 功能：
/// - 活动类型选择
/// - 可视化选择界面
/// - 状态管理
class ActivityTypeSelector extends StatelessWidget {
  /// 当前选中的活动类型
  final String selectedActivityType;

  /// 活动类型变更回调
  final Function(String) onChanged;

  /// 是否启用
  final bool enabled;

  /// 活动类型选项
  static const List<Map<String, String>> activityTypes = [
    {'value': 'lure', 'label': '路亚', 'emoji': '🎣'},
    {'value': 'platform', 'label': '台钓', 'emoji': '🪑'},
    {'value': 'night', 'label': '夜钓', 'emoji': '🌙'},
    {'value': 'sea', 'label': '海钓', 'emoji': '🌊'},
    {'value': 'pond', 'label': '鱼塘', 'emoji': '🏞️'}, 
  ];

  const ActivityTypeSelector({
    super.key,
    required this.selectedActivityType,
    required this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '一起去',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.normal,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Align(
          alignment: Alignment.centerLeft,
          child: Wrap(
            alignment: WrapAlignment.start,
            spacing: 8,
            runSpacing: 8,
            children:
                activityTypes.map((activityType) {
                  final isSelected =
                      selectedActivityType == activityType['value'];

                  return GestureDetector(
                    onTap:
                        enabled
                            ? () => onChanged(activityType['value']!)
                            : null,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: isSelected ? Colors.blue.shade50 : Colors.white,
                        border: Border.all(
                          color:
                              isSelected
                                  ? Colors.blue.shade300
                                  : Colors.grey.shade300,
                          width: isSelected ? 2 : 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ////不显示emoji就注释掉
                          // Text(
                          //   activityType['emoji']!,
                          //   style: const TextStyle(fontSize: 14),
                          // ),
                          const SizedBox(width: 5),
                          Text(
                            activityType['label']!,
                            style: TextStyle(
                              fontSize: 13,
                              color:
                                  isSelected
                                      ? Colors.blue.shade700
                                      : Colors.black87,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.w600
                                      : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),
      ],
    );
  }
}
