import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

/// 增量加载进度指示器
///
/// 显示三步加载的进度状态：
/// 1. 加载ID列表
/// 2. 加载摘要信息
/// 3. 应用过滤和排序
class IncrementalLoadingIndicator extends StatefulWidget {
  /// 当前加载步骤 (1-3)
  final int currentStep;

  /// 总步骤数
  final int totalSteps;

  /// 当前步骤的进度 (0.0-1.0)
  final double stepProgress;

  /// 当前步骤描述
  final String stepDescription;

  /// 加载的项目数量
  final int? loadedCount;

  /// 总项目数量
  final int? totalCount;

  /// 是否显示详细信息
  final bool showDetails;

  /// 是否显示在底部
  final bool showAtBottom;

  /// 自定义颜色
  final Color? primaryColor;

  /// 错误信息
  final String? errorMessage;

  /// 重试回调
  final VoidCallback? onRetry;

  const IncrementalLoadingIndicator({
    super.key,
    required this.currentStep,
    this.totalSteps = 3,
    required this.stepProgress,
    required this.stepDescription,
    this.loadedCount,
    this.totalCount,
    this.showDetails = true,
    this.showAtBottom = false,
    this.primaryColor,
    this.errorMessage,
    this.onRetry,
  });

  @override
  State<IncrementalLoadingIndicator> createState() =>
      _IncrementalLoadingIndicatorState();
}

class _IncrementalLoadingIndicatorState
    extends State<IncrementalLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // 滑入动画控制器
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: widget.showAtBottom ? const Offset(0, 1) : const Offset(0, -1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    // 启动滑入动画
    _slideController.forward();
  }

  @override
  void didUpdateWidget(IncrementalLoadingIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 脉冲动画已移除，无需额外处理
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child:
            widget.errorMessage != null
                ? _buildErrorContent()
                : _buildLoadingContent(),
      ),
    );
  }

  /// 构建加载内容
  Widget _buildLoadingContent() {
    final primaryColor = widget.primaryColor ?? Theme.of(context).primaryColor;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 步骤指示器
        _buildStepIndicator(primaryColor),

        if (widget.showDetails) ...[
          const SizedBox(height: 16),

          // 当前步骤描述
          Row(
            children: [
              FaIcon(
                _getStepIcon(widget.currentStep),
                color: primaryColor,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  widget.stepDescription,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // 进度条
          LinearProgressIndicator(
            value: widget.stepProgress,
            backgroundColor: primaryColor.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
          ),

          const SizedBox(height: 8),

          // 数量信息
          if (widget.loadedCount != null || widget.totalCount != null)
            _buildCountInfo(),
        ],
      ],
    );
  }

  /// 构建错误内容
  Widget _buildErrorContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            const FaIcon(
              FontAwesomeIcons.triangleExclamation,
              color: Colors.red,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                '加载失败',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        Text(
          widget.errorMessage!,
          style: Theme.of(context).textTheme.bodySmall,
        ),

        if (widget.onRetry != null) ...[
          const SizedBox(height: 12),

          ElevatedButton.icon(
            onPressed: widget.onRetry,
            icon: const FaIcon(FontAwesomeIcons.arrowRotateRight, size: 14),
            label: const Text('重试'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ],
    );
  }

  /// 构建步骤指示器
  Widget _buildStepIndicator(Color primaryColor) {
    return Row(
      children: List.generate(widget.totalSteps, (index) {
        final stepNumber = index + 1;
        final isActive = stepNumber == widget.currentStep;
        final isCompleted = stepNumber < widget.currentStep;

        return Expanded(
          child: Row(
            children: [
              Expanded(
                child: Container(
                  height: 4,
                  decoration: BoxDecoration(
                    color:
                        isCompleted || isActive
                            ? primaryColor
                            : primaryColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child:
                      isActive
                          ? LinearProgressIndicator(
                            value: widget.stepProgress,
                            backgroundColor: Colors.transparent,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              primaryColor.withOpacity(0.8),
                            ),
                          )
                          : null,
                ),
              ),

              if (index < widget.totalSteps - 1)
                Container(width: 8, height: 4, color: Colors.transparent),
            ],
          ),
        );
      }),
    );
  }

  /// 构建数量信息
  Widget _buildCountInfo() {
    String countText = '';

    if (widget.loadedCount != null && widget.totalCount != null) {
      countText = '${widget.loadedCount}/${widget.totalCount}';
    } else if (widget.loadedCount != null) {
      countText = '已加载 ${widget.loadedCount}';
    } else if (widget.totalCount != null) {
      countText = '共 ${widget.totalCount} 项';
    }

    if (countText.isEmpty) return const SizedBox.shrink();

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          countText,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),

        Text(
          '${(widget.stepProgress * 100).round()}%',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 获取步骤图标
  IconData _getStepIcon(int step) {
    switch (step) {
      case 1:
        return FontAwesomeIcons.listUl;
      case 2:
        return FontAwesomeIcons.download;
      case 3:
        return FontAwesomeIcons.filter;
      default:
        return FontAwesomeIcons.gear;
    }
  }
}

/// 简化版加载指示器
class SimpleLoadingIndicator extends StatelessWidget {
  final String message;
  final Color? color;
  final bool showAtBottom;

  const SimpleLoadingIndicator({
    super.key,
    required this.message,
    this.color,
    this.showAtBottom = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                color ?? Theme.of(context).primaryColor,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Text(message, style: Theme.of(context).textTheme.bodyMedium),
        ],
      ),
    );
  }
}
