import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 标记动画管理器
///
/// 负责管理地图标记的各种动画效果：
/// - 新标记的出现动画
/// - 标记更新的视觉反馈
/// - 标记移除的消失动画
/// - 批量更新的协调动画
class MarkerAnimationManager {
  static const Duration _defaultAnimationDuration = Duration(milliseconds: 300);
  static const Duration _staggerDelay = Duration(milliseconds: 50);

  /// 新标记出现动画
  static Widget buildAppearAnimation({
    required Widget child,
    required AnimationController controller,
    int? index,
    Duration? delay,
  }) {
    final actualDelay =
        delay ??
        (index != null ? Duration(milliseconds: index * 50) : Duration.zero);

    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        // 计算延迟后的动画进度
        final delayedProgress = _calculateDelayedProgress(
          controller.value,
          actualDelay,
          _defaultAnimationDuration,
        );

        if (delayedProgress <= 0) {
          return const SizedBox.shrink();
        }

        // 组合多种动画效果
        return Transform.scale(
          scale: _easeOutBack(delayedProgress),
          child: Opacity(opacity: delayedProgress, child: child),
        );
      },
      child: child,
    );
  }

  /// 标记更新动画（脉冲效果）
  static Widget buildUpdateAnimation({
    required Widget child,
    required AnimationController controller,
    Color? pulseColor,
  }) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        final pulseValue = (1.0 + 0.3 * (1.0 - controller.value));

        return Stack(
          alignment: Alignment.center,
          children: [
            // 脉冲背景
            if (controller.value > 0)
              Container(
                width: 40 * pulseValue,
                height: 40 * pulseValue,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: (pulseColor ?? Colors.blue).withOpacity(
                    0.3 * (1.0 - controller.value),
                  ),
                ),
              ),
            // 原始标记
            Transform.scale(
              scale: 1.0 + 0.1 * (1.0 - controller.value),
              child: child,
            ),
          ],
        );
      },
      child: child,
    );
  }

  /// 标记消失动画
  static Widget buildDisappearAnimation({
    required Widget child,
    required AnimationController controller,
  }) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        final progress = 1.0 - controller.value;

        return Transform.scale(
          scale: progress * 0.8,
          child: Opacity(opacity: progress, child: child),
        );
      },
      child: child,
    );
  }

  /// 批量标记动画容器
  static Widget buildBatchAnimationContainer({
    required List<Widget> children,
    required AnimationController controller,
    Duration? staggerDelay,
  }) {
    final actualStaggerDelay = staggerDelay ?? _staggerDelay;

    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return Stack(
          children:
              children.asMap().entries.map((entry) {
                final index = entry.key;
                final child = entry.value;

                return buildAppearAnimation(
                  child: child,
                  controller: controller,
                  index: index,
                  delay: Duration(
                    milliseconds: index * actualStaggerDelay.inMilliseconds,
                  ),
                );
              }).toList(),
        );
      },
    );
  }

  /// 标记高亮动画（用于搜索结果等）
  static Widget buildHighlightAnimation({
    required Widget child,
    required AnimationController controller,
    Color? highlightColor,
  }) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        final glowIntensity = (1.0 + controller.value) / 2.0;

        return Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: (highlightColor ?? Colors.yellow).withOpacity(
                  0.6 * glowIntensity,
                ),
                blurRadius: 20 * glowIntensity,
                spreadRadius: 5 * glowIntensity,
              ),
            ],
          ),
          child: child,
        );
      },
      child: child,
    );
  }

  /// 加载进度动画
  static Widget buildLoadingAnimation({
    required AnimationController controller,
    Color? color,
    double size = 24.0,
  }) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            value: null, // 无限旋转
            strokeWidth: 2.0,
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? Theme.of(context).primaryColor,
            ),
          ),
        );
      },
    );
  }

  /// 成功反馈动画（勾选标记）
  static Widget buildSuccessAnimation({
    required AnimationController controller,
    Color? color,
    double size = 24.0,
  }) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: (color ?? Colors.green).withOpacity(controller.value),
          ),
          child: Icon(
            Icons.check,
            color: Colors.white,
            size: size * 0.6 * controller.value,
          ),
        );
      },
    );
  }

  /// 错误反馈动画（摇摆效果）
  static Widget buildErrorAnimation({
    required Widget child,
    required AnimationController controller,
  }) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        final shakeValue = controller.value * 4 * 3.14159; // 4次摇摆
        final offset = 10 * controller.value * (1.0 - controller.value) * 4;

        return Transform.translate(
          offset: Offset(offset * math.sin(shakeValue), 0),
          child: child,
        );
      },
      child: child,
    );
  }

  // ==================== 辅助方法 ====================

  /// 计算延迟后的动画进度
  static double _calculateDelayedProgress(
    double overallProgress,
    Duration delay,
    Duration totalDuration,
  ) {
    final delayRatio = delay.inMilliseconds / totalDuration.inMilliseconds;

    if (overallProgress <= delayRatio) {
      return 0.0;
    }

    return (overallProgress - delayRatio) / (1.0 - delayRatio);
  }

  /// 缓出回弹动画曲线
  static double _easeOutBack(double t) {
    const c1 = 1.70158;
    const c3 = c1 + 1;

    return 1 + c3 * ((t - 1) * (t - 1) * (t - 1)) + c1 * ((t - 1) * (t - 1));
  }

  /// 触发触觉反馈
  static void triggerHapticFeedback(HapticFeedbackType type) {
    switch (type) {
      case HapticFeedbackType.light:
        HapticFeedback.lightImpact();
        break;
      case HapticFeedbackType.medium:
        HapticFeedback.mediumImpact();
        break;
      case HapticFeedbackType.heavy:
        HapticFeedback.heavyImpact();
        break;
      case HapticFeedbackType.selection:
        HapticFeedback.selectionClick();
        break;
    }
  }

  /// 创建动画控制器
  static AnimationController createController({
    required TickerProvider vsync,
    Duration? duration,
  }) {
    return AnimationController(
      duration: duration ?? _defaultAnimationDuration,
      vsync: vsync,
    );
  }

  /// 创建交错动画控制器组
  static List<AnimationController> createStaggeredControllers({
    required TickerProvider vsync,
    required int count,
    Duration? duration,
    Duration? staggerDelay,
  }) {
    final controllers = <AnimationController>[];
    final actualDuration = duration ?? _defaultAnimationDuration;

    for (int i = 0; i < count; i++) {
      controllers.add(
        AnimationController(duration: actualDuration, vsync: vsync),
      );
    }

    return controllers;
  }

  /// 启动交错动画
  static Future<void> startStaggeredAnimation(
    List<AnimationController> controllers, {
    Duration? staggerDelay,
  }) async {
    final actualStaggerDelay = staggerDelay ?? _staggerDelay;

    for (int i = 0; i < controllers.length; i++) {
      if (i > 0) {
        await Future.delayed(actualStaggerDelay);
      }
      controllers[i].forward();
    }
  }

  /// 清理动画控制器
  static void disposeControllers(List<AnimationController> controllers) {
    for (final controller in controllers) {
      controller.dispose();
    }
  }
}

/// 触觉反馈类型枚举
enum HapticFeedbackType { light, medium, heavy, selection }
