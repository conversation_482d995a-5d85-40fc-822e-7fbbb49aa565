import 'package:flutter/material.dart';

import '../models/fishing_activity.dart';
import '../services/unified_image_service.dart';

/// 活动标记组件
///
/// 专门用于在地图上显示钓鱼活动的方形标记
/// 区别于圆形的钓点标记，提供视觉差异化
class ActivityMarker extends StatefulWidget {
  /// 活动数据
  final FishingActivity activity;

  /// 标记大小
  final double size;

  /// 点击回调
  final VoidCallback? onTap;

  /// 圆心偏移量
  final Offset centerOffset;

  /// 边框颜色（可选，如果不提供则根据活动类型自动选择）
  final Color? borderColor;

  const ActivityMarker({
    super.key,
    required this.activity,
    required this.size,
    this.onTap,
    this.centerOffset = Offset.zero,
    this.borderColor,
  });

  @override
  State<ActivityMarker> createState() => _ActivityMarkerState();
}

class _ActivityMarkerState extends State<ActivityMarker>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    debugPrint(
      '🔍 [ActivityMarker] 渲染活动标记: ${widget.activity.id} - ${widget.activity.title}',
    );
    debugPrint('🔍 [ActivityMarker] 标记大小: ${widget.size}');
    debugPrint('🔍 [ActivityMarker] 活动类型: ${widget.activity.activityType}');

    return GestureDetector(
      onTap: widget.onTap,
      onLongPress: widget.onTap,
      behavior: HitTestBehavior.opaque,
      child: CustomPaint(
        size: Size(widget.size, widget.size),
        painter: _ActivityMarkerPainter(
          activity: widget.activity,
          size: widget.size,
          centerOffset: widget.centerOffset,
          borderColor: widget.borderColor ?? _getActivityTypeColor(),
        ),
        child: Stack(children: [_buildPhotoContent(), _buildStatusIndicator()]),
      ),
    );
  }

  /// 构建照片内容
  Widget _buildPhotoContent() {
    final photoSize = widget.size * 0.7; // 照片区域占标记的70%
    final photoOffset = (widget.size - photoSize) / 2;

    return Positioned(
      left: photoOffset,
      top: photoOffset*0.28,
      child: Container(
        width: photoSize,
        height: photoSize,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8), // 方形圆角
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: _buildImage(),
        ),
      ),
    );
  }

  /// 构建图像
  Widget _buildImage() {
    final imageService = UnifiedImageService();

    // 检查是否有用户上传的照片
    if (widget.activity.images != null &&
        widget.activity.images!['images'] != null) {

      // 安全地检查images字段是否为List类型
      final imagesData = widget.activity.images!['images'];
      if (imagesData is List && imagesData.isNotEmpty) {
        final firstImage = imagesData.first;

        // 确保firstImage是Map类型
        if (firstImage is Map<String, dynamic>) {
          final imageUrl = firstImage['url'] ?? firstImage['thumbnail_url'];

          if (imageUrl != null && imageUrl is String) {
            return imageService.buildCachedSignedImage(
              key: ValueKey('activity_photo_${widget.activity.id}'),
              originalUrl: imageUrl,
              fit: BoxFit.cover,
              placeholder: _buildDefaultImage(),
              errorWidget: _buildDefaultImage(),
            );
          }
        }
      }
    }

    // 使用默认照片
    return _buildDefaultImage();
  }

  /// 构建默认图像
  Widget _buildDefaultImage() {
    // 根据创建者ID的奇偶性选择默认照片
    final useGirl = widget.activity.creatorId.hashCode % 2 == 0;
    final defaultImagePath =
        useGirl
            ? 'assets/images/fishingGirl6.jpg'
            : 'assets/images/fishingBoy1.jpg';

    return Image.asset(
      defaultImagePath,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          color: _getActivityTypeColor().withValues(alpha: 0.1),
          child: Icon(
            Icons.person,
            color: _getActivityTypeColor(),
            size: widget.size * 0.3,
          ),
        );
      },
    );
  }

  /// 构建状态指示器
  Widget _buildStatusIndicator() {
    final indicatorColor = _getStatusIndicatorColor();
    if (indicatorColor == null) return const SizedBox.shrink();

    return Positioned(
      right: 2,
      top: 2,
      child: Container(
        width: 12,
        height: 12,
        decoration: BoxDecoration(
          color: indicatorColor,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 1),
        ),
      ),
    );
  }

  /// 获取活动类型颜色
  Color _getActivityTypeColor() {
    switch (widget.activity.activityType) {
      case 'lure':
        return const Color(0xFFFF9800); // 橙色 - 路亚
      case 'platform':
        return const Color(0xFF9C27B0); // 紫色 - 台钓
      case 'night':
        return const Color(0xFF4CAF50); // 绿色 - 夜钓
      case 'pond':
        return const Color(0xFF2196F3); // 蓝色 - 鱼塘
      case 'sea':
        return const Color(0xFF000000); // 黑色 - 海钓
      default:
        return const Color(0xFF4CAF50); // 默认绿色
    }
  }

  /// 获取状态指示器颜色
  Color? _getStatusIndicatorColor() {
    if (widget.activity.isExpired) return Colors.grey;
    if (widget.activity.isOngoing) return Colors.green;
    if (widget.activity.isStartingSoon) return Colors.orange;
    return null; // 等待中不显示指示器
  }
}

/// 活动标记绘制器
class _ActivityMarkerPainter extends CustomPainter {
  final FishingActivity activity;
  final double size;
  final Offset centerOffset;
  final Color borderColor;

  _ActivityMarkerPainter({
    required this.activity,
    required this.size,
    required this.centerOffset,
    required this.borderColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..style = PaintingStyle.fill
          ..color = Colors.white;

    final borderPaint =
        Paint()
          ..style = PaintingStyle.stroke
          ..color = borderColor
          ..strokeWidth = 3.0;

    // 三角形填充颜色（使用边框颜色）
    final triangleFillPaint =
        Paint()
          ..style = PaintingStyle.fill
          ..color = borderColor;

    final center = Offset(size.width / 2, size.height * 0.4) + centerOffset;
    final rectSize = (size.width - 6) * 0.8; // 留出边框空间

    // 绘制方形主体
    final rect = Rect.fromCenter(
      center: center,
      width: rectSize,
      height: rectSize,
    );

    final roundedRect = RRect.fromRectAndRadius(rect, const Radius.circular(8));
    canvas.drawRRect(roundedRect, paint);
    canvas.drawRRect(roundedRect, borderPaint);

    // 绘制底部三角指针
    _drawTrianglePointer(canvas, center, rectSize / 2, triangleFillPaint, borderPaint);
  }

  /// 绘制三角指针
  void _drawTrianglePointer(
    Canvas canvas,
    Offset center,
    double radius,
    Paint fillPaint,
    Paint borderPaint,
  ) {
    final trianglePath = Path();
    final triangleLeft = Offset(center.dx - radius * 0.3, center.dy + radius);
    final triangleRight = Offset(center.dx + radius * 0.3, center.dy + radius);
    final triangleBottom = Offset(center.dx, center.dy + radius * 1.5);

    trianglePath.moveTo(triangleLeft.dx, triangleLeft.dy);
    trianglePath.lineTo(triangleRight.dx, triangleRight.dy);
    trianglePath.lineTo(triangleBottom.dx, triangleBottom.dy);
    trianglePath.close();

    canvas.drawPath(trianglePath, fillPaint);
    canvas.drawPath(trianglePath, borderPaint);
  }



  @override
  bool shouldRepaint(covariant _ActivityMarkerPainter oldDelegate) {
    return oldDelegate.activity.id != activity.id ||
        oldDelegate.size != size ||
        oldDelegate.centerOffset != centerOffset ||
        oldDelegate.borderColor != borderColor;
  }
}
