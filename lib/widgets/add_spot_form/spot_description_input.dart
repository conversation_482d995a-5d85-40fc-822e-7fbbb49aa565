import 'package:flutter/material.dart';
import '../emoji_text_field.dart';
import '../../theme/unified_theme.dart';

/// 通用文本输入组件（支持emoji）
///
/// 功能：
/// - 多行文本输入
/// - 可选字数限制和验证
/// - 表单验证
/// - 实时字数检查和颜色变化
/// - 可自定义标题和占位符
class CustomTextInput extends StatefulWidget {
  /// 文本控制器
  final TextEditingController controller;

  /// 标题文本
  final String labelText;

  /// 占位符文本
  final String hintText;

  /// 最大字数
  final int maxLength;

  /// 最大行数
  final int maxLines;

  /// 是否启用
  final bool enabled;

  /// 验证函数（额外的自定义验证）
  final String? Function(String?)? validator;

  /// 是否显示字数统计
  final bool showCharacterCount;

  /// 最小字数要求（用于字数验证）
  final int? minLength;

  /// 自定义字数提示信息
  final String? characterCountMessage;

  /// 是否必填
  final bool requireInput;

  /// 自定义空值错误信息
  final String? customEmptyMessage;

  /// 自定义最小字数错误信息
  final String? customMinLengthMessage;

  /// 文本样式
  final TextStyle? style;

  const CustomTextInput({
    super.key,
    required this.controller,
    required this.labelText,
    required this.hintText,
    this.maxLength = 500,
    this.maxLines = 4,
    this.enabled = true,
    this.validator,
    this.showCharacterCount = false,
    this.minLength,
    this.characterCountMessage,
    this.requireInput = false,
    this.customEmptyMessage,
    this.customMinLengthMessage,
    this.style,
  });

  @override
  State<CustomTextInput> createState() => _CustomTextInputState();
}

class _CustomTextInputState extends State<CustomTextInput> {
  bool _isValid = false;
  int _currentLength = 0;
  bool _hasUserInteracted = false; // 跟踪用户是否已经与字段交互过

  @override
  void initState() {
    super.initState();
    // 监听文本变化
    widget.controller.addListener(_onTextChanged);
    // 初始化状态
    _onTextChanged();
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onTextChanged() {
    final text = widget.controller.text.trim();
    final length = text.length;
    final isValid = widget.minLength == null || length >= widget.minLength!;

    if (_currentLength != length || _isValid != isValid) {
      setState(() {
        _currentLength = length;
        _isValid = isValid;
      });
    }
  }

  /// 标记用户已经与字段交互过
  void _markUserInteracted() {
    if (!_hasUserInteracted) {
      setState(() {
        _hasUserInteracted = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: AppTheme.spacingS),
        EmojiTextField(
          controller: widget.controller,
          labelText: widget.labelText,
          hintText: widget.hintText,
          maxLines: widget.maxLines,
          maxLength: widget.maxLength,
          enabled: widget.enabled,
          validator: _buildValidator,
          style: widget.style,
          // 在用户交互后验证，但通过状态控制避免初始验证
          autovalidateMode: AutovalidateMode.onUserInteraction,
          onTap: _markUserInteracted,
          onChanged: (value) {
            _markUserInteracted();
          },
        ),
        // 字数提示（仅在启用字数统计且有内容时显示）
        if (widget.showCharacterCount && _currentLength > 0)
          Padding(
            padding: const EdgeInsets.only(top: 4, left: 12),
            child: Text(
              _buildCharacterCountText(),
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
            ),
          ),
      ],
    );
  }

  /// 构建验证器（内置验证 + 自定义验证）
  String? _buildValidator(String? value) {
    // 如果用户还没有与字段交互过，不返回错误（避免初始状态显示错误）
    if (!_hasUserInteracted) {
      return null;
    }

    // 内置验证：必填检查
    if (widget.requireInput) {
      if (value == null || value.trim().isEmpty) {
        return widget.customEmptyMessage ?? '请输入${widget.labelText}';
      }
    }

    // 内置验证：最小字数检查（智能错误信息）
    if (widget.minLength != null && value != null) {
      final trimmedValue = value.trim();
      if (trimmedValue.isNotEmpty && trimmedValue.length < widget.minLength!) {
        // 如果有内容但字数不足，显示字数不足的错误
        return widget.customMinLengthMessage ??
            '${widget.labelText}至少需要${widget.minLength}个字符';
      }
    }

    // 自定义验证
    if (widget.validator != null) {
      return widget.validator!(value);
    }

    return null;
  }

  /// 构建字数统计文本
  String _buildCharacterCountText() {
    if (widget.characterCountMessage != null) {
      return widget.characterCountMessage!;
    }

    // 简化字数统计显示，不显示验证状态
    if (widget.minLength != null) {
      return '$_currentLength/${widget.minLength} 字符';
    }

    return '$_currentLength/${widget.maxLength} 字符';
  }
}

/// 为了向后兼容，保留原来的类名作为别名
typedef SpotDescriptionInput = CustomTextInput;
