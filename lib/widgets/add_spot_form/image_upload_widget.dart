import 'dart:async';
import '../snackbar.dart';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:video_compress/video_compress.dart';
import '../../services/unified_image_service.dart';
import '../common/media_viewer.dart';

/// 媒体类型枚举
enum MediaType { image, video }

/// 媒体上传项目，包含文件和上传状态（支持图片和视频）
class ImageUploadItem {
  final File file;
  final bool isFromCamera; // 是否来自相机拍摄
  final MediaType mediaType; // 媒体类型
  final Duration? videoDuration; // 视频时长（仅视频有效）
  final int? fileSize; // 文件大小（字节）
  final File? thumbnailFile; // 视频缩略图文件（仅视频有效）
  double uploadProgress;
  bool isUploading;
  bool isCompleted;
  bool isCancelled; // 是否已取消
  String? uploadedUrl;
  String? thumbnailUrl;
  String? errorMessage;

  // 用于取消上传的控制器
  Completer<void>? _uploadCanceller;

  ImageUploadItem({
    required this.file,
    this.isFromCamera = false,
    this.mediaType = MediaType.image, // 默认为图片类型，保持向后兼容
    this.videoDuration,
    this.fileSize,
    this.thumbnailFile, // 视频缩略图文件
    this.uploadProgress = 0.0,
    this.isUploading = false,
    this.isCompleted = false,
    this.isCancelled = false,
    this.uploadedUrl,
    this.thumbnailUrl,
    this.errorMessage,
  });

  /// 取消上传
  void cancelUpload() {
    if (isUploading && !isCompleted) {
      isCancelled = true;
      isUploading = false;
      uploadProgress = 0.0;
      errorMessage = '上传已取消';
      _uploadCanceller?.complete();
    }
  }

  /// 设置上传取消控制器
  void setUploadCanceller(Completer<void> canceller) {
    _uploadCanceller = canceller;
  }

  /// 重置状态（用于重试）
  void resetForRetry() {
    isUploading = false;
    isCompleted = false;
    isCancelled = false;
    uploadProgress = 0.0;
    errorMessage = null;
    uploadedUrl = null;
    thumbnailUrl = null;
    _uploadCanceller = null;
  }

  /// 判断是否为图片
  bool get isImage => mediaType == MediaType.image;

  /// 判断是否为视频
  bool get isVideo => mediaType == MediaType.video;

  /// 获取文件扩展名
  String get fileExtension {
    return file.path.split('.').last.toLowerCase();
  }

  /// 获取格式化的文件大小
  String get formattedFileSize {
    if (fileSize == null) return '';
    final sizeInMB = fileSize! / (1024 * 1024);
    return '${sizeInMB.toStringAsFixed(1)}MB';
  }

  /// 获取格式化的视频时长
  String get formattedDuration {
    if (videoDuration == null) return '';
    final minutes = videoDuration!.inMinutes;
    final seconds = videoDuration!.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}

/// 媒体上传组件（支持图片和视频）
///
/// 功能：
/// - 媒体选择（相机拍照/录视频、相册选择）
/// - 媒体预览（图片缩略图、视频缩略图）
/// - 上传进度显示
/// - 媒体删除
/// - 支持图片和视频混合上传
class ImageUploadWidget extends StatefulWidget {
  /// 已选择的媒体列表（图片和视频）
  final List<ImageUploadItem> selectedImages;

  /// 媒体添加回调
  final Function(List<ImageUploadItem>) onImagesAdded;

  /// 媒体删除回调
  final Function(int) onImageRemoved;

  /// 媒体取消上传回调
  final Function(int)? onImageCancelled;

  /// 媒体重试上传回调
  final Function(int)? onImageRetry;

  /// 最大媒体数量
  final int maxImages;

  /// 是否启用
  final bool enabled;

  const ImageUploadWidget({
    super.key,
    required this.selectedImages,
    required this.onImagesAdded,
    required this.onImageRemoved,
    this.onImageCancelled,
    this.onImageRetry,
    this.maxImages = 9,
    this.enabled = true,
  });

  @override
  State<ImageUploadWidget> createState() => _ImageUploadWidgetState();
}

class _ImageUploadWidgetState extends State<ImageUploadWidget> {
  final UnifiedImageService _imageService = UnifiedImageService();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              '钓点照片视频',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: Colors.black87,
              ),
            ),
            const Spacer(),
            Text(
              '${widget.selectedImages.length}/${widget.maxImages}',
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 80,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount:
                widget.selectedImages.length +
                (widget.selectedImages.length < widget.maxImages ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == widget.selectedImages.length) {
                // 添加媒体的方框
                return Container(
                  width: 80,
                  height: 80,
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.grey.shade400,
                      width: 2,
                      style: BorderStyle.solid,
                    ),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey.shade100,
                  ),
                  child: InkWell(
                    onTap:
                        widget.enabled
                            ? () => _showImagePickerOptions(context)
                            : null,
                    borderRadius: BorderRadius.circular(8),
                    child: const Icon(Icons.add, size: 32, color: Colors.grey),
                  ),
                );
              } else {
                // 已选择的媒体缩略图
                return Container(
                  width: 80,
                  height: 80,
                  margin: const EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      GestureDetector(
                        onTap: () => _openMediaViewer(context, index),
                        child: Hero(
                          tag: 'media_hero_$index',
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: _buildMediaThumbnail(
                              widget.selectedImages[index],
                            ),
                          ),
                        ),
                      ),
                      // 根据不同状态显示不同的操作按钮
                      if (widget.enabled)
                        _buildActionButton(widget.selectedImages[index], index),
                    ],
                  ),
                );
              }
            },
          ),
        ),
      ],
    );
  }

  /// 显示图片选择选项
  Future<void> _showImagePickerOptions(BuildContext context) async {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.camera_alt, color: Colors.blue),
                title: const Text('相机拍摄'),
                subtitle: const Text('拍摄照片或录制视频（支持实拍认证）'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromCamera(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library, color: Colors.green),
                title: const Text('从相册选择'),
                subtitle: const Text('选择已有照片'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromGallery(context);
                },
              ),
              // ListTile(
              //   leading: const Icon(Icons.cancel, color: Colors.grey),
              //   title: const Text('取消'),
              //   onTap: () => Navigator.pop(context),
              // ),
            ],
          ),
        );
      },
    );
  }

  /// 从相机拍照或录视频（使用微信风格相机选择器）
  Future<void> _pickImageFromCamera(BuildContext context) async {
    try {
      // 检查并请求必要的权限
      final hasPermissions = await _checkAndRequestPermissions(context);
      if (!hasPermissions) {
        return; // 权限被拒绝，退出
      }

      // 使用wechat_camera_picker，优化视频录制配置
      // ResolutionPreset选项分析
      // low: 352x288 (iOS), 320x240 (Android) - 4:3比例
      // medium: 640x480 (iOS), 720x480 (Android) - 4:3和3:2比例
      // high: 1280x720 - 16:9比例 ⭐
      // veryHigh: 1920x1080 - 16:9比例 ⭐
      // ultraHigh: 3840x2160 - 16:9比例
      // max: 最高可用分辨率
      final AssetEntity? result = await CameraPicker.pickFromCamera(
        context,
        pickerConfig: const CameraPickerConfig(
          enableRecording: true, // 启用视频录制
          maximumRecordingDuration: Duration(seconds: 10), //[*参数设置*] 限制录制时长为10秒
          resolutionPreset:
              ResolutionPreset.high, // [*参数优化*] 使用720p(16:9)分辨率，符合主流视频标准
          theme: null, // 使用默认主题
        ),
      );

      if (result != null) {
        // 获取文件
        final file = await result.file;
        if (file != null) {
          if (result.type == AssetType.video) {
            // 处理视频文件：压缩和生成缩略图
            await _processVideoFile(file, result);
          } else {
            // 处理图片文件：立即显示，后台处理
            await _processImageFile(file);
          }
        }
      }
    } catch (e) {
      debugPrint('相机拍摄失败: $e');
      if (context.mounted) {
        SnackBarService.showError(context, '相机拍摄失败，请重试');
      }
    }
  }

  /// 检查并请求相机和存储权限
  Future<bool> _checkAndRequestPermissions(BuildContext context) async {
    try {
      // 需要检查的权限列表
      final permissions = <Permission>[
        Permission.camera, // 相机权限
        Permission.microphone, // 麦克风权限（录视频需要）
      ];

      // 根据Android版本添加存储权限
      if (Platform.isAndroid) {
        // Android 13+ 使用新的媒体权限
        if (await _isAndroid13OrHigher()) {
          permissions.addAll([
            Permission.photos, // 照片权限
            Permission.videos, // 视频权限
          ]);
        } else {
          // Android 12 及以下使用传统存储权限
          permissions.add(Permission.storage);
        }
      } else if (Platform.isIOS) {
        permissions.add(Permission.photos);
      }

      // 检查权限状态
      final statuses = await permissions.request();

      // 检查是否所有权限都被授予
      final allGranted = statuses.values.every(
        (status) => status == PermissionStatus.granted,
      );

      if (!allGranted) {
        // 找出被拒绝的权限
        final deniedPermissions =
            statuses.entries
                .where((entry) => entry.value != PermissionStatus.granted)
                .map((entry) => entry.key)
                .toList();

        await _handlePermissionDenied(context, deniedPermissions);
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('权限检查失败: $e');
      if (context.mounted) {
        SnackBarService.showError(context, '权限检查失败，请重试');
      }
      return false;
    }
  }

  /// 检查是否为Android 13或更高版本
  Future<bool> _isAndroid13OrHigher() async {
    if (!Platform.isAndroid) return false;

    try {
      // 简单的版本检查，Android 13 对应 API 33
      return true; // 暂时返回true，使用新权限模式
    } catch (e) {
      return false;
    }
  }

  /// 处理权限被拒绝的情况
  Future<void> _handlePermissionDenied(
    BuildContext context,
    List<Permission> deniedPermissions,
  ) async {
    if (!context.mounted) return;

    final permissionNames = deniedPermissions
        .map((p) {
          switch (p) {
            case Permission.camera:
              return '相机';
            case Permission.microphone:
              return '麦克风';
            case Permission.storage:
              return '存储';
            case Permission.photos:
              return '照片';
            case Permission.videos:
              return '视频';
            default:
              return '未知';
          }
        })
        .join('、');

    final shouldOpenSettings = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('权限需要'),
            content: Text('需要$permissionNames权限才能使用相机功能。请在设置中授予权限。'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('去设置'),
              ),
            ],
          ),
    );

    if (shouldOpenSettings == true) {
      await openAppSettings();
    }
  }

  /// 从相册选择图片
  Future<void> _pickImageFromGallery(BuildContext context) async {
    try {
      final ImagePicker picker = ImagePicker();
      final List<XFile> images = await picker.pickMultiImage(
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (images.isNotEmpty) {
        final newItems =
            images
                .map(
                  (xFile) => ImageUploadItem(
                    file: File(xFile.path),
                    isFromCamera: false,
                  ),
                )
                .toList();

        widget.onImagesAdded(newItems);

        // 图片选择成功，不显示提示
      }
    } catch (e) {
      debugPrint('选择图片失败: $e');
      if (context.mounted) {
        SnackBarService.showError(context, '选择图片失败，请重试');
      }
    }
  }

  /// 处理图片文件：立即显示，后台压缩和上传（移动端最佳实践）
  Future<void> _processImageFile(File imageFile) async {
    try {
      // 1. 立即创建ImageUploadItem并显示（使用原图作为临时显示）
      final imageItem = ImageUploadItem(
        file: imageFile,
        isFromCamera: true,
        mediaType: MediaType.image,
        fileSize: await imageFile.length(),
      );

      // 立即添加到UI显示
      widget.onImagesAdded([imageItem]);

      // 2. 显示处理提示
      if (mounted) {
        debugPrint('正在处理图片，请稍候...');
      }
    } catch (e) {
      debugPrint('❌ [图片处理] 图片处理失败: $e');
      if (mounted) {
        SnackBarService.showError(context, '图片处理失败: $e');
      }
    }
  }

  /// 构建媒体缩略图，支持图片和视频，支持黑白显示和渐进式彩色
  Widget _buildMediaThumbnail(ImageUploadItem item) {
    Widget imageWidget;

    if (item.mediaType == MediaType.video) {
      // 视频类型：实现渐进式加载策略
      if (item.thumbnailFile != null) {
        // 优先显示本地缩略图文件

        Widget localImageWidget =
            kIsWeb
                ? Image.network(
                  item.thumbnailFile!.path,
                  width: 80,
                  height: 80,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildVideoErrorWidget();
                  },
                )
                : Image.file(
                  item.thumbnailFile!,
                  width: 80,
                  height: 80,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildVideoErrorWidget();
                  },
                );

        // 如果有远程缩略图URL且上传完成，实现渐进式加载
        if (item.thumbnailUrl != null &&
            item.thumbnailUrl!.isNotEmpty &&
            item.isCompleted) {
          imageWidget = _buildProgressiveSignedImage(
            localImage: localImageWidget,
            remoteUrl: item.thumbnailUrl!,
            width: 80,
            height: 80,
          );
        } else {
          imageWidget = localImageWidget;
        }
      } else {
        // 没有本地缩略图时显示默认视频图标
        debugPrint('🖼️ [UI显示] 使用默认视频图标');
        imageWidget = _buildVideoErrorWidget();
      }
    } else {
      // 图片类型：显示原文件
      imageWidget =
          kIsWeb
              ? Image.network(
                item.file.path,
                width: 80,
                height: 80,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 80,
                    height: 80,
                    color: Colors.grey[300],
                    child: const Icon(Icons.image),
                  );
                },
              )
              : Image.file(item.file, width: 80, height: 80, fit: BoxFit.cover);
    }

    // 如果还没开始上传或正在上传，显示黑白图片
    if (!item.isCompleted) {
      imageWidget = ColorFiltered(
        colorFilter: const ColorFilter.matrix([
          0.2126, 0.7152, 0.0722, 0, 0, // Red channel
          0.2126, 0.7152, 0.0722, 0, 0, // Green channel
          0.2126, 0.7152, 0.0722, 0, 0, // Blue channel
          0, 0, 0, 1, 0, // Alpha channel
        ]),
        child: imageWidget,
      );

      // 如果正在上传，显示渐进式彩色效果
      if (item.isUploading && item.uploadProgress > 0) {
        // 为渐进式效果构建正确的彩色图片
        Widget colorImageWidget;
        if (item.mediaType == MediaType.video) {
          // 视频类型：使用缩略图显示渐进式效果
          if (item.thumbnailFile != null) {
            colorImageWidget =
                kIsWeb
                    ? Image.network(
                      item.thumbnailFile!.path,
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                    )
                    : Image.file(
                      item.thumbnailFile!,
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                    );
          } else {
            // 没有缩略图时使用默认图标
            colorImageWidget = _buildVideoErrorWidget();
          }
        } else {
          // 图片类型：使用原文件
          colorImageWidget =
              kIsWeb
                  ? Image.network(
                    item.file.path,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                  )
                  : Image.file(
                    item.file,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                  );
        }

        imageWidget = Stack(
          children: [
            imageWidget, // 黑白背景
            ClipRect(
              child: Align(
                alignment: Alignment.centerLeft,
                widthFactor: item.uploadProgress,
                child: colorImageWidget,
              ),
            ),
          ],
        );
      }
    }

    // 如果是视频，添加播放按钮覆盖层和时长显示
    if (item.mediaType == MediaType.video) {
      imageWidget = Stack(
        children: [
          imageWidget,
          // 播放按钮覆盖层
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Icon(
                  Icons.play_circle_filled,
                  color: Colors.white,
                  size: 32,
                ),
              ),
            ),
          ),
          // 视频时长显示
          if (item.videoDuration != null)
            Positioned(
              bottom: 4,
              right: 4,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  _formatDuration(item.videoDuration!),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      );
    }

    return imageWidget;
  }

  /// 格式化视频时长显示
  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// 构建视频错误显示组件
  Widget _buildVideoErrorWidget() {
    return Container(
      width: 80,
      height: 80,
      color: Colors.grey[300],
      child: const Icon(Icons.videocam, color: Colors.grey, size: 32),
    );
  }

  /// 构建渐进式签名图片加载组件（使用预签名URL）
  Widget _buildProgressiveSignedImage({
    required Widget localImage,
    required String remoteUrl,
    required double width,
    required double height,
  }) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: FutureBuilder<bool>(
        key: ValueKey(remoteUrl), // 确保每个URL都有独立的状态
        future: _preloadSignedImage(remoteUrl),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done &&
              snapshot.data == true) {
            // 预加载成功，显示远程签名图片
            return _imageService.buildCachedSignedImage(
              key: ValueKey('remote_$remoteUrl'),
              originalUrl: remoteUrl,
              width: width,
              height: height,
              fit: BoxFit.cover,
              placeholder: localImage, // 加载时显示本地图片
              errorWidget: localImage, // 错误时回退到本地图片
            );
          } else {
            // 预加载中或失败，显示本地图片
            return Container(
              key: ValueKey('local_$remoteUrl'),
              child: localImage,
            );
          }
        },
      ),
    );
  }

  /// 预加载签名图片（使用预签名URL）
  Future<bool> _preloadSignedImage(String imageUrl) async {
    try {
      // 使用统一图片服务预加载签名图片
      // 这里我们简单地尝试生成预签名URL来验证图片是否可访问
      final signedUrl = await _imageService.getSignedUrl(imageUrl);

      if (signedUrl.isNotEmpty) {
        // 使用预签名URL预加载图片
        final imageProvider = NetworkImage(signedUrl);
        await precacheImage(imageProvider, context);

        return true;
      } else {
        return false;
      }
    } catch (e) {
      debugPrint('❌ [渐进式签名加载] 签名图片预加载失败: $e');
      return false;
    }
  }

  /// 构建操作按钮（取消/删除/重试）
  Widget _buildActionButton(ImageUploadItem item, int index) {
    if (item.isUploading) {
      // 上传中 - 显示取消按钮
      return Positioned(
        top: 4,
        right: 4,
        child: GestureDetector(
          onTap: () => _cancelUpload(index),
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.9),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Icon(Icons.close, color: Colors.white, size: 16),
          ),
        ),
      );
    } else if (item.isCompleted) {
      // 上传完成 - 显示删除按钮
      return Positioned(
        top: 4,
        right: 4,
        child: GestureDetector(
          onTap: () => widget.onImageRemoved(index),
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.9),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Icon(Icons.close, color: Colors.white, size: 16),
          ),
        ),
      );
    } else if (item.errorMessage != null || item.isCancelled) {
      // 上传失败或已取消 - 显示重试和删除按钮
      return Positioned(
        top: 4,
        right: 4,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 重试按钮
            GestureDetector(
              onTap: () => _retryUpload(index),
              child: Container(
                width: 24,
                height: 24,
                margin: const EdgeInsets.only(right: 4),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(Icons.refresh, color: Colors.white, size: 16),
              ),
            ),
            // 删除按钮
            GestureDetector(
              onTap: () => widget.onImageRemoved(index),
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 16),
              ),
            ),
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  /// 取消上传
  void _cancelUpload(int index) {
    if (widget.onImageCancelled != null) {
      widget.onImageCancelled!(index);
    }
  }

  /// 重试上传
  void _retryUpload(int index) {
    if (widget.onImageRetry != null) {
      widget.onImageRetry!(index);
    }
  }

  /// 处理视频文件：立即显示缩略图，后台压缩和上传（移动端最佳实践）
  Future<void> _processVideoFile(File originalFile, AssetEntity result) async {
    try {
      // 1. 立即生成缩略图并显示
      final thumbnailFile = await VideoCompress.getFileThumbnail(
        originalFile.path,
        quality: 80, // 缩略图质量
        position: 1000, // 从1秒处提取缩略图
      );

      // 2. 立即创建视频项目并显示（使用缩略图显示）
      final videoItem = ImageUploadItem(
        file: originalFile, // 使用原始文件（UI只显示缩略图）
        isFromCamera: true,
        mediaType: MediaType.video,
        videoDuration: Duration(seconds: result.duration),
        fileSize: await originalFile.length(),
        thumbnailFile: thumbnailFile, // 缩略图文件
      );

      // 立即添加到UI显示
      widget.onImagesAdded([videoItem]);

      // 3. 显示处理提示
      if (mounted) {
        debugPrint('正在压缩视频，请稍候...');
      }

      // 4. 后台压缩视频（不阻塞UI显示）
      _compressVideoInBackground(originalFile, videoItem);
    } catch (e) {
      debugPrint('❌ [视频处理] 视频处理失败: $e');
      if (mounted) {
        SnackBarService.showError(context, '视频处理失败: $e');
      }
    }
  }

  /// 后台压缩视频（移动端最佳实践：不阻塞UI）
  Future<void> _compressVideoInBackground(
    File originalFile,
    ImageUploadItem videoItem,
  ) async {
    try {
      // 压缩视频
      final compressedInfo = await VideoCompress.compressVideo(
        originalFile.path,
        quality: VideoQuality.MediumQuality, // 使用中等质量
        deleteOrigin: false, // 不删除原文件
        includeAudio: true, // 包含音频
      );

      if (compressedInfo == null || compressedInfo.file == null) {
        throw Exception('视频压缩失败');
      }

      if (mounted) {
        debugPrint('视频压缩完成');
      }
    } catch (e) {
      debugPrint('❌ [后台压缩] 视频压缩失败: $e');
      if (mounted) {
        SnackBarService.showError(context, '视频压缩失败: $e');
      }
    }
  }

  /// 打开媒体查看器
  void _openMediaViewer(BuildContext context, int initialIndex) {
    // 只显示已完成上传的媒体，或者本地可预览的媒体
    final viewableItemsWithIndex = <MapEntry<int, ImageUploadItem>>[];

    for (int i = 0; i < widget.selectedImages.length; i++) {
      final item = widget.selectedImages[i];
      // 图片总是可以预览
      if (item.isImage) {
        viewableItemsWithIndex.add(MapEntry(i, item));
      }
      // 视频需要有缩略图才能预览
      else if (item.isVideo && item.thumbnailFile != null) {
        viewableItemsWithIndex.add(MapEntry(i, item));
      }
    }

    if (viewableItemsWithIndex.isEmpty) {
      SnackBarService.showError(context, '暂无可预览的媒体');
      return;
    }

    // 找到点击的媒体在可预览列表中的索引
    int viewableIndex = 0;
    for (int i = 0; i < viewableItemsWithIndex.length; i++) {
      if (viewableItemsWithIndex[i].key == initialIndex) {
        viewableIndex = i;
        break;
      }
    }

    // 创建带有原始索引信息的媒体项目列表
    final viewableItems =
        viewableItemsWithIndex.map((entry) => entry.value).toList();
    final originalIndices =
        viewableItemsWithIndex.map((entry) => entry.key).toList();

    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder:
            (context, animation, secondaryAnimation) => MediaViewer(
              mediaItems: viewableItems,
              initialIndex: viewableIndex,
              heroTagPrefix: 'media_hero',
              originalIndices: originalIndices,
            ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 300),
        reverseTransitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }
}
