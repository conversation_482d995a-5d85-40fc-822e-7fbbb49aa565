import 'package:flutter/material.dart';
import '../snackbar.dart';
import '../emoji_text_field.dart';
import '../../utils/tianditu_utils.dart';
import '../../theme/unified_theme.dart';
import 'package:latlong2/latlong.dart';

/// 钓点名称输入组件
///
/// 功能：
/// - 钓点名称输入
/// - 地名反查功能
/// - 表单验证
/// - 支持自动获取地名
class SpotNameInput extends StatefulWidget {
  /// 名称控制器
  final TextEditingController controller;

  /// 当前位置
  final LatLng location;

  /// 验证函数
  final String? Function(String?)? validator;

  /// 是否启用
  final bool enabled;

  /// 是否自动获取地名
  final bool autoFetchLocationName;

  /// 标签文本
  final String labelText;

  /// 地名后缀（如"钓点"、"活动"等）
  final String nameSuffix;

  const SpotNameInput({
    super.key,
    required this.controller,
    required this.location,
    this.validator,
    this.enabled = true,
    this.autoFetchLocationName = false,
    this.labelText = '钓点名称',
    this.nameSuffix = '钓点',
  });

  @override
  State<SpotNameInput> createState() => _SpotNameInputState();
}

class _SpotNameInputState extends State<SpotNameInput> {
  bool _isRefreshingLocationName = false;

  @override
  void initState() {
    super.initState();

    // 如果启用自动获取地名且输入框为空，则自动获取地名
    if (widget.autoFetchLocationName &&
        widget.controller.text.trim().isEmpty) {
      // 延迟执行，确保组件完全初始化
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _refreshLocationName();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text(
        //   '钓点名称',
        //   style: AppTheme.headlineSmall,
        // ),
        const SizedBox(height: AppTheme.spacingS),
        Row(
          children: [
            Expanded(
              child: EmojiTextField(
                controller: widget.controller,
                labelText: widget.labelText,
                enabled: widget.enabled,
                validator: widget.validator,
                // 禁用自动验证，只在失去焦点或表单提交时验证
                autovalidateMode: AutovalidateMode.disabled,
              ),
            ),
            const SizedBox(width: AppTheme.spacingM),
            // 反查地名按钮
            Container(
              decoration: BoxDecoration(
                color:
                    _isRefreshingLocationName
                        ? AppTheme.warningColor.withValues(alpha: 0.1)
                        : AppTheme.successColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusS),
                border: Border.all(
                  color:
                      _isRefreshingLocationName
                          ? AppTheme.warningColor
                          : AppTheme.successColor,
                ),
              ),
              child: IconButton(
                onPressed:
                    _isRefreshingLocationName || !widget.enabled
                        ? null
                        : _refreshLocationName,
                icon:
                    _isRefreshingLocationName
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppTheme.warningColor,
                            ),
                          ),
                        )
                        : const Icon(
                          Icons.refresh_rounded,
                          color: AppTheme.successColor,
                          size: 30,
                        ),
                padding: const EdgeInsets.all(8),
                constraints: const BoxConstraints(minWidth: 53, minHeight: 53),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 反查地名功能
  Future<void> _refreshLocationName() async {
    setState(() {
      _isRefreshingLocationName = true;
    });

    try {
      debugPrint(
        '开始反查地名: ${widget.location.latitude}, ${widget.location.longitude}',
      );

      final locationName = await TianDiTuUtils.getBestLocationName(
        widget.location.longitude,
        widget.location.latitude,
      );

      if (locationName != null && locationName.trim().isNotEmpty) {
        setState(() {
          widget.controller.text = '${locationName.trim()}${widget.nameSuffix}';
        });
        debugPrint('反查地名成功: ${widget.controller.text}');

        // 地名获取成功，不显示提示
      } else {
        debugPrint('反查地名失败：未获取到有效地名');
        if (mounted) {
          SnackBarService.showWarning(context, '未获取到地名信息');
        }
      }
    } catch (e) {
      debugPrint('反查地名异常: $e');
      if (mounted) {
        SnackBarService.showError(context, '获取地名失败: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshingLocationName = false;
        });
      }
    }
  }
}
