import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import '../../services/service_locator.dart';
import '../../services/media_processing/unified_media_service.dart';
import '../../config/pocketbase_config.dart';
import 'image_upload_widget.dart';

/// 媒体上传管理器
///
/// 功能：
/// - 管理图片和视频上传流程
/// - 处理上传进度
/// - 保存媒体记录到数据库
/// - 验证实拍状态
/// - 使用统一媒体服务处理所有类型
class ImageUploadManager {
  final UnifiedMediaService _mediaService = UnifiedMediaService.instance;

  /// 上传选中的图片（即时上传）
  Future<void> uploadSelectedImages(
    List<ImageUploadItem> items,
    Function(ImageUploadItem) onItemUpdated,
  ) async {
    // 检查用户是否已登录
    if (!Services.auth.isLoggedIn) {
      // 如果未登录，将图片标记为等待上传状态
      for (var item in items) {
        item.errorMessage = '请先登录';
        onItemUpdated(item);
      }
      return;
    }

    final user = Services.auth.currentUser;
    if (user == null) return;

    // 立即开始上传每张图片
    for (var item in items) {
      _uploadSingleImage(item, user.id, onItemUpdated);
    }
  }

  /// 上传单张媒体文件（图片或视频）- 使用统一媒体服务
  Future<void> _uploadSingleImage(
    ImageUploadItem item,
    String userId,
    Function(ImageUploadItem) onItemUpdated,
  ) async {
    try {
      debugPrint('🔄 [上传管理] 开始上传媒体文件: ${item.file.path}');
      debugPrint('🔄 [上传管理] 媒体类型: ${item.mediaType}');

      // 标记为正在上传
      item.isUploading = true;
      item.uploadProgress = 0.0;
      item.errorMessage = null;
      item.isCancelled = false;
      onItemUpdated(item);

      // 创建取消控制器
      final canceller = Completer<void>();
      item.setUploadCanceller(canceller);

      // 使用统一媒体服务处理
      final result = await Future.any([
        _mediaService.processMedia(
          file: item.file,
          userId: userId,
          progressCallback: (progress, message) {
            // 更新进度
            item.uploadProgress = progress;
            onItemUpdated(item);
            debugPrint('📊 [上传管理] 进度: ${(progress * 100).toInt()}% - $message');
          },
        ),
        canceller.future.then((_) => null),
      ]);

      // 检查是否被取消
      if (item.isCancelled) {
        debugPrint('⚠️ [上传管理] 上传已取消');
        return;
      }

      if (result != null) {
        // 上传成功
        item.isCompleted = true;
        item.isUploading = false;
        item.uploadProgress = 1.0;
        item.uploadedUrl = result.originalUrl;
        item.thumbnailUrl = result.thumbnailUrl;
        onItemUpdated(item);

        debugPrint('✅ [统一上传] 媒体上传成功: ${result.originalUrl}');
        debugPrint('✅ [统一上传] 缩略图URL: ${result.thumbnailUrl}');
        debugPrint('✅ [统一上传] 文件大小: ${result.formattedFileSize}');
        debugPrint('✅ [统一上传] 处理耗时: ${result.processingTime.inSeconds}s');
      } else {
        // 上传失败
        item.isUploading = false;
        item.uploadProgress = 0.0;
        item.errorMessage = '上传失败';
        onItemUpdated(item);

        debugPrint('❌ [统一上传] 媒体上传失败');
      }
    } catch (e) {
      // 检查是否是取消导致的异常
      if (item.isCancelled) {
        debugPrint('⚠️ [即时上传] 图片上传已取消');
        return;
      }

      // 上传异常
      item.isUploading = false;
      item.uploadProgress = 0.0;
      item.errorMessage = '上传异常: $e';
      onItemUpdated(item);

      debugPrint('❌ [即时上传] 图片上传异常: $e');
    }
  }

  /// 更新实拍状态
  bool updateCameraShotStatus(List<ImageUploadItem> selectedImages) {
    // 如果没有照片，则不能认证为实拍
    if (selectedImages.isEmpty) {
      debugPrint('🔍 [实拍状态] 无照片，实拍状态: false');
      return false;
    }

    // 检查是否所有照片都来自相机拍摄
    final isCameraShot = selectedImages.every((item) => item.isFromCamera);

    debugPrint('🔍 [实拍状态] 实拍状态: $isCameraShot');
    return isCameraShot;
  }

  /// 取消指定图片的上传
  void cancelImageUpload(List<ImageUploadItem> selectedImages, int index) {
    if (index >= 0 && index < selectedImages.length) {
      final item = selectedImages[index];
      if (item.isUploading) {
        item.cancelUpload();
        debugPrint('⚠️ [上传管理] 已取消图片上传: ${item.file.path}');
      }
    }
  }

  /// 重试指定图片的上传
  Future<void> retryImageUpload(
    List<ImageUploadItem> selectedImages,
    int index,
    Function(ImageUploadItem) onItemUpdated,
  ) async {
    if (index >= 0 && index < selectedImages.length) {
      final item = selectedImages[index];

      // 检查用户是否已登录
      if (!Services.auth.isLoggedIn) {
        item.errorMessage = '请先登录';
        onItemUpdated(item);
        return;
      }

      final user = Services.auth.currentUser;
      if (user == null) return;

      // 重置状态并重新上传
      item.resetForRetry();
      onItemUpdated(item);

      debugPrint('🔄 [上传管理] 重试图片上传: ${item.file.path}');
      await _uploadSingleImage(item, user.id, onItemUpdated);
    }
  }

  /// 检查是否有图片正在上传
  bool hasUploadingImages(List<ImageUploadItem> selectedImages) {
    return selectedImages.any((item) => item.isUploading);
  }

  /// 获取上传状态文本
  String getUploadStatusText(List<ImageUploadItem> selectedImages) {
    final uploadingCount =
        selectedImages.where((item) => item.isUploading).length;
    if (uploadingCount > 0) {
      return '照片上传中 ($uploadingCount/${selectedImages.length})';
    }
    return '发布钓点';
  }

  /// 关联已上传的图片到钓点
  Future<void> associateUploadedImages(
    String spotId,
    String userId,
    List<ImageUploadItem> uploadedImages,
  ) async {
    debugPrint('🔍 [图片关联] 开始关联已上传的图片');
    debugPrint('🔍 [图片关联] 钓点ID: $spotId');
    debugPrint('🔍 [图片关联] 用户ID: $userId');
    debugPrint('🔍 [图片关联] 图片数量: ${uploadedImages.length}');

    try {
      // 为每张已上传的图片创建数据库记录
      List<String> failedReasons = [];
      int successCount = 0;

      for (int i = 0; i < uploadedImages.length; i++) {
        final imageItem = uploadedImages[i];

        try {
          // 实现图片记录保存功能
          await _savePhotoRecord(
            spotId: spotId,
            userId: userId,
            imageItem: imageItem,
            sortOrder: i,
          );

          successCount++;
          debugPrint('✅ [图片关联] 照片记录 ${i + 1} 保存成功');
        } catch (e) {
          final reason = '照片${i + 1}: $e';
          failedReasons.add(reason);
          debugPrint('❌ [图片关联] 照片记录 ${i + 1} 保存失败: $e');
        }
      }

      if (failedReasons.isNotEmpty) {
        final errorMessage =
            '${failedReasons.length}张照片保存失败:\n${failedReasons.join('\n')}';
        debugPrint('⚠️ [图片关联] $errorMessage');
        throw Exception(errorMessage);
      } else {
        debugPrint('✅ [图片关联] 所有 $successCount 张照片记录保存成功');
      }
    } catch (e) {
      debugPrint('❌ [图片关联] 关联失败: $e');
      // 重新抛出异常，保持错误信息
      rethrow;
    }
  }

  /// 根据文件扩展名和媒体类型生成正确的MIME类型
  String _generateMimeType(ImageUploadItem item) {
    final extension = path
        .extension(item.file.path)
        .toLowerCase()
        .replaceAll('.', '');

    if (item.mediaType == MediaType.video) {
      switch (extension) {
        case 'mp4':
          return 'video/mp4';
        case 'mov':
          return 'video/quicktime';
        case 'avi':
          return 'video/x-msvideo';
        case 'mkv':
          return 'video/x-matroska';
        case 'webm':
          return 'video/webm';
        case '3gp':
          return 'video/3gpp';
        case 'flv':
          return 'video/x-flv';
        default:
          return 'video/mp4'; // 默认视频类型
      }
    } else {
      switch (extension) {
        case 'jpg':
        case 'jpeg':
          return 'image/jpeg';
        case 'png':
          return 'image/png';
        case 'gif':
          return 'image/gif';
        case 'bmp':
          return 'image/bmp';
        case 'webp':
          return 'image/webp';
        case 'svg':
          return 'image/svg+xml';
        default:
          return 'image/jpeg'; // 默认图片类型
      }
    }
  }

  /// 保存照片记录到数据库
  Future<bool> _savePhotoRecord({
    required String spotId,
    required String userId,
    required ImageUploadItem imageItem,
    required int sortOrder,
  }) async {
    try {
      debugPrint('🔍 [照片记录] 开始保存照片记录到数据库');
      debugPrint('🔍 [照片记录] 钓点ID: $spotId');
      debugPrint('🔍 [照片记录] 用户ID: $userId');
      debugPrint('🔍 [照片记录] 原图URL: ${imageItem.uploadedUrl}');
      debugPrint('🔍 [照片记录] 缩略图URL: ${imageItem.thumbnailUrl}');
      debugPrint('🔍 [照片记录] 是否来自相机: ${imageItem.isFromCamera}');
      debugPrint('🔍 [照片记录] 媒体类型: ${imageItem.mediaType}');

      // 获取PocketBase客户端
      final pb = PocketBaseConfig.instance.client;

      // 确定正确的photo_source值（数据库枚举值：gallery, camera）
      String photoSource;
      if (imageItem.isFromCamera) {
        photoSource = 'camera'; // 相机拍摄
      } else {
        photoSource = 'gallery'; // 相册选择
      }

      debugPrint('🔍 [照片记录] 设置photo_source为: $photoSource');

      // 生成正确的MIME类型
      final mimeType = _generateMimeType(imageItem);
      debugPrint(
        '🔍 [照片记录] 生成MIME类型: $mimeType (媒体类型: ${imageItem.mediaType.name})',
      );

      final requestBody = {
        'spot_id': spotId,
        'user_id': userId,
        'filename': path.basename(imageItem.file.path),
        'url': imageItem.uploadedUrl,
        'thumbnail_url': imageItem.thumbnailUrl ?? imageItem.uploadedUrl,
        'storage_path': imageItem.uploadedUrl, // 使用URL作为存储路径
        'thumbnail_path': imageItem.thumbnailUrl ?? imageItem.uploadedUrl,
        'type': 'normal',
        'description': null,
        'sort_order': sortOrder,
        'file_size': await imageItem.file.length(),
        'mime_type': mimeType, // 使用动态生成的MIME类型
        'is_camera_shot': imageItem.isFromCamera, // 添加照片来源信息
        'photo_source': photoSource, // 使用修正后的值
      };

      debugPrint('🔍 [照片记录] 完整请求体: $requestBody');

      // 创建照片记录
      final record = await pb
          .collection('spot_photos')
          .create(body: requestBody);

      debugPrint('🔍 [照片记录] 照片来源: ${imageItem.isFromCamera ? "相机" : "相册"}');
      debugPrint('✅ [照片记录] 保存成功，记录ID: ${record.id}');
      return true;
    } catch (e) {
      final errorMessage = '照片记录保存失败: $e';
      debugPrint('❌ [照片记录] $errorMessage');

      // 抛出详细的错误信息，让上层调用者能够获取具体错误
      throw Exception(errorMessage);
    }
  }
}
