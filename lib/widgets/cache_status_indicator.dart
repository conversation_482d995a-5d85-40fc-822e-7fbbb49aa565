import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

/// 缓存状态指示器
///
/// 显示数据来源和缓存命中状态的视觉提示
class CacheStatusIndicator extends StatefulWidget {
  /// 缓存命中状态
  final CacheHitStatus status;

  /// 数据来源描述
  final String? sourceDescription;

  /// 缓存时间
  final DateTime? cacheTime;

  /// 是否显示详细信息
  final bool showDetails;

  /// 位置
  final CacheIndicatorPosition position;

  /// 自动隐藏时间（秒）
  final int autoHideSeconds;

  const CacheStatusIndicator({
    super.key,
    required this.status,
    this.sourceDescription,
    this.cacheTime,
    this.showDetails = false,
    this.position = CacheIndicatorPosition.topRight,
    this.autoHideSeconds = 3,
  });

  @override
  State<CacheStatusIndicator> createState() => _CacheStatusIndicatorState();
}

class _CacheStatusIndicatorState extends State<CacheStatusIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  bool _isVisible = true;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: _getInitialOffset(),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    // 启动进入动画
    _animationController.forward();

    // 自动隐藏
    if (widget.autoHideSeconds > 0) {
      Future.delayed(Duration(seconds: widget.autoHideSeconds), () {
        if (mounted) {
          _hide();
        }
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isVisible) return const SizedBox.shrink();

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: _buildIndicator(),
      ),
    );
  }

  /// 构建指示器
  Widget _buildIndicator() {
    final statusInfo = _getStatusInfo();

    return Container(
      margin: const EdgeInsets.all(8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: statusInfo.backgroundColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          FaIcon(statusInfo.icon, color: statusInfo.iconColor, size: 14),

          const SizedBox(width: 6),

          Text(
            statusInfo.text,
            style: TextStyle(
              color: statusInfo.textColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),

          if (widget.showDetails && widget.cacheTime != null) ...[
            const SizedBox(width: 8),
            Text(
              _formatCacheTime(widget.cacheTime!),
              style: TextStyle(
                color: statusInfo.textColor.withOpacity(0.7),
                fontSize: 10,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 获取状态信息
  CacheStatusInfo _getStatusInfo() {
    switch (widget.status) {
      case CacheHitStatus.hit:
        return CacheStatusInfo(
          icon: FontAwesomeIcons.bolt,
          text: widget.sourceDescription ?? '缓存命中',
          backgroundColor: Colors.green.shade100,
          iconColor: Colors.green.shade700,
          textColor: Colors.green.shade800,
        );
      case CacheHitStatus.partial:
        return CacheStatusInfo(
          icon: FontAwesomeIcons.clockRotateLeft,
          text: widget.sourceDescription ?? '部分缓存',
          backgroundColor: Colors.orange.shade100,
          iconColor: Colors.orange.shade700,
          textColor: Colors.orange.shade800,
        );
      case CacheHitStatus.miss:
        return CacheStatusInfo(
          icon: FontAwesomeIcons.download,
          text: widget.sourceDescription ?? '网络加载',
          backgroundColor: Colors.blue.shade100,
          iconColor: Colors.blue.shade700,
          textColor: Colors.blue.shade800,
        );
      case CacheHitStatus.offline:
        return CacheStatusInfo(
          icon: FontAwesomeIcons.cloudArrowDown,
          text: widget.sourceDescription ?? '离线缓存',
          backgroundColor: Colors.grey.shade200,
          iconColor: Colors.grey.shade700,
          textColor: Colors.grey.shade800,
        );
    }
  }

  /// 获取初始偏移
  Offset _getInitialOffset() {
    switch (widget.position) {
      case CacheIndicatorPosition.topLeft:
        return const Offset(-1, -1);
      case CacheIndicatorPosition.topRight:
        return const Offset(1, -1);
      case CacheIndicatorPosition.bottomLeft:
        return const Offset(-1, 1);
      case CacheIndicatorPosition.bottomRight:
        return const Offset(1, 1);
    }
  }

  /// 格式化缓存时间
  String _formatCacheTime(DateTime cacheTime) {
    final now = DateTime.now();
    final difference = now.difference(cacheTime);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else {
      return '${difference.inDays}天前';
    }
  }

  /// 隐藏指示器
  void _hide() {
    if (mounted) {
      setState(() {
        _isVisible = false;
      });
    }
  }
}

/// 操作引导提示组件
class OperationGuideTooltip extends StatefulWidget {
  /// 引导文本
  final String guideText;

  /// 目标组件
  final Widget child;

  /// 是否显示引导
  final bool showGuide;

  /// 引导位置
  final GuidePosition position;

  /// 自动隐藏时间（秒）
  final int autoHideSeconds;

  /// 隐藏回调
  final VoidCallback? onHide;

  const OperationGuideTooltip({
    super.key,
    required this.guideText,
    required this.child,
    this.showGuide = false,
    this.position = GuidePosition.top,
    this.autoHideSeconds = 5,
    this.onHide,
  });

  @override
  State<OperationGuideTooltip> createState() => _OperationGuideTooltipState();
}

class _OperationGuideTooltipState extends State<OperationGuideTooltip>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  bool _isVisible = false;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _isVisible = widget.showGuide;
    if (_isVisible) {
      _animationController.forward();
      _scheduleAutoHide();
    }
  }

  @override
  void didUpdateWidget(OperationGuideTooltip oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.showGuide != oldWidget.showGuide) {
      if (widget.showGuide) {
        setState(() {
          _isVisible = true;
        });
        _animationController.forward();
        _scheduleAutoHide();
      } else {
        _hide();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.hardEdge, // 修复：改为hardEdge避免溢出
      children: [
        widget.child,

        if (_isVisible)
          Positioned(
            top:
                widget.position == GuidePosition.bottom
                    ? null
                    : -50, // 修复：减少负值偏移
            bottom:
                widget.position == GuidePosition.top ? null : -50, // 修复：减少负值偏移
            left: 8, // 修复：添加左边距避免贴边
            right: 8, // 修复：添加右边距避免贴边
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: _buildTooltip(),
              ),
            ),
          ),
      ],
    );
  }

  /// 构建提示框
  Widget _buildTooltip() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8), // 修复：减少边距
      padding: const EdgeInsets.all(8), // 修复：减少内边距
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.inverseSurface,
        borderRadius: BorderRadius.circular(6), // 修复：减少圆角
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15), // 修复：减少阴影透明度
            blurRadius: 4, // 修复：减少模糊半径
            offset: const Offset(0, 1), // 修复：减少阴影偏移
          ),
        ],
      ),
      child: Row(
        children: [
          FaIcon(
            FontAwesomeIcons.lightbulb,
            color: Theme.of(context).colorScheme.onInverseSurface,
            size: 16,
          ),

          const SizedBox(width: 8),

          Expanded(
            child: Text(
              widget.guideText,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onInverseSurface,
              ),
            ),
          ),

          IconButton(
            onPressed: _hide,
            icon: FaIcon(
              FontAwesomeIcons.xmark,
              color: Theme.of(context).colorScheme.onInverseSurface,
              size: 14,
            ),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
            padding: EdgeInsets.zero,
          ),
        ],
      ),
    );
  }

  /// 安排自动隐藏
  void _scheduleAutoHide() {
    if (widget.autoHideSeconds > 0) {
      Future.delayed(Duration(seconds: widget.autoHideSeconds), () {
        if (mounted && _isVisible) {
          _hide();
        }
      });
    }
  }

  /// 隐藏提示
  void _hide() {
    if (mounted) {
      _animationController.reverse().then((_) {
        if (mounted) {
          setState(() {
            _isVisible = false;
          });
          widget.onHide?.call();
        }
      });
    }
  }
}

/// 缓存命中状态枚举
enum CacheHitStatus {
  hit, // 缓存命中
  partial, // 部分缓存
  miss, // 缓存未命中
  offline, // 离线缓存
}

/// 缓存指示器位置枚举
enum CacheIndicatorPosition { topLeft, topRight, bottomLeft, bottomRight }

/// 引导位置枚举
enum GuidePosition { top, bottom }

/// 缓存状态信息数据类
class CacheStatusInfo {
  final IconData icon;
  final String text;
  final Color backgroundColor;
  final Color iconColor;
  final Color textColor;

  CacheStatusInfo({
    required this.icon,
    required this.text,
    required this.backgroundColor,
    required this.iconColor,
    required this.textColor,
  });
}
