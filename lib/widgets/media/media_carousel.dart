import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:chewie/chewie.dart';
import 'package:video_player/video_player.dart';
import '../../models/spot_media.dart';
import '../../services/media_cache_service.dart';
import '../../services/unified_image_service.dart';
import '../common/media_viewer.dart';
import '../add_spot_form/image_upload_widget.dart' as upload_widget;

/// 混合媒体轮播组件
///
/// 支持图片和视频的混合轮播显示
/// 实现边下载边播放和智能预加载
class MediaCarousel extends StatefulWidget {
  /// 媒体项目列表
  final List<SpotMediaItem> mediaItems;

  /// 初始显示索引
  final int initialIndex;

  /// 页面变化回调
  final Function(int)? onPageChanged;

  /// 是否自动播放视频
  final bool autoPlayVideo;

  /// 是否显示控制器
  final bool showControls;

  /// 轮播高度
  final double? height;

  /// 是否启用点击查看大图/视频
  final bool enableViewer;

  const MediaCarousel({
    super.key,
    required this.mediaItems,
    this.initialIndex = 0,
    this.onPageChanged,
    this.autoPlayVideo = true,
    this.showControls = true,
    this.height,
    this.enableViewer = true,
  });

  @override
  State<MediaCarousel> createState() => _MediaCarouselState();
}

class _MediaCarouselState extends State<MediaCarousel>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late int _currentIndex;

  // 视频播放器相关
  VideoPlayerController? _videoController;
  ChewieController? _chewieController;

  // 缓存服务
  final MediaCacheService _cacheService = MediaCacheService.instance;
  final UnifiedImageService _imageService = UnifiedImageService();

  // 自动播放定时器
  Timer? _autoPlayTimer;
  bool _userInteracted = false;
  bool _videoCompleted = false;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);

    // 开始智能预加载
    _startSmartPreload();

    // 初始化当前媒体
    _initializeCurrentMedia();

    // 启动智能自动播放（图片和视频）
    if (widget.mediaItems.length > 1) {
      _startAutoPlay();
    }
  }

  @override
  void dispose() {
    _autoPlayTimer?.cancel();
    _pageController.dispose();
    _disposeVideoPlayer();
    super.dispose();
  }

  /// 开始智能预加载
  Future<void> _startSmartPreload() async {
    if (widget.mediaItems.isNotEmpty) {
      await _cacheService.smartPreload(widget.mediaItems, _currentIndex);
    }
  }

  /// 初始化当前媒体
  void _initializeCurrentMedia() {
    if (_currentIndex < widget.mediaItems.length) {
      final currentItem = widget.mediaItems[_currentIndex];
      if (currentItem.isVideo && widget.autoPlayVideo) {
        _initializeVideoPlayer(currentItem);
      }
    }
  }

  /// 初始化视频播放器
  void _initializeVideoPlayer(SpotMediaItem mediaItem) async {
    _disposeVideoPlayer();

    try {
      // 获取缓存文件或开始下载
      final cachedFile = await _cacheService.getCachedFile(mediaItem);
      if (cachedFile != null && mounted) {
        _videoController = VideoPlayerController.file(cachedFile);
        await _videoController!.initialize();

        if (mounted) {
          _chewieController = ChewieController(
            videoPlayerController: _videoController!,
            aspectRatio: null, // 让视频适应容器而不是保持原始比例
            autoPlay: widget.autoPlayVideo,
            looping: false,
            showControls: widget.showControls,
            allowFullScreen: false,
            allowMuting: true,
            allowPlaybackSpeedChanging: false,
            hideControlsTimer: const Duration(
              milliseconds: 1000,
            ), //[*参数调整*]播放控制器自动隐藏时间调整为0.5秒
            showControlsOnInitialize: false,
            materialProgressColors: ChewieProgressColors(
              playedColor: Theme.of(context).primaryColor,
              handleColor: Theme.of(context).primaryColor,
              backgroundColor: Colors.grey,
              bufferedColor: Colors.grey.shade300,
            ),
          );

          // 添加视频播放完成监听
          _addVideoCompletionListener();

          setState(() {});
        }
      }
    } catch (e) {
      debugPrint('❌ [视频播放器] 初始化失败: $e');
      if (mounted) {
        mediaItem.setLoadingState(error: true, errorMsg: e.toString());
        setState(() {});
      }
    }
  }

  /// 释放视频播放器
  void _disposeVideoPlayer() {
    _removeVideoCompletionListener();
    _chewieController?.dispose();
    _videoController?.dispose();
    _chewieController = null;
    _videoController = null;
  }

  /// 添加视频播放完成监听
  void _addVideoCompletionListener() {
    _videoCompleted = false;
    _videoController?.addListener(_onVideoPositionChanged);
    debugPrint('🎬 [视频监听] 添加播放完成监听器');
  }

  /// 移除视频播放完成监听
  void _removeVideoCompletionListener() {
    _videoController?.removeListener(_onVideoPositionChanged);
    debugPrint('🎬 [视频监听] 移除播放完成监听器');
  }

  /// 视频位置变化监听
  void _onVideoPositionChanged() {
    try {
      if (_videoController == null || !_videoController!.value.isInitialized) {
        return;
      }

      // 检查视频是否有错误
      if (_videoController!.value.hasError) {
        debugPrint('❌ [视频播放] 播放出错，使用默认自动播放');
        _fallbackToDefaultAutoPlay();
        return;
      }

      final position = _videoController!.value.position;
      final duration = _videoController!.value.duration;

      // 检测播放完成（考虑精度问题，提前100ms判断）
      if (position >= duration - const Duration(milliseconds: 100)) {
        if (!_videoCompleted) {
          _videoCompleted = true;
          debugPrint('🎬 [视频播放] 播放完成，自动切换到下一个');
          _moveToNext();
        }
      }
    } catch (e) {
      debugPrint('❌ [视频播放] 监听器异常: $e');
      _fallbackToDefaultAutoPlay();
    }
  }

  /// 降级到默认自动播放
  void _fallbackToDefaultAutoPlay() {
    if (!mounted) return;

    _removeVideoCompletionListener();
    _autoPlayTimer?.cancel();
    _autoPlayTimer = Timer(const Duration(seconds: 10), () {
      if (mounted) {
        _moveToNext();
      }
    });
    debugPrint('🔄 [自动播放] 降级到默认10秒自动播放');
  }

  /// 移动到下一个媒体项目
  void _moveToNext() {
    if (!mounted || !_pageController.hasClients) return;

    final nextIndex = (_currentIndex + 1) % widget.mediaItems.length;
    _pageController.animateToPage(
      nextIndex,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
    debugPrint('📱 [轮播切换] 切换到索引: $nextIndex');
  }

  /// 页面变化处理
  void _onPageChanged(int index) {
    if (_currentIndex != index) {
      // 暂停之前的视频
      if (_currentIndex < widget.mediaItems.length &&
          widget.mediaItems[_currentIndex].isVideo) {
        _pauseCurrentVideo();
      }

      setState(() {
        _currentIndex = index;
      });

      // 智能预加载新的范围
      _cacheService.smartPreload(widget.mediaItems, index);

      // 初始化新的媒体
      _initializeCurrentMedia();

      // 重新启动智能自动播放
      if (widget.mediaItems.length > 1) {
        _startAutoPlay();
      }

      // 回调
      widget.onPageChanged?.call(index);
    }
  }

  /// 暂停当前视频
  void _pauseCurrentVideo() {
    if (_videoController != null && _videoController!.value.isPlaying) {
      _videoController!.pause();
    }
  }

  /// 开始智能自动播放（图片和视频）
  void _startAutoPlay() {
    _stopAutoPlay(); // 先停止现有的自动播放

    if (_userInteracted || widget.mediaItems.length <= 1) {
      return; // 用户已交互或只有一个媒体项目时不自动播放
    }

    final currentItem = widget.mediaItems[_currentIndex];
    if (currentItem.isVideo) {
      // 视频：监听播放完成事件
      _startVideoAutoPlay();
    } else {
      // 图片：使用固定时间间隔
      _startImageAutoPlay();
    }
  }

  /// 开始视频自动播放（监听播放完成）
  void _startVideoAutoPlay() {
    debugPrint('🎬 [自动播放] 启动视频自动播放（监听播放完成）');
    // 视频的自动播放通过播放完成监听器处理，这里不需要额外操作
    // 监听器已在_initializeVideoPlayer中添加
  }

  /// 开始图片自动播放（固定时间间隔）
  void _startImageAutoPlay() {
    debugPrint('🖼️ [自动播放] 启动图片自动播放（10秒间隔）');
    _autoPlayTimer = Timer(const Duration(seconds: 10), () {
      if (!_userInteracted && mounted) {
        _moveToNext();
      }
    });
  }

  /// 停止自动播放
  void _stopAutoPlay() {
    _autoPlayTimer?.cancel();
    _autoPlayTimer = null;
    // 注意：视频播放完成监听器在_disposeVideoPlayer中清理
    debugPrint('⏹️ [自动播放] 停止自动播放');
  }

  /// 用户交互处理
  void _onUserInteraction() {
    _userInteracted = true;
    _stopAutoPlay();

    // 10秒后恢复自动播放
    Timer(const Duration(seconds: 10), () {
      if (mounted) {
        _userInteracted = false;
        _startAutoPlay();
      }
    });
  }

  /// 打开媒体查看器
  void _openMediaViewer(int initialIndex) {
    if (!widget.enableViewer) return;

    // 暂停当前视频
    _pauseCurrentVideo();

    // 转换数据格式并打开MediaViewer
    _convertAndOpenMediaViewer(initialIndex);
  }

  /// 转换数据格式并打开MediaViewer
  Future<void> _convertAndOpenMediaViewer(int initialIndex) async {
    try {
      // 转换为MediaViewer支持的格式
      final viewableItemsWithIndex =
          <MapEntry<int, upload_widget.ImageUploadItem>>[];

      for (int i = 0; i < widget.mediaItems.length; i++) {
        final mediaItem = widget.mediaItems[i];
        final imageUploadItem = await _convertToImageUploadItem(mediaItem);

        if (imageUploadItem != null) {
          viewableItemsWithIndex.add(MapEntry(i, imageUploadItem));
        }
      }

      if (viewableItemsWithIndex.isEmpty) {
        debugPrint('❌ [媒体查看器] 没有可预览的媒体');
        return;
      }

      // 找到点击的媒体在可预览列表中的索引
      int viewableIndex = 0;
      for (int i = 0; i < viewableItemsWithIndex.length; i++) {
        if (viewableItemsWithIndex[i].key == initialIndex) {
          viewableIndex = i;
          break;
        }
      }

      final viewableItems =
          viewableItemsWithIndex.map((entry) => entry.value).toList();
      final originalIndices =
          viewableItemsWithIndex.map((entry) => entry.key).toList();

      debugPrint('✅ [媒体查看器] 打开媒体查看器，索引: $initialIndex -> $viewableIndex');

      // 打开MediaViewer
      if (mounted) {
        Navigator.of(context)
            .push(
              PageRouteBuilder(
                pageBuilder:
                    (context, animation, secondaryAnimation) => MediaViewer(
                      mediaItems: viewableItems,
                      initialIndex: viewableIndex,
                      heroTagPrefix: 'media_carousel_hero',
                      originalIndices: originalIndices,
                    ),
                transitionsBuilder: (
                  context,
                  animation,
                  secondaryAnimation,
                  child,
                ) {
                  return FadeTransition(opacity: animation, child: child);
                },
                transitionDuration: const Duration(milliseconds: 300),
                reverseTransitionDuration: const Duration(milliseconds: 300),
              ),
            )
            .then((_) {
              // MediaViewer关闭后，恢复轮播状态
              if (mounted && widget.mediaItems.length > 1) {
                _startAutoPlay();
              }
            });
      }
    } catch (e) {
      debugPrint('❌ [媒体查看器] 打开失败: $e');
    }
  }

  /// 将SpotMediaItem转换为ImageUploadItem
  Future<upload_widget.ImageUploadItem?> _convertToImageUploadItem(
    SpotMediaItem mediaItem,
  ) async {
    try {
      // 获取缓存文件
      File? cachedFile = mediaItem.cachedFile;
      if (cachedFile == null) {
        cachedFile = await _cacheService.getCachedFile(mediaItem);
      }

      if (cachedFile == null || !cachedFile.existsSync()) {
        debugPrint('⚠️ [数据转换] 媒体文件不存在: ${mediaItem.filename}');
        return null;
      }

      // 获取缩略图文件（如果是视频）
      File? thumbnailFile;
      if (mediaItem.isVideo && mediaItem.thumbnailUrl != null) {
        thumbnailFile = mediaItem.cachedThumbnailFile;
        if (thumbnailFile == null) {
          thumbnailFile = await _cacheService.getCachedThumbnail(mediaItem);
        }
      }

      // 创建ImageUploadItem
      return upload_widget.ImageUploadItem(
        file: cachedFile,
        isFromCamera: false,
        mediaType:
            mediaItem.isVideo
                ? upload_widget.MediaType.video
                : upload_widget.MediaType.image,
        videoDuration: mediaItem.duration,
        fileSize: mediaItem.fileSize,
        thumbnailFile: thumbnailFile,
        uploadProgress: 1.0, // 已完成
        isUploading: false,
        isCompleted: true,
        isCancelled: false,
        uploadedUrl: mediaItem.url,
        thumbnailUrl: mediaItem.thumbnailUrl,
        errorMessage: null,
      );
    } catch (e) {
      debugPrint('❌ [数据转换] 转换失败: ${mediaItem.filename}, 错误: $e');
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.mediaItems.isEmpty) {
      return Container(
        height: widget.height ?? 300,
        color: Colors.grey.shade200,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.photo_library_outlined, size: 48, color: Colors.grey),
              SizedBox(height: 8),
              Text(
                '暂无媒体内容',
                style: TextStyle(color: Colors.grey, fontSize: 16),
              ),
            ],
          ),
        ),
      );
    }

    return SizedBox(
      height: widget.height ?? 300,
      child: Stack(
        children: [
          // 主轮播
          PageView.builder(
            controller: _pageController,
            onPageChanged: _onPageChanged,
            itemCount: widget.mediaItems.length,
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () => _openMediaViewer(index),
                onPanStart: (_) => _onUserInteraction(),
                child: _buildMediaItem(widget.mediaItems[index], index),
              );
            },
          ),

          // 媒体指示器
          if (widget.mediaItems.length > 1)
            Positioned(
              bottom: 16,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(widget.mediaItems.length, (index) {
                  final item = widget.mediaItems[index];
                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color:
                          index == _currentIndex
                              ? Colors.white
                              : Colors.white.withValues(alpha: 0.5),
                    ),
                    child:
                        item.isVideo
                            ? const Icon(
                              Icons.play_circle_filled,
                              size: 8,
                              color: Colors.transparent,
                            )
                            : null,
                  );
                }),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建媒体项目
  Widget _buildMediaItem(SpotMediaItem mediaItem, int index) {
    debugPrint(
      '🎯 [媒体轮播] 构建媒体项目: ${mediaItem.filename}, 类型: ${mediaItem.type.name}, isVideo: ${mediaItem.isVideo}',
    );

    if (mediaItem.isVideo) {
      return _buildVideoItem(mediaItem, index);
    } else {
      return _buildImageItem(mediaItem, index);
    }
  }

  /// 构建图片项目
  Widget _buildImageItem(SpotMediaItem mediaItem, int index) {
    debugPrint(
      '🖼️ [图片显示] 构建图片项目: ${mediaItem.filename}, URL: ${mediaItem.url}',
    );

    return GestureDetector(
      onTap: () => _openMediaViewer(index),
      child: Hero(
        tag: 'media_carousel_hero_$index',
        child: _imageService.buildCachedSignedImage(
          originalUrl: mediaItem.url,
          fit: BoxFit.cover,
          placeholder: Container(
            color: Colors.grey.shade200,
            child: const Center(child: CircularProgressIndicator()),
          ),
          errorWidget: Container(
            color: Colors.grey.shade200,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.broken_image_outlined,
                    size: 48,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '图片加载失败',
                    style: TextStyle(color: Colors.grey.shade500, fontSize: 14),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建视频项目
  Widget _buildVideoItem(SpotMediaItem mediaItem, int index) {
    if (_currentIndex == index && _chewieController != null) {
      // 显示视频播放器，使用Stack + GestureDetector解决点击事件冲突
      return Container(
        width: double.infinity,
        height: widget.height,
        child: ClipRect(
          child: FittedBox(
            alignment: Alignment.center,
            fit: BoxFit.cover,
            child: SizedBox(
              width: _videoController!.value.size.width,
              height: _videoController!.value.size.height,
              child: Hero(
                tag: 'media_carousel_hero_$index',
                child: Stack(
                  children: [
                    // 视频播放器
                    Chewie(controller: _chewieController!),
                    // 透明的手势检测层，用于打开MediaViewer
                    Positioned.fill(
                      child: GestureDetector(
                        onTap: () => _openMediaViewer(index),
                        behavior: HitTestBehavior.translucent,
                        child: Container(color: Colors.transparent),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    } else {
      // 显示视频缩略图或加载状态
      return _buildVideoThumbnail(mediaItem);
    }
  }

  /// 构建视频缩略图
  Widget _buildVideoThumbnail(SpotMediaItem mediaItem) {
    if (mediaItem.isLoading) {
      return _buildLoadingWidget(mediaItem);
    }

    if (mediaItem.hasError) {
      return _buildErrorWidget(mediaItem);
    }

    // 获取当前索引用于Hero标签
    final currentIndex = widget.mediaItems.indexOf(mediaItem);

    // 显示缩略图
    return GestureDetector(
      onTap: () => _openMediaViewer(currentIndex),
      child: Hero(
        tag: 'media_carousel_hero_$currentIndex',
        child: Stack(
          alignment: Alignment.center,
          children: [
            if (mediaItem.thumbnailUrl != null)
              _imageService.buildCachedSignedImage(
                originalUrl: mediaItem.thumbnailUrl!,
                fit: BoxFit.cover,
                placeholder: Container(
                  color: Colors.grey.shade200,
                  child: const Center(child: CircularProgressIndicator()),
                ),
              )
            else
              Container(
                color: Colors.grey.shade800,
                child: const Icon(
                  Icons.video_file,
                  size: 64,
                  color: Colors.white,
                ),
              ),

            // 播放按钮指示
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.6),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.play_arrow,
                color: Colors.white,
                size: 30,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingWidget(SpotMediaItem mediaItem) {
    return Container(
      color: Colors.grey.shade200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              value:
                  mediaItem.downloadProgress > 0
                      ? mediaItem.downloadProgress
                      : null,
            ),
            const SizedBox(height: 8),
            Text(
              mediaItem.downloadProgress > 0
                  ? '加载中 ${(mediaItem.downloadProgress * 100).toStringAsFixed(0)}%'
                  : '加载中...',
              style: const TextStyle(color: Colors.grey, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorWidget(SpotMediaItem mediaItem) {
    return Container(
      color: Colors.grey.shade200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 8),
            Text(
              '加载失败',
              style: TextStyle(color: Colors.grey.shade500, fontSize: 14),
            ),
            const SizedBox(height: 4),
            TextButton(
              onPressed: () {
                mediaItem.resetLoadingState();
                _cacheService.preloadMedia(mediaItem, priority: true);
                setState(() {});
              },
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }
}
