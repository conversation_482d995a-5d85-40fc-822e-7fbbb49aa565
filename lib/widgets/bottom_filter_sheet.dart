import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../config/filter_config.dart';
import '../models/unified_marker.dart';

/// 底部过滤抽屉
///
/// 采用现代移动应用的底部抽屉设计，提供更好的用户体验
/// 支持拖拽调整高度、手势关闭等交互功能
class BottomFilterSheet extends StatefulWidget {
  /// 当前过滤配置
  final FilterConfig currentConfig;

  /// 配置变更回调
  final Function(FilterConfig) onConfigChanged;

  /// 关闭回调
  final VoidCallback onClose;

  /// 更多设置回调
  final VoidCallback onMoreSettings;

  /// 当前地图范围内的统计数据
  final FilterStatistics statistics;

  const BottomFilterSheet({
    super.key,
    required this.currentConfig,
    required this.onConfigChanged,
    required this.onClose,
    required this.onMoreSettings,
    required this.statistics,
  });

  @override
  State<BottomFilterSheet> createState() => _BottomFilterSheetState();
}

class _BottomFilterSheetState extends State<BottomFilterSheet>
    with TickerProviderStateMixin {
  late FilterConfig _config;
  late AnimationController _animationController;
  late Animation<double> _animation;

  // 操作反馈相关
  FilterStatistics? _previousStatistics;

  @override
  void initState() {
    super.initState();
    _config = widget.currentConfig;
    _previousStatistics = widget.statistics;

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    );

    // 启动进入动画
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(BottomFilterSheet oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.currentConfig != oldWidget.currentConfig) {
      setState(() {
        _config = widget.currentConfig;
      });
    }
  }

  void _updateConfig(FilterConfig newConfig) {
    // 保存之前的统计数据
    _previousStatistics = widget.statistics;

    setState(() {
      _config = newConfig;
    });

    // 触发配置变更
    widget.onConfigChanged(newConfig);
  }

  /// 关闭抽屉
  void _closeSheet() async {
    debugPrint('📊 [底部面板] 用户关闭过滤面板');
    debugPrint('📊 [底部面板] 当前配置: ${_config.getSummary()}');

    await _animationController.reverse();
    widget.onClose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Stack(
          children: [
            // 半透明背景遮罩
            GestureDetector(
              onTap: _closeSheet,
              child: Semantics(
                label: '关闭过滤面板',
                hint: '点击背景关闭过滤面板',
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.black.withValues(alpha: 0.4 * _animation.value),
                ),
              ),
            ),

            // 底部抽屉
            Positioned(
              left: 0,
              right: 0,
              bottom:
                  -MediaQuery.of(context).size.height * (1 - _animation.value),
              child: GestureDetector(
                onPanUpdate: (details) {
                  // 向下拖拽关闭
                  if (details.delta.dy > 0) {
                    final progress =
                        details.delta.dy / MediaQuery.of(context).size.height;
                    final newValue = (_animationController.value - progress * 2)
                        .clamp(0.0, 1.0);
                    _animationController.value = newValue;
                  }
                },
                onPanEnd: (details) {
                  // 根据拖拽速度和位置决定是否关闭
                  if (details.velocity.pixelsPerSecond.dy > 300 ||
                      _animationController.value < 0.5) {
                    _closeSheet();
                  } else {
                    _animationController.forward();
                  }
                },
                child: Semantics(
                  label: '过滤设置面板',
                  hint: '向下拖拽可关闭面板',
                  child: Container(
                    height: MediaQuery.of(context).size.height * 0.75,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.15),
                          blurRadius: 20,
                          offset: const Offset(0, -8),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        _buildDragHandle(),
                        _buildHeader(),
                        Expanded(
                          child: SingleChildScrollView(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: _buildContent(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 构建拖拽指示器
  Widget _buildDragHandle() {
    return Semantics(
      label: '拖拽指示器',
      hint: '向下拖拽可关闭过滤面板',
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Center(
          child: Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Row(
        children: [
          Icon(
            FontAwesomeIcons.filter,
            size: 20,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 12),
          Text(
            '过滤设置',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: _closeSheet,
            icon: const Icon(Icons.close),
            iconSize: 24,
            style: IconButton.styleFrom(
              backgroundColor: Colors.grey.shade100,
              shape: const CircleBorder(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 8),
        _buildStatisticsOverview(),
        const SizedBox(height: 20),
        _buildQuickActions(),
        const SizedBox(height: 24),
        _buildTypeFilters(),
        const SizedBox(height: 24),
        _buildQualityFilters(),
        const SizedBox(height: 24),
        _buildDisplaySettings(),
        const SizedBox(height: 24),
        _buildMoreSettingsButton(),
        const SizedBox(height: 32),
      ],
    );
  }

  /// 构建统计信息概览
  Widget _buildStatisticsOverview() {
    final stats = widget.statistics;
    final filterRatio =
        stats.totalCount > 0 ? stats.filteredCount / stats.totalCount : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withValues(alpha: 0.05),
            Theme.of(context).primaryColor.withValues(alpha: 0.02),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题行
          Row(
            children: [
              Icon(
                FontAwesomeIcons.chartBar,
                size: 16,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                '过滤统计',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const Spacer(),
              if (_previousStatistics != null) _buildChangeIndicator(),
            ],
          ),

          const SizedBox(height: 16),

          // 统计卡片行
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  '总数',
                  '${stats.totalCount}',
                  Icons.location_on,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  '显示',
                  '${stats.filteredCount}',
                  Icons.visibility,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  '推荐',
                  '${stats.priorityCount}',
                  Icons.star,
                  Colors.orange,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 过滤比例进度条
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '过滤比例',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  Text(
                    '${(filterRatio * 100).toStringAsFixed(1)}%',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              AnimatedContainer(
                duration: const Duration(milliseconds: 500),
                height: 8,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.grey.shade200,
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: filterRatio,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      gradient: LinearGradient(
                        colors: [
                          Theme.of(context).primaryColor,
                          Theme.of(context).primaryColor.withValues(alpha: 0.7),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建变化指示器
  Widget _buildChangeIndicator() {
    if (_previousStatistics == null) return const SizedBox.shrink();

    final currentCount = widget.statistics.filteredCount;
    final previousCount = _previousStatistics!.filteredCount;
    final change = currentCount - previousCount;

    if (change == 0) return const SizedBox.shrink();

    final isIncrease = change > 0;
    final color = isIncrease ? Colors.green : Colors.red;
    final icon = isIncrease ? Icons.trending_up : Icons.trending_down;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            '${isIncrease ? '+' : ''}$change',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建统计卡片
  Widget _buildStatCard(
    String label,
    String value,
    IconData icon,
    MaterialColor color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        children: [
          Icon(icon, size: 20, color: color.shade600),
          const SizedBox(height: 6),
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: Text(
              value,
              key: ValueKey(value),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color.shade700,
              ),
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建快速操作区域
  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '快速设置',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionButton(
                '全部显示',
                Icons.visibility,
                () => _applyPreset('all'),
                isActive: _config.isDefault,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionButton(
                '仅推荐',
                Icons.recommend,
                () => _applyPreset('recommended'),
                isActive: _isRecommendedPreset(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionButton(
                '重置',
                Icons.refresh,
                () => _applyPreset('reset'),
                isActive: false,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建快速操作按钮
  Widget _buildQuickActionButton(
    String label,
    IconData icon,
    VoidCallback onPressed, {
    bool isActive = false,
  }) {
    return Material(
      color:
          isActive
              ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
              : Colors.grey.shade50,
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Column(
            children: [
              Icon(
                icon,
                size: 20,
                color:
                    isActive
                        ? Theme.of(context).primaryColor
                        : Colors.grey.shade600,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color:
                      isActive
                          ? Theme.of(context).primaryColor
                          : Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 应用预设配置
  void _applyPreset(String preset) {
    FilterConfig newConfig;
    switch (preset) {
      case 'all':
        newConfig = FilterConfig.relaxedConfig();
        break;
      case 'recommended':
        newConfig = _createRecommendedConfig();
        break;
      case 'reset':
        newConfig = FilterConfig.defaultConfig();
        break;
      default:
        return;
    }
    _updateConfig(newConfig);
  }

  /// 创建推荐配置
  FilterConfig _createRecommendedConfig() {
    return FilterConfig.defaultConfig().copyWith(
      // 显示所有类型
      enabledTypes: {MarkerType.spot, MarkerType.activity},
      showLureSpots: true,
      showTraditionalSpots: true,
      showPaidSpots: true,
      // 启用质量过滤
      requireOnSite: true,
      requireRealPhotos: true,
      requirePositiveLikes: true,
      // 不显示过期活动
      showExpired: false,
      // 限制显示数量
      maxDisplayCount: 100,
    );
  }

  /// 检查是否为推荐预设
  bool _isRecommendedPreset() {
    final recommended = _createRecommendedConfig();
    return _config.enabledTypes.containsAll(recommended.enabledTypes) &&
        _config.requireOnSite == recommended.requireOnSite &&
        _config.requireRealPhotos == recommended.requireRealPhotos &&
        _config.requirePositiveLikes == recommended.requirePositiveLikes &&
        _config.showExpired == recommended.showExpired;
  }

  /// 构建类型过滤区域
  Widget _buildTypeFilters() {
    return _buildFilterSection(
      title: '显示类型',
      icon: FontAwesomeIcons.layerGroup,
      children: [
        _buildSwitchCard(
          title: '钓点分享',
          subtitle: '显示用户分享的钓点',
          icon: FontAwesomeIcons.mapPin,
          value: _config.showSpots,
          onChanged: (value) {
            debugPrint('🎛️ [底部面板] 钓点分享开关: ${_config.showSpots} -> $value');
            _updateConfig(_config.copyWithShowSpots(value));
          },
        ),
        _buildSwitchCard(
          title: '活动分享',
          subtitle: '显示钓鱼活动信息',
          icon: FontAwesomeIcons.calendar,
          value: _config.showActivities,
          onChanged:
              (value) => _updateConfig(_config.copyWithShowActivities(value)),
        ),
        _buildSwitchCard(
          title: '路亚标点',
          subtitle: '专门的路亚钓点',
          icon: FontAwesomeIcons.bullseye,
          value: _config.showLureSpots,
          onChanged:
              (value) => _updateConfig(_config.copyWith(showLureSpots: value)),
        ),
        _buildSwitchCard(
          title: '传统钓点',
          subtitle: '野钓和海钓点',
          icon: FontAwesomeIcons.fish,
          value: _config.showTraditionalSpots,
          onChanged:
              (value) =>
                  _updateConfig(_config.copyWith(showTraditionalSpots: value)),
        ),
        _buildSwitchCard(
          title: '收费钓点',
          subtitle: '付费鱼塘和钓场',
          icon: FontAwesomeIcons.coins,
          value: _config.showPaidSpots,
          onChanged:
              (value) => _updateConfig(_config.copyWith(showPaidSpots: value)),
        ),
      ],
    );
  }

  /// 构建质量过滤区域
  Widget _buildQualityFilters() {
    return _buildFilterSection(
      title: '质量筛选',
      icon: FontAwesomeIcons.award,
      children: [
        _buildSwitchCard(
          title: '实地标签',
          subtitle: '要求有实地验证标签',
          icon: FontAwesomeIcons.locationDot,
          value: _config.requireFieldTag,
          onChanged:
              (value) => _updateConfig(_config.copyWithRequireFieldTag(value)),
        ),
        _buildSwitchCard(
          title: '照片标签',
          subtitle: '要求有实拍照片',
          icon: FontAwesomeIcons.camera,
          value: _config.requirePhotoTag,
          onChanged:
              (value) => _updateConfig(_config.copyWithRequirePhotoTag(value)),
        ),
        _buildSwitchCard(
          title: '较多点赞',
          subtitle: '过滤低评价内容',
          icon: FontAwesomeIcons.thumbsUp,
          value: _config.requireHighLikes,
          onChanged:
              (value) => _updateConfig(_config.copyWithRequireHighLikes(value)),
        ),
        _buildSwitchCard(
          title: '过期活动',
          subtitle: '显示已过期的活动',
          icon: FontAwesomeIcons.clockRotateLeft,
          value: _config.showExpired,
          onChanged:
              (value) => _updateConfig(_config.copyWith(showExpired: value)),
        ),
      ],
    );
  }

  /// 构建显示设置区域
  Widget _buildDisplaySettings() {
    return _buildFilterSection(
      title: '显示设置',
      icon: FontAwesomeIcons.sliders,
      children: [
        _buildSliderCard(
          title: '最大显示数量',
          subtitle: '限制地图上显示的标记数量',
          icon: FontAwesomeIcons.hashtag,
          value: _config.maxDisplayCount.toDouble(),
          min: 10,
          max: 500,
          divisions: 49,
          onChanged:
              (value) => _updateConfig(
                _config.copyWith(maxDisplayCount: value.round()),
              ),
        ),
      ],
    );
  }

  /// 构建过滤区域
  Widget _buildFilterSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 18, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  /// 构建开关卡片
  Widget _buildSwitchCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: value ? Colors.white : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              value
                  ? Theme.of(context).primaryColor.withValues(alpha: 0.4)
                  : Colors.grey.shade200,
          width: value ? 1.5 : 1,
        ),
        boxShadow:
            value
                ? [
                  BoxShadow(
                    color: Theme.of(
                      context,
                    ).primaryColor.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
                : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: Semantics(
          label: title,
          hint: subtitle,
          value: value ? '已启用' : '已禁用',
          onTap: () => onChanged(!value),
          child: InkWell(
            onTap: () => onChanged(!value),
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color:
                          value
                              ? Theme.of(
                                context,
                              ).primaryColor.withValues(alpha: 0.15)
                              : Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 200),
                      child: Icon(
                        icon,
                        key: ValueKey(value),
                        size: 18,
                        color:
                            value
                                ? Theme.of(context).primaryColor
                                : Colors.grey.shade600,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AnimatedDefaultTextStyle(
                          duration: const Duration(milliseconds: 200),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: value ? Colors.black87 : Colors.black54,
                          ),
                          child: Text(title),
                        ),
                        const SizedBox(height: 2),
                        AnimatedDefaultTextStyle(
                          duration: const Duration(milliseconds: 200),
                          style: TextStyle(
                            fontSize: 13,
                            color:
                                value
                                    ? Colors.grey.shade700
                                    : Colors.grey.shade600,
                          ),
                          child: Text(subtitle),
                        ),
                      ],
                    ),
                  ),
                  AnimatedScale(
                    duration: const Duration(milliseconds: 200),
                    scale: value ? 1.05 : 1.0,
                    child: Switch(
                      value: value,
                      onChanged: onChanged,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建滑动条卡片
  Widget _buildSliderCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required Function(double) onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 18,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '${value.round()}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  /// 构建更多设置按钮
  Widget _buildMoreSettingsButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: widget.onMoreSettings,
        icon: const Icon(Icons.settings),
        label: const Text('更多设置'),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }
}

/// 过滤统计数据
class FilterStatistics {
  /// 总数量
  final int totalCount;

  /// 过滤后数量
  final int filteredCount;

  /// 高优先级数量（用户发布、收藏、点赞的）
  final int priorityCount;

  const FilterStatistics({
    required this.totalCount,
    required this.filteredCount,
    required this.priorityCount,
  });
}
