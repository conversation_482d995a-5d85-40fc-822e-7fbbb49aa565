import 'dart:async';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:uuid/uuid.dart';

import '../models/fishing_spot.dart';
import '../models/spot_visibility.dart';
import '../models/spot_type.dart';
import '../theme/unified_theme.dart';
import '../services/location_verification_service.dart';
import '../services/service_locator.dart';
import '../config/pocketbase_config.dart';
import 'package:path/path.dart' as path;

// 导入新的模块化组件
import 'add_spot_form/spot_name_input.dart';
import 'add_spot_form/spot_description_input.dart';
import 'add_spot_form/water_level_selector.dart';
import 'add_spot_form/bait_selector.dart';
import 'add_spot_form/fish_type_selector.dart';
import 'add_spot_form/spot_type_selector.dart';
import 'add_spot_form/image_upload_widget.dart';
import 'add_spot_form/image_upload_manager.dart';

import 'snackbar.dart';
import 'verification_tags.dart';
import 'location_status_widget.dart';

// 导入基础组件和混入
import 'common/split_screen_form_base.dart';
import 'mixins/form_validation_mixin.dart';
import 'mixins/scroll_behavior_mixin.dart';

/// 分屏添加钓点组件（重构版本）
class SplitScreenAddSpot extends SplitScreenFormBase<FishingSpot> {
  const SplitScreenAddSpot({
    super.key,
    required super.location,
    required super.onLocationChanged,
    required super.onClose,
    required super.onSpotAdded,
    super.suggestedName,
  });

  @override
  State<SplitScreenAddSpot> createState() => _SplitScreenAddSpotState();
}

class _SplitScreenAddSpotState
    extends SplitScreenFormBaseState<FishingSpot, SplitScreenAddSpot>
    with FormValidationMixin, ScrollBehaviorMixin
    implements CustomHeaderProvider {
  /// 根据钓点类型获取默认描述
  String _getDefaultDescriptionBySpotType(SpotType spotType) {
    switch (spotType) {
      case SpotType.wildFishing:
        return "这是一个野钓钓点，环境自然，鱼类丰富多样，适合休闲垂钓。水质清澈，周围植被茂盛，是钓鱼爱好者的理想选择。";
      case SpotType.seaFishing:
        return "这是一个海钓钓点，面向大海，海风习习，适合海钓爱好者。海水清澈，鱼类种类丰富，是体验海钓乐趣的绝佳地点。";
      case SpotType.lureFishing:
        return "这是一个路亚钓点，适合使用路亚钓法，技巧性较强。水域环境适合掠食性鱼类栖息，是路亚爱好者挑战技巧的好地方。";
      case SpotType.paidFishPond:
        return "这是一个收费鱼塘，设施完善，管理规范，鱼类密度较高。环境整洁，服务周到，适合家庭休闲垂钓和新手学习。";
    }
  }

  // 钓点特有的管理器和控制器
  final _imageUploadManager = ImageUploadManager();
  final _nameController = TextEditingController();
  late final TextEditingController _descriptionController;

  // 钓点特有的表单状态
  String _selectedWaterLevel = '正常';
  String _selectedBait = '商品饵';
  String _selectedFishType = 'carp'; // 默认鲤鱼
  SpotType _selectedSpotType = SpotType.wildFishing; // 默认野钓
  final List<ImageUploadItem> _selectedImages = [];

  // 位置验证状态
  Position? _publishLocation;
  bool _isOnSite = false;
  bool _isCameraShot = false;
  StreamSubscription<Position>? _locationSubscription;

  @override
  void initState() {
    super.initState();
    // 根据当前钓点类型初始化描述控制器
    _descriptionController = TextEditingController(
      text: _getDefaultDescriptionBySpotType(_selectedSpotType),
    );
  }

  @override
  ScrollController? get currentScrollController =>
      super.currentScrollController;

  @override
  Map<String, GlobalKey> get formKeys => super.formKeys;

  @override
  void onInitialize() {
    // 如果有建议的钓点名称，设置到名称字段中
    if (widget.suggestedName != null &&
        widget.suggestedName!.trim().isNotEmpty) {
      _nameController.text = widget.suggestedName!;
    }
  }

  @override
  void onDispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _locationSubscription?.cancel();
  }

  @override
  List<String> getFormKeyNames() {
    return ['nameInput', 'imageUpload', 'descriptionInput'];
  }

  @override
  String getFormTitle() {
    return '添加钓点';
  }

  @override
  String getSubmitButtonText() {
    if (isSubmitting) {
      return '发布中...';
    }
    if (_imageUploadManager.hasUploadingImages(_selectedImages)) {
      return '等待图片上传';
    }
    return '发布钓点';
  }

  @override
  Widget getSubmitButtonIcon() {
    if (isSubmitting ||
        _imageUploadManager.hasUploadingImages(_selectedImages)) {
      return const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2.5,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    // 根据实地验证状态显示不同图标
    IconData iconData = _isOnSite ? Icons.location_on : Icons.publish_rounded;

    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Icon(iconData, color: Colors.white, size: 18),
    );
  }

  @override
  bool validateForm() {
    // 验证图片
    if (!validateImages(_selectedImages, errorMessage: '请添加钓点照片')) {
      scrollToFormField('imageUpload');
      return false;
    }

    // 验证图片上传状态
    if (!validateImageUploading(
      _selectedImages,
      _imageUploadManager.hasUploadingImages,
      errorMessage: '请等待图片上传完成',
    )) {
      scrollToFormField('imageUpload');
      return false;
    }

    // 验证钓点名称
    if (!validateTextNotEmpty(_nameController.text, errorMessage: '请输入钓点名称')) {
      scrollToFormField('nameInput');
      return false;
    }

    // 验证钓点描述
    if (!validateTextMinLength(
      _descriptionController.text,
      15,
      errorMessage: '钓点描述至少需要15个字符',
    )) {
      scrollToFormField('descriptionInput');
      return false;
    }

    // 验证表单字段
    if (!validateFormFields(formKey)) {
      scrollToFirstError();
      return false;
    }

    return true;
  }

  @override
  Future<void> handleSubmit() async {
    final user = Services.auth.currentUser;

    // 额外的提交前验证（提供更具体的错误信息）
    if (_selectedImages.isEmpty) {
      debugPrint('❌ [钓点创建] 照片为必填项');
      SnackBarService.showError(context, '请至少添加一张钓点照片');
      return;
    }

    if (_imageUploadManager.hasUploadingImages(_selectedImages)) {
      debugPrint('⚠️ [钓点创建] 还有照片正在上传');
      SnackBarService.showWarning(context, '请等待照片上传完成');
      return;
    }

    // 创建钓点对象
    String description = _descriptionController.text.trim();

    // 将水位和饵料信息添加到描述中
    List<String> additionalInfo = [];
    if (_selectedWaterLevel != '正常') {
      additionalInfo.add('水位：$_selectedWaterLevel');
    }
    if (_selectedBait != '商品饵') {
      additionalInfo.add('饵料：$_selectedBait');
    }

    if (additionalInfo.isNotEmpty) {
      if (description.isNotEmpty) {
        description += '\n\n';
      }
      description += additionalInfo.join('，');
    }

    final clientGeneratedId = const Uuid().v4();

    final spot = FishingSpot(
      id: clientGeneratedId,
      name: _nameController.text.trim(),
      description: description,
      location: {
        'lat': widget.location.latitude,
        'lon': widget.location.longitude,
      },
      userId: user?.id ?? '',
      userName: user?.username,
      spotType: _selectedSpotType.value,
      fishTypes: _selectedFishType,
      spotEmoji: '🎣',
      fishEmoji: '🐟',
      visibility: SpotVisibility.public,
      visibilityConditions: null,
      visibilityUpdatedAt: DateTime.now(),
      created: DateTime.now(),
      updated: DateTime.now(),
    );

    // 添加钓点
    final addedSpot = await Services.fishingSpot.addSpot(spot);

    if (addedSpot != null) {
      // 异步关联已上传的图片到钓点
      if (_selectedImages.isNotEmpty) {
        final uploadedImages =
            _selectedImages
                .where(
                  (img) =>
                      img.uploadedUrl != null && img.uploadedUrl!.isNotEmpty,
                )
                .toList();

        if (uploadedImages.isNotEmpty) {
          _associateUploadedImagesAsync(addedSpot, user, uploadedImages);
        }
      }

      // 调用成功回调
      widget.onSpotAdded(addedSpot);

      // 显示成功提示
      showSuccessMessage('钓点发布成功！');

      // 清空表单内容
      _clearForm();
    } else {
      throw Exception('添加钓点失败');
    }
  }

  /// 构建自定义标题栏（重写基类方法）
  @override
  Widget buildCustomHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
      child: Row(
        children: [
          // 标题和标签
          Expanded(
            child: Row(
              children: [
                Text(
                  getFormTitle(),
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(width: 12),
                // 验证标签
                VerificationTags(
                  isOnSite: _publishLocation != null && _isOnSite,
                  isCameraShot: _selectedImages.isNotEmpty && _isCameraShot,
                  size: 16, // 较小的尺寸适合标题栏
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.grey, size: 24),
            onPressed: widget.onClose,
          ),
        ],
      ),
    );
  }

  /// 构建自定义坐标显示组件（重写基类方法）
  @override
  Widget buildCoordinateDisplay() {
    return buildFormCard(
      child: Row(
        children: [
          LocationStatusWidget(
            size: 24,
            autoStart: true,
            timeoutSeconds: 11, // 与LocationService保持一致
            showSuccessSnackBar: true, // 启用成功提示
            onLocationUpdate: (data) {
              if (data.location != null) {
                // 更新发布位置
                final newPublishLocation = Position(
                  latitude: data.location!.latitude,
                  longitude: data.location!.longitude,
                  timestamp: DateTime.now(),
                  accuracy: 15.0,
                  altitude: 0,
                  altitudeAccuracy: 0,
                  heading: 0,
                  headingAccuracy: 0,
                  speed: 0,
                  speedAccuracy: 0,
                );

                // 计算实地验证状态
                final isOnSite = LocationVerificationService.isOnSite(
                  widget.location,
                  newPublishLocation,
                );

                setState(() {
                  _publishLocation = newPublishLocation;
                  _isOnSite = isOnSite;
                });

                // 启动位置监听（持续验证实地状态）
                _startLocationMonitoring();
              }
            },
          ),
          const SizedBox(width: 8),
          const Text(
            '坐标位置',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '${widget.location.latitude.toStringAsFixed(6)}, ${widget.location.longitude.toStringAsFixed(6)}',
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  @override
  List<Widget> buildFormContent() {
    return [
      // 钓点名称
      buildFormCard(
        key: formKeys['nameInput'],
        showBottomBorder: false,
        child: SpotNameInput(
          controller: _nameController,
          location: widget.location,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入钓点名称';
            }
            return null;
          },
        ),
      ),

      // 图片上传
      buildFormCard(
        key: formKeys['imageUpload'],
        showBottomBorder: false,
        child: ImageUploadWidget(
          selectedImages: _selectedImages,
          onImagesAdded: _handleImagesAdded,
          onImageRemoved: _handleImageRemoved,
          onImageCancelled: _handleImageCancelled,
          onImageRetry: _handleImageRetry,
        ),
      ),

      // 钓点描述
      buildFormCard(
        key: formKeys['descriptionInput'],
        child: SpotDescriptionInput(
          controller: _descriptionController,
          labelText: '钓点描述',
          hintText: '描述钓点的特色、环境、注意事项等...',
          showCharacterCount: false, // 移除字符统计显示
          minLength: 15,
          requireInput: true,
        ),
      ),

      // 钓点类型、水位、饵料和鱼种选择
      buildFormCard(
        child: Column(
          children: [
            SpotTypeSelector(
              selectedSpotType: _selectedSpotType,
              onChanged: (value) {
                setState(() {
                  _selectedSpotType = value;
                  // 当钓点类型改变时，更新默认描述（如果用户还没有修改过描述）
                  _updateDescriptionIfDefault(value);
                });
              },
            ),
            const SizedBox(height: AppTheme.spacingS),
            WaterLevelSelector(
              selectedWaterLevel: _selectedWaterLevel,
              onChanged: (value) => setState(() => _selectedWaterLevel = value),
            ),
            const SizedBox(height: AppTheme.spacingS),

            BaitSelector(
              selectedBait: _selectedBait,
              onChanged: (value) => setState(() => _selectedBait = value),
            ),
            const SizedBox(height: AppTheme.spacingS),
            FishTypeSelector(
              selectedFishType: _selectedFishType,
              onChanged: (value) => setState(() => _selectedFishType = value),
            ),
          ],
        ),
      ),
    ];
  }

  @override
  void didUpdateWidget(SplitScreenAddSpot oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当建议名称更新时，如果输入框为空，则填充新的建议名称
    if (widget.suggestedName != oldWidget.suggestedName &&
        widget.suggestedName != null &&
        widget.suggestedName!.trim().isNotEmpty &&
        _nameController.text.trim().isEmpty) {
      _nameController.text = widget.suggestedName!;
    }

    // 当位置发生变化时，重新计算实地验证状态
    if (widget.location != oldWidget.location) {
      _updateOnSiteVerificationStatus();
    }
  }

  /// 更新实地验证状态
  void _updateOnSiteVerificationStatus() {
    if (_publishLocation != null) {
      _isOnSite = LocationVerificationService.isOnSite(
        widget.location,
        _publishLocation!,
      );

      if (mounted) {
        setState(() {});
      }
    }
  }

  /// 更新实拍状态
  void _updateCameraShotStatus() {
    if (_selectedImages.isEmpty) {
      _isCameraShot = false;
    } else {
      _isCameraShot = _selectedImages.every((item) => item.isFromCamera);
    }
  }

  /// 如果当前描述是默认值，则更新为新类型的默认描述
  void _updateDescriptionIfDefault(SpotType newSpotType) {
    final currentText = _descriptionController.text;

    // 检查当前文本是否是任何一种默认描述
    final isDefaultDescription = SpotType.values.any(
      (type) => _getDefaultDescriptionBySpotType(type) == currentText,
    );

    // 如果是默认描述，则更新为新类型的默认描述
    if (isDefaultDescription) {
      _descriptionController.text = _getDefaultDescriptionBySpotType(
        newSpotType,
      );
    }
  }

  /// 启动位置监听（持续验证实地状态）
  void _startLocationMonitoring() {
    // 取消之前的监听
    _locationSubscription?.cancel();

    // 启动新的位置监听
    _locationSubscription = LocationVerificationService.listenToLocationChanges(
      widget.location,
      (bool isOnSite, Position position) {
        if (mounted) {
          setState(() {
            _publishLocation = position;
            _isOnSite = isOnSite;
          });
        }
      },
    );
  }

  // 删除未使用的方法

  /// 处理图片添加
  void _handleImagesAdded(List<ImageUploadItem> newImages) {
    setState(() {
      _selectedImages.addAll(newImages);
      // 更新实拍状态
      _updateCameraShotStatus();
    });

    // 开始上传图片
    _imageUploadManager.uploadSelectedImages(newImages, (updatedItem) {
      setState(() {
        // 找到对应的item并更新其状态
        final index = _selectedImages.indexWhere(
          (item) => item.file.path == updatedItem.file.path,
        );
        if (index != -1) {
          _selectedImages[index] = updatedItem;
        }
      });
    });
  }

  /// 处理图片删除
  void _handleImageRemoved(int index) {
    if (index >= 0 && index < _selectedImages.length) {
      setState(() {
        _selectedImages.removeAt(index);
        // 更新实拍状态
        _updateCameraShotStatus();
      });
    }
  }

  /// 处理图片取消上传
  void _handleImageCancelled(int index) {
    _imageUploadManager.cancelImageUpload(_selectedImages, index);
    setState(() {
      // 触发UI更新
    });
  }

  /// 处理图片重试上传
  void _handleImageRetry(int index) {
    _imageUploadManager.retryImageUpload(_selectedImages, index, (updatedItem) {
      setState(() {
        // 找到对应的item并更新其状态
        final itemIndex = _selectedImages.indexWhere(
          (item) => item.file.path == updatedItem.file.path,
        );
        if (itemIndex != -1) {
          _selectedImages[itemIndex] = updatedItem;
        }
      });
    });
  }

  // 删除未使用的方法

  /// 清空表单内容和状态
  void _clearForm() {
    // 清空文本输入框
    _nameController.clear();
    _descriptionController.clear();

    // 重置选择项为默认值
    setState(() {
      _selectedWaterLevel = '正常';
      _selectedBait = '商品饵';
      _selectedSpotType = SpotType.wildFishing;

      // 清空图片列表
      _selectedImages.clear();
    });

    debugPrint('✅ [表单清空] 表单内容已清空');
  }

  /// 异步关联图片（后台处理，不阻塞UI）
  void _associateUploadedImagesAsync(
    FishingSpot spot,
    dynamic user,
    List<ImageUploadItem> uploadedImages,
  ) {
    // 在后台异步处理，不阻塞UI
    Future.microtask(() async {
      try {
        await _associateUploadedImages(spot, user, uploadedImages);

        // 关联完成后显示成功提示
        if (mounted) {
          SnackBarService.showSuccess(context, '图片关联完成');
        }
      } catch (e) {
        final errorMessage = '钓点发布成功，但图片保存失败: $e';
        debugPrint('❌ [异步图片关联] $errorMessage');

        if (mounted) {
          SnackBarService.showError(context, errorMessage);
        }
      }
    });
  }

  /// 关联已上传的图片到钓点（并行处理）
  Future<void> _associateUploadedImages(
    FishingSpot spot,
    dynamic user,
    List<ImageUploadItem> uploadedImages,
  ) async {
    try {
      // 逐个处理图片记录创建，收集详细错误信息
      List<String> failedReasons = [];
      int successCount = 0;

      for (int i = 0; i < uploadedImages.length; i++) {
        final imageItem = uploadedImages[i];

        try {
          await _savePhotoRecord(
            spotId: spot.id,
            userId: user.id,
            imageItem: imageItem,
            sortOrder: i,
          );

          successCount++;
          debugPrint('✅ [分屏图片关联] 照片记录 ${i + 1} 保存成功');
        } catch (e) {
          final reason = '照片${i + 1}: $e';
          failedReasons.add(reason);
          debugPrint('❌ [分屏图片关联] 照片记录 ${i + 1} 保存失败: $e');
        }
      }

      if (failedReasons.isNotEmpty) {
        final errorMessage =
            '${failedReasons.length}张照片保存失败:\n${failedReasons.join('\n')}';
        debugPrint('⚠️ [分屏图片关联] $errorMessage');
        throw Exception(errorMessage);
      } else {
        debugPrint('✅ [分屏图片关联] 所有 $successCount 张照片记录保存成功');
      }
    } catch (e) {
      debugPrint('❌ [分屏图片关联] 关联失败: $e');
      // 重新抛出异常，让上层处理
      rethrow;
    }
  }

  /// 根据文件扩展名和媒体类型生成正确的MIME类型
  String _generateMimeType(ImageUploadItem item) {
    final extension = path
        .extension(item.file.path)
        .toLowerCase()
        .replaceAll('.', '');

    if (item.mediaType == MediaType.video) {
      switch (extension) {
        case 'mp4':
          return 'video/mp4';
        case 'mov':
          return 'video/quicktime';
        case 'avi':
          return 'video/x-msvideo';
        case 'mkv':
          return 'video/x-matroska';
        case 'webm':
          return 'video/webm';
        case '3gp':
          return 'video/3gpp';
        case 'flv':
          return 'video/x-flv';
        default:
          return 'video/mp4'; // 默认视频类型
      }
    } else {
      switch (extension) {
        case 'jpg':
        case 'jpeg':
          return 'image/jpeg';
        case 'png':
          return 'image/png';
        case 'gif':
          return 'image/gif';
        case 'bmp':
          return 'image/bmp';
        case 'webp':
          return 'image/webp';
        case 'svg':
          return 'image/svg+xml';
        default:
          return 'image/jpeg'; // 默认图片类型
      }
    }
  }

  /// 保存照片记录到数据库
  Future<bool> _savePhotoRecord({
    required String spotId,
    required String userId,
    required ImageUploadItem imageItem,
    required int sortOrder,
  }) async {
    try {
      // 获取PocketBase客户端
      final pb = PocketBaseConfig.instance.client;

      // 确定正确的photo_source值（数据库枚举值：gallery, camera）
      String photoSource;
      if (imageItem.isFromCamera) {
        photoSource = 'camera'; // 相机拍摄
      } else {
        photoSource = 'gallery'; // 相册选择
      }

      debugPrint('🔍 [分屏照片记录] 设置photo_source为: $photoSource');

      // 生成正确的MIME类型
      final mimeType = _generateMimeType(imageItem);
      debugPrint(
        '🔍 [分屏照片记录] 生成MIME类型: $mimeType (媒体类型: ${imageItem.mediaType.name})',
      );

      // 创建照片记录
      final record = await pb
          .collection('spot_photos')
          .create(
            body: {
              'spot_id': spotId,
              'user_id': userId,
              'filename': path.basename(imageItem.file.path),
              'url': imageItem.uploadedUrl,
              'thumbnail_url': imageItem.thumbnailUrl ?? imageItem.uploadedUrl,
              'storage_path': imageItem.uploadedUrl,
              'thumbnail_path': imageItem.thumbnailUrl ?? imageItem.uploadedUrl,
              'type': 'normal',
              'description': null,
              'sort_order': sortOrder,
              'file_size': await imageItem.file.length(),
              'mime_type': mimeType, // 使用动态生成的MIME类型
              'is_camera_shot': imageItem.isFromCamera,
              'photo_source': photoSource, // 使用修正后的值
            },
          );

      debugPrint('✅ [照片记录] 保存成功，记录ID: ${record.id}');
      return true;
    } catch (e) {
      final errorMessage = '照片记录保存失败: $e';
      debugPrint('❌ [照片记录] $errorMessage');

      // 抛出详细的错误信息，让上层调用者能够获取具体错误
      throw Exception(errorMessage);
    }
  }
}
