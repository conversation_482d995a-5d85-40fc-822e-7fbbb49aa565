import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/fishing_activity.dart';
import '../config/activity_config.dart';
import '../providers/add_activity_provider.dart';
import '../services/activity_creation_service.dart';
import '../services/service_locator.dart';
import 'snackbar.dart';
import 'common/submit_button_with_hint.dart';

// 导入组件
import 'add_spot_form/spot_name_input.dart';
import 'add_spot_form/spot_description_input.dart';
import 'add_spot_form/fish_type_selector.dart';
import 'add_spot_form/image_upload_widget.dart';
import 'add_activity_form/fishing_time_selector.dart';
import 'add_activity_form/activity_type_selector.dart';

// 导入基础组件和混入
import 'common/split_screen_form_base.dart';
import 'mixins/form_validation_mixin.dart';
import 'mixins/scroll_behavior_mixin.dart';

/// 分屏添加钓鱼活动组件
class SplitScreenAddActivity extends SplitScreenFormBase<FishingActivity> {
  const SplitScreenAddActivity({
    super.key,
    required super.location,
    required super.onLocationChanged,
    required super.onClose,
    required super.onSpotAdded, // 这里使用基类的参数名，但在内部我们将其视为onActivityAdded
    super.suggestedName,
  });

  // 为了向后兼容，提供一个getter来访问活动添加回调
  Function(FishingActivity) get onActivityAdded => onSpotAdded;

  @override
  State<SplitScreenAddActivity> createState() => _SplitScreenAddActivityState();
}

class _SplitScreenAddActivityState
    extends SplitScreenFormBaseState<FishingActivity, SplitScreenAddActivity>
    with FormValidationMixin, ScrollBehaviorMixin {
  // 活动特有的服务
  final ActivityCreationService _creationService = ActivityCreationService();

  @override
  ScrollController? get currentScrollController =>
      super.currentScrollController;

  @override
  Map<String, GlobalKey> get formKeys => super.formKeys;

  @override
  void onInitialize() {
    // 活动页面特有的初始化逻辑
  }

  @override
  void onDispose() {
    // 活动页面特有的清理逻辑
  }

  @override
  List<String> getFormKeyNames() {
    return ['activityName', 'fishingTime', 'imageUpload'];
  }

  @override
  String getFormTitle() {
    return '添加钓鱼活动';
  }

  @override
  String getSubmitButtonText() {
    // 这个方法在活动页面中不会被使用，因为我们使用Provider模式
    return '发布活动';
  }

  @override
  Widget getSubmitButtonIcon() {
    // 这个方法在活动页面中不会被使用，因为我们使用Provider模式
    return const Icon(Icons.publish);
  }

  @override
  bool validateForm() {
    // 这个方法在活动页面中不会被使用，因为我们有自己的验证逻辑
    return true;
  }

  @override
  Future<void> handleSubmit() async {
    // 这个方法在活动页面中不会被使用，因为我们有自己的提交逻辑
  }

  @override
  List<Widget> buildFormContent() {
    // 这个方法在活动页面中不会被使用，因为我们使用Provider模式
    return [];
  }

  /// 处理提交
  Future<void> _handleSubmit(AddActivityProvider provider) async {
    // 检查用户登录状态（与钓点页面保持一致）
    if (!Services.auth.isLoggedIn) {
      // 跳转到登录页面，等待登录结果
      final result = await Navigator.pushNamed(context, '/login');
      // 如果登录成功，继续发布流程
      if (result == true && Services.auth.isLoggedIn) {
        // 登录成功后，递归调用自己继续发布
        await _handleSubmit(provider);
      }
      return;
    }

    // 验证表单
    if (!_validateForm(provider)) {
      return;
    }

    // 直接创建活动
    await _confirmCreateActivity(provider);
  }

  /// 验证表单
  bool _validateForm(AddActivityProvider provider) {
    // 检查用户登录状态（与钓点页面保持一致）
    if (!Services.auth.isLoggedIn) {
      SnackBarService.showError(context, '请先登录');
      return false;
    }

    // 检查活动名称
    if (provider.nameController.text.trim().isEmpty) {
      SnackBarService.showError(context, '请输入活动名称');
      scrollToFormField('activityName');
      return false;
    }

    // 检查钓鱼时间
    if (provider.selectedFishingTime == null) {
      SnackBarService.showError(context, '请选择钓鱼时间');
      scrollToFormField('fishingTime');
      return false;
    }

    if (provider.selectedFishingTime!.isBefore(DateTime.now())) {
      SnackBarService.showError(context, '钓鱼时间不能是过去的时间');
      scrollToFormField('fishingTime');
      return false;
    }

    // 检查活动开始时间是否小于1小时
    final oneHourLater = DateTime.now().add(const Duration(hours: 1));
    if (provider.selectedFishingTime!.isBefore(oneHourLater)) {
      SnackBarService.showWarning(context, '活动开始时间建议至少提前1小时安排，以便钓友有充足的准备时间');
      scrollToFormField('fishingTime');
      return false;
    }

    // 检查时间不能超过30天
    final maxFutureTime = DateTime.now().add(const Duration(days: 30));
    if (provider.selectedFishingTime!.isAfter(maxFutureTime)) {
      SnackBarService.showError(context, '钓鱼时间不能超过30天后');
      scrollToFormField('fishingTime');
      return false;
    }

    // 检查活动描述长度（与钓点页面保持一致的验证逻辑）
    final description = provider.descriptionController.text.trim();
    if (description.isNotEmpty && description.length < 15) {
      SnackBarService.showError(context, '活动描述至少需要15个字符');
      scrollToFormField('activityName'); // 滚动到描述区域
      return false;
    }

    // 检查是否有照片正在上传
    if (provider.imageUploadManager.hasUploadingImages(
      provider.selectedImages,
    )) {
      SnackBarService.showError(context, '请等待图片上传完成');
      scrollToFormField('imageUpload');
      return false;
    }

    return true;
  }

  // 这些方法已经在基类和混入中提供，不再需要

  /// 确认创建活动
  Future<void> _confirmCreateActivity(AddActivityProvider provider) async {
    provider.setSubmitting(true);

    try {
      // 创建活动对象
      final activity = provider.createActivity();

      // 调用创建服务
      final result = await _creationService.createActivity(
        activity: activity,
        images: provider.selectedImages,
        publishLocation: provider.publishLocation,
      );

      if (result.success && result.activity != null) {
        // 显示成功提示
        SnackBarService.showSuccess(
          context,
          '约钓活动发布成功！\n钓鱼时间：${ActivityConfig.formatDateTime(result.activity!.startTime)}',
        );

        // 重置表单
        provider.resetForm();

        // 调用成功回调
        widget.onSpotAdded(result.activity!);
      } else {
        final errorMessage = ActivityCreationService.getFriendlyErrorMessage(
          result.errorCode,
          result.errorMessage,
        );
        SnackBarService.showError(context, errorMessage);
      }
    } catch (e) {
      debugPrint('❌ [添加活动] 创建活动异常: $e');
      SnackBarService.showError(context, '创建活动失败，请重试');
    } finally {
      provider.setSubmitting(false);
    }
  }

  /// 构建表单卡片
  Widget _buildFormCard({Key? key, required Widget child}) {
    return Container(
      key: key,
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 0.5),
        ),
      ),
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create:
          (context) =>
              AddActivityProvider()..initialize(
                location: widget.location,
                suggestedName: widget.suggestedName,
              ),
      child: Consumer<AddActivityProvider>(
        builder: (context, provider, child) {
          return DraggableScrollableSheet(
            controller: dragController,
            initialChildSize: ActivityConfig.defaultSheetSize,
            minChildSize: ActivityConfig.minSheetSize,
            maxChildSize: ActivityConfig.maxSheetSize,
            snap: true,
            snapSizes: ActivityConfig.snapSizes,
            builder: (context, scrollController) {
              // 保存ScrollController引用到基类
              currentScrollController = scrollController;

              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Colors.white, Colors.grey.shade50],
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.08),
                      blurRadius: 32,
                      offset: const Offset(0, -8),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                  child:
                      provider.isSubmitting
                          ? const Center(child: CircularProgressIndicator())
                          : Form(
                            key: provider.formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // 拖拽指示器
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 4,
                                  ),
                                  child: Center(
                                    child: Container(
                                      margin: const EdgeInsets.only(
                                        top: 8,
                                        bottom: 0,
                                      ),
                                      width: 48,
                                      height: 5,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            Colors.grey.shade300,
                                            Colors.grey.shade400,
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(3),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withValues(
                                              alpha: 0.1,
                                            ),
                                            blurRadius: 4,
                                            offset: const Offset(0, 1),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),

                                // 标题栏
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 4,
                                  ),
                                  child: Row(
                                    children: [
                                      const Text(
                                        '一起去钓鱼',
                                        style: TextStyle(
                                          fontSize: 24,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black87,
                                        ),
                                      ),
                                      const Spacer(),
                                      IconButton(
                                        icon: const Icon(
                                          Icons.close,
                                          color: Colors.grey,
                                          size: 24,
                                        ),
                                        onPressed: widget.onClose,
                                      ),
                                    ],
                                  ),
                                ),

                                // 表单内容区域
                                Expanded(
                                  child: Container(
                                    color: Colors.grey.shade50,
                                    child: SingleChildScrollView(
                                      controller: scrollController,
                                      physics: const BouncingScrollPhysics(),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          // 坐标信息
                                          _buildFormCard(
                                            child: Row(
                                              children: [
                                                Icon(
                                                  Icons.location_on,
                                                  size: 20,
                                                  color: Colors.blue.shade600,
                                                ),
                                                const SizedBox(width: 8),
                                                const Text(
                                                  '坐标位置',
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w500,
                                                    color: Colors.black87,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                Expanded(
                                                  child: Text(
                                                    '${widget.location.latitude.toStringAsFixed(6)}, ${widget.location.longitude.toStringAsFixed(6)}',
                                                    style: TextStyle(
                                                      fontSize: 12,
                                                      color:
                                                          Colors.grey.shade600,
                                                    ),
                                                    textAlign: TextAlign.right,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),

                                          // 活动类型选择
                                          _buildFormCard(
                                            child: ActivityTypeSelector(
                                              selectedActivityType:
                                                  provider.selectedActivityType,
                                              onChanged:
                                                  provider.updateActivityType,
                                            ),
                                          ),

                                          // 活动名称
                                          _buildFormCard(
                                            key: formKeys['activityName'],
                                            child: SpotNameInput(
                                              controller:
                                                  provider.nameController,
                                              location:
                                                  provider.activityLocation ??
                                                  widget.location,
                                              autoFetchLocationName: true,
                                              labelText: '活动名称',
                                              nameSuffix: '钓鱼',
                                              validator: (value) {
                                                if (value == null ||
                                                    value.trim().isEmpty) {
                                                  return '请输入活动名称';
                                                }
                                                if (value.trim().length >
                                                    ActivityConfig
                                                        .maxTitleLength) {
                                                  return '活动名称不能超过${ActivityConfig.maxTitleLength}个字符';
                                                }
                                                return null;
                                              },
                                            ),
                                          ),

                                          // 钓鱼时间
                                          _buildFormCard(
                                            key: formKeys['fishingTime'],
                                            child: FishingTimeSelector(
                                              selectedTime:
                                                  provider.selectedFishingTime,
                                              onTimeChanged: (DateTime time) {
                                                // 使用默认的2小时持续时长
                                                provider.updateFishingTime(
                                                  time,
                                                  2.0,
                                                );
                                              },
                                            ),
                                          ),

                                          // 图片上传
                                          _buildFormCard(
                                            key: formKeys['imageUpload'],
                                            child: ImageUploadWidget(
                                              selectedImages:
                                                  provider.selectedImages,
                                              onImagesAdded: provider.addImages,
                                              onImageRemoved:
                                                  provider.removeImage,
                                              onImageCancelled:
                                                  (index) => provider
                                                      .cancelImageUpload(index),
                                              onImageRetry:
                                                  (index) => provider
                                                      .retryImageUpload(index),
                                            ),
                                          ),

                                          // 活动描述
                                          _buildFormCard(
                                            child: SpotDescriptionInput(
                                              controller:
                                                  provider
                                                      .descriptionController,
                                              labelText: '活动描述',
                                              hintText: '描述约钓活动的详情、要求、注意事项等...',
                                              maxLength:
                                                  ActivityConfig
                                                      .maxDescriptionLength,
                                              showCharacterCount:
                                                  false, // 活动描述不强制字数要求
                                            ),
                                          ),

                                          // 鱼种选择
                                          _buildFormCard(
                                            child: FishTypeSelector(
                                              selectedFishType:
                                                  provider.selectedFishType,
                                              onChanged:
                                                  provider.updateFishType,
                                            ),
                                          ),

                                          // 发布按钮区域
                                          Container(
                                            color: Colors.grey.shade50,
                                            padding: const EdgeInsets.all(20),
                                            child: _buildSubmitSection(
                                              provider,
                                            ),
                                          ),

                                          const SizedBox(height: 24),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  /// 构建提交区域（包含按钮和提示）
  Widget _buildSubmitSection(AddActivityProvider provider) {
    final disabledReason = provider.getSubmitDisabledReason();
    final canSubmit = provider.canSubmit();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 使用通用的提交按钮组件
        ActivitySubmitButton(
          canSubmit: canSubmit,
          disabledReason: disabledReason,
          onSubmit: () => _handleSubmit(provider),
          isSubmitting:
              provider.isSubmitting ||
              provider.imageUploadManager.hasUploadingImages(
                provider.selectedImages,
              ),
        ),
      ],
    );
  }
}
