import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

/// 加载错误处理器
///
/// 提供统一的加载状态和错误处理UI组件
/// 支持多种错误类型和重试机制
class LoadingErrorHandler extends StatefulWidget {
  /// 错误类型
  final LoadingErrorType errorType;

  /// 错误消息
  final String? errorMessage;

  /// 重试回调
  final VoidCallback? onRetry;

  /// 是否显示详细错误信息
  final bool showDetails;

  /// 自定义操作按钮
  final List<Widget>? customActions;

  /// 是否显示在底部
  final bool showAtBottom;

  /// 自定义颜色主题
  final Color? primaryColor;

  const LoadingErrorHandler({
    super.key,
    required this.errorType,
    this.errorMessage,
    this.onRetry,
    this.showDetails = true,
    this.customActions,
    this.showAtBottom = false,
    this.primaryColor,
  });

  @override
  State<LoadingErrorHandler> createState() => _LoadingErrorHandlerState();
}

class _LoadingErrorHandlerState extends State<LoadingErrorHandler>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: widget.showAtBottom ? const Offset(0, 0.5) : const Offset(0, -0.5),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _getErrorColor().withOpacity(0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildErrorHeader(),

              if (widget.showDetails) ...[
                const SizedBox(height: 12),
                _buildErrorDetails(),
              ],

              const SizedBox(height: 16),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建错误头部
  Widget _buildErrorHeader() {
    final errorInfo = _getErrorInfo();

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getErrorColor().withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: FaIcon(errorInfo.icon, color: _getErrorColor(), size: 20),
        ),

        const SizedBox(width: 12),

        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                errorInfo.title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: _getErrorColor(),
                ),
              ),

              if (errorInfo.subtitle.isNotEmpty)
                Text(
                  errorInfo.subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建错误详情
  Widget _buildErrorDetails() {
    if (widget.errorMessage == null || widget.errorMessage!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        widget.errorMessage!,
        style: Theme.of(
          context,
        ).textTheme.bodySmall?.copyWith(fontFamily: 'monospace'),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    final List<Widget> buttons = [];

    // 重试按钮
    if (widget.onRetry != null) {
      buttons.add(
        ElevatedButton.icon(
          onPressed: widget.onRetry,
          icon: const FaIcon(FontAwesomeIcons.arrowRotateRight, size: 14),
          label: const Text('重试'),
          style: ElevatedButton.styleFrom(
            backgroundColor:
                widget.primaryColor ?? Theme.of(context).primaryColor,
            foregroundColor: Colors.white,
          ),
        ),
      );
    }

    // 自定义操作按钮
    if (widget.customActions != null) {
      buttons.addAll(widget.customActions!);
    }

    // 默认操作按钮
    if (buttons.isEmpty) {
      buttons.add(
        TextButton.icon(
          onPressed: () => Navigator.of(context).pop(),
          icon: const FaIcon(FontAwesomeIcons.xmark, size: 14),
          label: const Text('关闭'),
        ),
      );
    }

    return Wrap(spacing: 8, runSpacing: 8, children: buttons);
  }

  /// 获取错误颜色
  Color _getErrorColor() {
    switch (widget.errorType) {
      case LoadingErrorType.network:
        return Colors.orange;
      case LoadingErrorType.server:
        return Colors.red;
      case LoadingErrorType.timeout:
        return Colors.amber;
      case LoadingErrorType.offline:
        return Colors.grey;
      case LoadingErrorType.permission:
        return Colors.purple;
      case LoadingErrorType.unknown:
        return Colors.red;
    }
  }

  /// 获取错误信息
  ErrorInfo _getErrorInfo() {
    switch (widget.errorType) {
      case LoadingErrorType.network:
        return ErrorInfo(
          icon: FontAwesomeIcons.wifi,
          title: '网络连接异常',
          subtitle: '请检查网络连接后重试',
        );
      case LoadingErrorType.server:
        return ErrorInfo(
          icon: FontAwesomeIcons.server,
          title: '服务器错误',
          subtitle: '服务器暂时无法响应',
        );
      case LoadingErrorType.timeout:
        return ErrorInfo(
          icon: FontAwesomeIcons.clock,
          title: '请求超时',
          subtitle: '网络响应时间过长',
        );
      case LoadingErrorType.offline:
        return ErrorInfo(
          icon: FontAwesomeIcons.cloudArrowDown,
          title: '离线模式',
          subtitle: '当前处于离线状态',
        );
      case LoadingErrorType.permission:
        return ErrorInfo(
          icon: FontAwesomeIcons.lock,
          title: '权限不足',
          subtitle: '无法访问所需资源',
        );
      case LoadingErrorType.unknown:
        return ErrorInfo(
          icon: FontAwesomeIcons.triangleExclamation,
          title: '加载失败',
          subtitle: '发生未知错误',
        );
    }
  }
}

/// 离线模式指示器
class OfflineModeIndicator extends StatelessWidget {
  final bool isOffline;
  final VoidCallback? onTapToRetry;

  const OfflineModeIndicator({
    super.key,
    required this.isOffline,
    this.onTapToRetry,
  });

  @override
  Widget build(BuildContext context) {
    if (!isOffline) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Colors.grey.shade800,
      child: Row(
        children: [
          const FaIcon(
            FontAwesomeIcons.cloudArrowDown,
            color: Colors.white,
            size: 16,
          ),

          const SizedBox(width: 8),

          Expanded(
            child: Text(
              '离线模式 - 显示缓存数据',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.white),
            ),
          ),

          if (onTapToRetry != null)
            TextButton(
              onPressed: onTapToRetry,
              child: const Text('重试', style: TextStyle(color: Colors.white)),
            ),
        ],
      ),
    );
  }
}

/// 网络状态指示器
class NetworkStatusIndicator extends StatefulWidget {
  final NetworkStatus status;
  final String? message;
  final VoidCallback? onRetry;

  const NetworkStatusIndicator({
    super.key,
    required this.status,
    this.message,
    this.onRetry,
  });

  @override
  State<NetworkStatusIndicator> createState() => _NetworkStatusIndicatorState();
}

class _NetworkStatusIndicatorState extends State<NetworkStatusIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    if (widget.status == NetworkStatus.connecting) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(NetworkStatusIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.status == NetworkStatus.connecting) {
      _pulseController.repeat(reverse: true);
    } else {
      _pulseController.stop();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final statusInfo = _getStatusInfo();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: statusInfo.color,
      child: Row(
        children: [
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Opacity(
                opacity:
                    widget.status == NetworkStatus.connecting
                        ? _pulseAnimation.value
                        : 1.0,
                child: FaIcon(statusInfo.icon, color: Colors.white, size: 16),
              );
            },
          ),

          const SizedBox(width: 8),

          Expanded(
            child: Text(
              widget.message ?? statusInfo.message,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.white),
            ),
          ),

          if (widget.onRetry != null && widget.status == NetworkStatus.error)
            TextButton(
              onPressed: widget.onRetry,
              child: const Text('重试', style: TextStyle(color: Colors.white)),
            ),
        ],
      ),
    );
  }

  NetworkStatusInfo _getStatusInfo() {
    switch (widget.status) {
      case NetworkStatus.connecting:
        return NetworkStatusInfo(
          icon: FontAwesomeIcons.wifi,
          message: '正在连接...',
          color: Colors.blue,
        );
      case NetworkStatus.connected:
        return NetworkStatusInfo(
          icon: FontAwesomeIcons.check,
          message: '网络连接正常',
          color: Colors.green,
        );
      case NetworkStatus.error:
        return NetworkStatusInfo(
          icon: FontAwesomeIcons.triangleExclamation,
          message: '网络连接失败',
          color: Colors.red,
        );
      case NetworkStatus.offline:
        return NetworkStatusInfo(
          icon: FontAwesomeIcons.cloudArrowDown,
          message: '离线模式',
          color: Colors.grey,
        );
    }
  }
}

/// 错误类型枚举
enum LoadingErrorType {
  network, // 网络错误
  server, // 服务器错误
  timeout, // 超时错误
  offline, // 离线状态
  permission, // 权限错误
  unknown, // 未知错误
}

/// 网络状态枚举
enum NetworkStatus {
  connecting, // 连接中
  connected, // 已连接
  error, // 连接错误
  offline, // 离线
}

/// 错误信息数据类
class ErrorInfo {
  final IconData icon;
  final String title;
  final String subtitle;

  ErrorInfo({required this.icon, required this.title, required this.subtitle});
}

/// 网络状态信息数据类
class NetworkStatusInfo {
  final IconData icon;
  final String message;
  final Color color;

  NetworkStatusInfo({
    required this.icon,
    required this.message,
    required this.color,
  });
}
