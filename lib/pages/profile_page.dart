import 'package:flutter/material.dart';
import '../widgets/snackbar.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../models/user.dart';
import '../services/service_locator.dart';
import '../widgets/profile_widgets.dart';
import '../services/user_stats_service.dart';
import 'new_replies_page.dart';
import 'my_spots_page.dart';
import 'my_posts_page.dart';
import 'settings_page.dart';
import 'edit_profile_page.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  bool _isLoggingOut = false;
  UserStats? _userStats;

  @override
  void initState() {
    super.initState();
    _loadUserStats();
  }

  // 加载用户统计数据
  Future<void> _loadUserStats() async {
    try {
      final stats = await Services.userStats.getUserStats();
      if (mounted) {
        setState(() {
          _userStats = stats;
        });
      }
    } catch (e) {
      debugPrint('加载用户统计数据失败: $e');
    }
  }

  // 显示头像选择选项
  void _showAvatarOptions(User user) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                '选择头像',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: const Icon(Icons.camera_alt, color: Color(0xFF4A90E2)),
                title: const Text('拍照'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromCamera();
                },
              ),
              ListTile(
                leading: const Icon(
                  Icons.photo_library,
                  color: Color(0xFF4A90E2),
                ),
                title: const Text('从相册选择'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromGallery();
                },
              ),
              if (user.avatarUrl.isNotEmpty)
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text('删除头像'),
                  onTap: () {
                    Navigator.pop(context);
                    _removeAvatar();
                  },
                ),
              const SizedBox(height: 10),
            ],
          ),
        );
      },
    );
  }

  // 从相机拍照
  Future<void> _pickImageFromCamera() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 512,
        maxHeight: 512, // 头像保持正方形1:1比例
        imageQuality: 80,
      );

      if (image != null) {
        await _uploadAvatar(File(image.path));
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.showError(context, '拍照失败: $e');
      }
    }
  }

  // 从相册选择
  Future<void> _pickImageFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        await _uploadAvatar(File(image.path));
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.showError(context, '选择图片失败: $e');
      }
    }
  }

  // 上传头像
  Future<void> _uploadAvatar(File imageFile) async {
    try {
      // 显示加载指示器
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => const Center(child: CircularProgressIndicator()),
        );
      }

      // 检查用户登录状态
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      // 直接使用PocketBase文件上传更新头像
      final success = await Services.user.updateUserAvatar(
        currentUser.id,
        imageFile.path,
      );

      if (mounted) {
        Navigator.pop(context); // 关闭加载指示器
      }

      if (success) {
        if (mounted) {
          // 重新加载用户数据
          _loadUserStats();
        }
      } else {
        if (mounted) {
          SnackBarService.showError(context, '头像更新失败');
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // 关闭加载指示器
        SnackBarService.showError(context, '头像上传失败: $e');
      }
    }
  }

  // 删除头像
  Future<void> _removeAvatar() async {
    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      // 使用UserService的删除头像方法
      final success = await Services.user.deleteUserAvatar(currentUser.id);

      if (success) {
        if (mounted) {
          // 重新加载用户数据
          _loadUserStats();
        }
      } else {
        if (mounted) {
          SnackBarService.showError(context, '头像删除失败');
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.showError(context, '删除头像失败: $e');
      }
    }
  }

  // 退出登录
  Future<void> _logout() async {
    if (_isLoggingOut) return; // 防止重复点击

    setState(() {
      _isLoggingOut = true;
    });

    try {
      debugPrint('开始退出登录操作');
      await Services.auth.logout(clearSavedCredentials: true);

      if (mounted) {
        // 退出登录成功，不显示提示
      }
    } catch (e) {
      debugPrint('退出登录异常: $e');
      if (mounted) {
        SnackBarService.showError(context, '退出登录失败: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoggingOut = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<User?>(
      valueListenable: Services.auth.currentUserNotifier,
      builder: (context, currentUser, child) {
        // 如果正在登出，显示加载指示器
        if (_isLoggingOut) {
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在退出登录...'),
                ],
              ),
            ),
          );
        }

        // 如果没有登录，显示未登录视图
        if (currentUser == null) {
          return _buildNotLoggedInView();
        }

        // 如果已经登录，显示已登录视图
        return _buildLoggedInView(currentUser);
      },
    );
  }

  // 构建未登录视图
  Widget _buildNotLoggedInView() {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 用户信息卡片（未登录状态）
            ProfileWidgets.buildUserInfoCard(
              avatarUrl: null,
              nickname: '未登录',
              bio: '登录后查看更多功能',
              points: 0,
              isLoggedIn: false,
              onEditProfile: () {
                Navigator.pushNamed(context, '/login');
              },
              onAvatarTap: () {
                Navigator.pushNamed(context, '/login');
              },
            ),

            const SizedBox(height: 20),

            // 数据统计（未登录状态）
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  ProfileWidgets.buildStatsCard(
                    count: '0',
                    label: '漂豆',
                    backgroundColor: const Color(0xFFFF6B6B), // 红色系
                    onTap: () {
                      Navigator.pushNamed(context, '/login');
                    },
                  ),
                  ProfileWidgets.buildStatsCard(
                    count: '0',
                    label: '钓点',
                    backgroundColor: const Color(0xFF4ECDC4), // 青色系
                    onTap: () {
                      Navigator.pushNamed(context, '/login');
                    },
                  ),
                  ProfileWidgets.buildStatsCard(
                    count: '0',
                    label: '动态',
                    backgroundColor: const Color(0xFF45B7D1), // 蓝色系
                    onTap: () {
                      Navigator.pushNamed(context, '/login');
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(height: 30),

            // 登录提示
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 20),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.account_circle_outlined,
                    size: 60,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '登录后享受更多功能',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '发布钓点、分享动态、收藏内容',
                    style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
                  ),
                  const SizedBox(height: 20),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => Navigator.pushNamed(context, '/login'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF4A90E2),
                        foregroundColor: Colors.white,
                        minimumSize: const Size(double.infinity, 50),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                      child: const Text(
                        '立即登录',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 30),
          ],
        ),
      ),
    );
  }

  // 构建已登录视图
  Widget _buildLoggedInView(User user) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 用户信息卡片
            ProfileWidgets.buildUserInfoCard(
              avatarUrl: user.avatarUrl,
              nickname: user.nickname,
              bio: user.bio,
              points: _userStats?.points ?? 0,
              isLoggedIn: true,
              onEditProfile: () async {
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => EditProfilePage(user: user),
                  ),
                );

                // 如果有更新，重新加载用户数据
                if (result == true) {
                  _loadUserStats();
                }
              },
              onAvatarTap: () {
                _showAvatarOptions(user);
              },
            ),

            const SizedBox(height: 20),

            // 数据统计
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  ProfileWidgets.buildStatsCard(
                    count: '${_userStats?.points ?? 0}',
                    label: '漂豆',
                    backgroundColor: const Color(0xFFFF6B6B), // 红色系
                    onTap: () {
                      // TODO: 跳转到积分详情页面
                      SnackBarService.showInfo(context, '积分详情页面待实现');
                    },
                  ),
                  ProfileWidgets.buildStatsCard(
                    count: '${_userStats?.spotsCount ?? 0}',
                    label: '钓点',
                    backgroundColor: const Color(0xFF4ECDC4), // 青色系
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const MySpotsPage(),
                        ),
                      );
                    },
                  ),
                  ProfileWidgets.buildStatsCard(
                    count: '${_userStats?.postsCount ?? 0}',
                    label: '动态',
                    backgroundColor: const Color(0xFF45B7D1), // 蓝色系
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const MyPostsPage(),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(height: 30),

            // 我的内容
            ProfileWidgets.buildSectionTitle('我的内容'),
            ProfileWidgets.buildMenuTile(
              icon: Icons.notifications,
              title: '最新回复',
              iconColor: Colors.blue,
              trailing:
                  (_userStats?.newRepliesCount ?? 0) > 0
                      ? Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${_userStats?.newRepliesCount}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      )
                      : null,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const NewRepliesPage(),
                  ),
                );
              },
            ),
            ProfileWidgets.buildMenuTile(
              icon: Icons.favorite,
              title: '我的点赞',
              iconColor: Colors.red,
              onTap: () {
                SnackBarService.showInfo(context, '我的点赞页面待实现');
              },
            ),
            ProfileWidgets.buildMenuTile(
              icon: Icons.star,
              title: '我的收藏',
              iconColor: Colors.orange,
              onTap: () {
                SnackBarService.showInfo(context, '我的收藏页面待实现');
              },
            ),
            ProfileWidgets.buildMenuTile(
              icon: Icons.comment,
              title: '我的评论',
              iconColor: Colors.green,
              onTap: () {
                SnackBarService.showInfo(context, '我的评论页面待实现');
              },
            ),

            const SizedBox(height: 20),

            // 其他功能
            ProfileWidgets.buildSectionTitle('其他'),
            ProfileWidgets.buildMenuTile(
              icon: Icons.settings,
              title: '设置',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const SettingsPage()),
                );
              },
            ),
            ProfileWidgets.buildMenuTile(
              icon: Icons.help_outline,
              title: '帮助反馈',
              onTap: () {
                SnackBarService.showInfo(context, '帮助反馈页面待实现');
              },
            ),
            ProfileWidgets.buildMenuTile(
              icon: Icons.info_outline,
              title: '关于应用',
              onTap: () {
                SnackBarService.showInfo(context, '关于应用页面待实现');
              },
            ),

            const SizedBox(height: 30),

            // 退出登录按钮
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 20),
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _logout,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade400,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 50),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                child: const Text(
                  '退出登录',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ),
            ),

            const SizedBox(height: 30),
          ],
        ),
      ),
    );
  }
}
