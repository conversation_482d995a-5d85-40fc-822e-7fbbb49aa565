import 'dart:async';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/fishing_spot.dart';
import '../models/spot_photo.dart';
import '../models/spot_media.dart';
import '../models/spot_visibility.dart';
import '../services/service_locator.dart';
import '../services/baidu_map_navigation_service.dart';

import '../widgets/comments/comments.dart';
import '../widgets/photos/photos.dart';
import '../widgets/media/media_carousel.dart';
import '../widgets/snackbar.dart';
import '../widgets/carousel_height_manager.dart';
import '../widgets/spot_detail/spot_info_card.dart';

import '../config/pocketbase_config.dart';

/// 钓点详情全屏页面
///
/// 特性：
/// - 全屏显示，完美支持键盘避让
/// - SnackBar显示正常
/// - 更好的用户体验和交互空间
/// - 标准的页面结构，易于维护
class SpotDetailPage extends StatefulWidget {
  final FishingSpot spot;

  const SpotDetailPage({super.key, required this.spot});

  @override
  State<SpotDetailPage> createState() => _SpotDetailPageState();
}

class _SpotDetailPageState extends State<SpotDetailPage>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  // 媒体相关（照片和视频）
  List<SpotPhoto> _photos = [];
  List<SpotMediaItem> _mediaItems = [];
  bool _isLoadingPhotos = true;

  // 照片轮播控制器
  late PageController _photoPageController;
  Timer? _autoPlayTimer;
  bool _userInteracted = false;
  final ValueNotifier<int> _currentPhotoIndex = ValueNotifier<int>(0);

  // 滚动控制器
  late ScrollController _scrollController;

  // 用户互动状态
  bool _isLiked = false;
  bool _isUnliked = false;
  bool _isFavorited = false;

  // 实时数据
  int _likesCount = 0;
  int _unlikesCount = 0;
  int _commentsCount = 0;

  // 键盘状态
  bool _isKeyboardVisible = false;

  // 轮播图高度管理器
  late CarouselHeightManager _carouselHeightManager;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _scrollController = ScrollController();

    _photoPageController = PageController();
    _carouselHeightManager = CarouselHeightManager();

    _loadSpotData();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _autoPlayTimer?.cancel();
    _scrollController.dispose();
    _photoPageController.dispose();
    _currentPhotoIndex.dispose();
    _carouselHeightManager.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        // 应用进入后台时暂停自动播放
        _stopAutoPlay();
        break;
      case AppLifecycleState.resumed:
        // 应用恢复时重新开始自动播放
        if (_mediaItems.length > 1 && !_userInteracted) {
          _startAutoPlay();
        }
        break;
      case AppLifecycleState.hidden:
        _stopAutoPlay();
        break;
    }
  }

  /// 加载钓点数据
  Future<void> _loadSpotData() async {
    await Future.wait([
      _loadSpotPhotos(),
      _loadUserInteractions(),
      _loadSpotStatistics(),
    ]);
  }

  /// 加载钓点照片
  Future<void> _loadSpotPhotos() async {
    try {
      debugPrint('🔍 [钓点详情] 开始加载钓点照片，钓点ID: ${widget.spot.id}');

      // 从PocketBase直接获取钓点照片
      final pb = PocketBaseConfig.instance.client;
      final records = await pb
          .collection('spot_photos')
          .getFullList(
            filter: 'spot_id = "${widget.spot.id}"',
            sort: 'sort_order,created',
          );

      if (mounted) {
        setState(() {
          _photos =
              records
                  .map((record) => SpotPhoto.fromJson(record.toJson()))
                  .toList();

          // 转换为媒体项目列表
          _mediaItems =
              _photos
                  .map((photo) => SpotMediaItem.fromSpotPhoto(photo))
                  .toList();

          _isLoadingPhotos = false;
        });
        debugPrint('✅ [钓点详情] 媒体加载完成，共 ${_mediaItems.length} 项');

        // 调试：打印每个媒体项目的类型
        for (int i = 0; i < _mediaItems.length; i++) {
          final item = _mediaItems[i];
          debugPrint(
            '📋 [媒体列表] [$i] ${item.filename} - 类型: ${item.type.name}, MIME: ${item.mimeType}',
          );
        }

        // 如果有多个媒体，启动自动播放
        if (_mediaItems.length > 1) {
          _startAutoPlay();
        }
      }
    } catch (e) {
      debugPrint('❌ [钓点详情] 加载钓点照片失败: $e');
      if (mounted) {
        setState(() {
          _isLoadingPhotos = false;
        });
      }
    }
  }

  /// 加载用户互动状态
  Future<void> _loadUserInteractions() async {
    try {
      debugPrint('🔍 [互动状态] 开始加载用户互动状态');

      // 确保服务已加载用户互动记录
      await Services.spotInteraction.loadUserInteractions();

      if (mounted) {
        setState(() {
          _isLiked = Services.spotInteraction.isLiked(widget.spot.id);
          _isUnliked = Services.spotInteraction.isUnliked(widget.spot.id);
          _isFavorited = Services.userFavorite.isFavorited(widget.spot.id);
        });

        debugPrint('✅ [互动状态] 状态加载完成: 点赞=$_isLiked, 倒赞=$_isUnliked');
      }
    } catch (e) {
      debugPrint('❌ [互动状态] 加载用户互动状态失败: $e');
      if (mounted) {
        setState(() {
          _isLiked = false;
          _isUnliked = false;
          _isFavorited = false;
        });
      }
    }
  }

  /// 加载钓点统计数据
  Future<void> _loadSpotStatistics() async {
    try {
      debugPrint('🔍 [统计数据] 开始加载钓点统计数据');

      // 获取点赞数量
      final likesCount = await Services.social.getSpotLikesCount(
        widget.spot.id,
      );

      // 获取评论数量
      final commentsCount = await Services.spotComment.getCommentCount(
        widget.spot.id,
      );

      if (mounted) {
        setState(() {
          _likesCount = likesCount;
          _unlikesCount = widget.spot.unlikes; // 暂时使用模型中的数据
          _commentsCount = commentsCount;
        });

        debugPrint(
          '✅ [统计数据] 数据加载完成: 点赞=$_likesCount, 倒赞=$_unlikesCount, 评论=$_commentsCount',
        );
      }
    } catch (e) {
      debugPrint('❌ [统计数据] 加载统计数据失败: $e');
      // 使用默认值或钓点模型中的数据作为后备
      if (mounted) {
        setState(() {
          _likesCount = widget.spot.likes;
          _unlikesCount = widget.spot.unlikes;
          _commentsCount = widget.spot.comments.length;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 监听键盘状态
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    // 键盘状态变化时的处理
    WidgetsBinding.instance.addPostFrameCallback((_) {
      debugPrint('⌨️ [键盘状态] 检查键盘状态变化');
      debugPrint('⌨️ [键盘状态] 键盘高度: $keyboardHeight');
      debugPrint('⌨️ [键盘状态] 当前状态: $_isKeyboardVisible -> $isKeyboardVisible');

      if (_isKeyboardVisible != isKeyboardVisible) {
        debugPrint('⌨️ [键盘状态] 键盘状态发生变化，更新状态');
        setState(() {
          _isKeyboardVisible = isKeyboardVisible;
        });

        // 更新轮播图高度管理器的键盘状态
        debugPrint('⌨️ [键盘状态] 准备更新轮播图高度管理器');
        _carouselHeightManager.updateKeyboardState(isKeyboardVisible);

        // 键盘收起时，滚动到顶部显示图片轮播
        if (!isKeyboardVisible && _scrollController.hasClients) {
          debugPrint('⌨️ [键盘状态] 键盘收起，滚动到顶部');
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      } else {
        debugPrint('⌨️ [键盘状态] 键盘状态未变化');
      }
    });

    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: true, // 启用键盘避让
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // 自定义AppBar和照片展示区域
          _buildSliverAppBar(),

          // 钓点信息内容
          SliverToBoxAdapter(child: _buildSpotContent()),
        ],
      ),

      // 底部操作栏
      bottomNavigationBar: _buildBottomActionBar(),
    );
  }

  /// 构建SliverAppBar和照片展示区域
  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: _carouselHeightManager.currentHeight,
      floating: false,
      pinned: true,
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      elevation: 0,
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.95),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
          color: Colors.black87,
          iconSize: 20,
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.95),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            icon: Icon(
              _isFavorited ? Icons.favorite : Icons.favorite_border,
              color: _isFavorited ? Colors.red : Colors.black87,
            ),
            onPressed: _handleFavorite,
            iconSize: 20,
          ),
        ),
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.95),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            icon: const Icon(Icons.share),
            onPressed: _handleShare,
            color: Colors.black87,
            iconSize: 20,
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: AnimatedBuilder(
          animation: _carouselHeightManager,
          builder: (context, child) {
            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              height: _carouselHeightManager.currentHeight,
              child: _buildPhotoCarousel(),
            );
          },
        ),
      ),
    );
  }

  /// 构建媒体轮播
  Widget _buildPhotoCarousel() {
    if (_isLoadingPhotos) {
      return Container(
        color: Colors.grey.shade200,
        child: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_mediaItems.isEmpty) {
      return Container(
        color: Colors.grey.shade200,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.photo_library_outlined, size: 48, color: Colors.grey),
              SizedBox(height: 8),
              Text(
                '暂无媒体内容',
                style: TextStyle(color: Colors.grey, fontSize: 16),
              ),
            ],
          ),
        ),
      );
    }

    return MediaCarousel(
      mediaItems: _mediaItems,
      initialIndex: _currentPhotoIndex.value,
      height: 300,
      autoPlayVideo: true,
      showControls: true,
      enableViewer: true,
      onPageChanged: (index) {
        _onPhotoPageChanged(index);
        _onUserSwipe();
      },
    );
  }

  /// 构建钓点内容（新的单一滚动布局）
  Widget _buildSpotContent() {
    return Container(
      color: Colors.grey.shade50,
      child: Column(
        children: [
          // 钓点标题和基本信息
          _buildSpotHeader(),

          // 基本信息卡片
          _buildBasicInfoCard(),

          // 照片展示区域
          _buildPhotosSection(),

          // 评论区域
          _buildCommentsSection(),

          // 底部间距
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  /// 构建钓点标题区域
  Widget _buildSpotHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 0.5),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 钓点名称和实地发布徽标
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.spot.name,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ),
              OnSiteBadge(isOnSite: widget.spot.isOnSite),
            ],
          ),
          const SizedBox(height: 8),

          // 创建者和发布时间信息行
          Row(
            children: [
              Icon(Icons.person_outline, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                widget.spot.userName ?? '未知',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 12),
              Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                _formatDateTime(widget.spot.created),
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // 可见性和互动统计信息行
          Row(
            children: [
              Expanded(child: VisibilityInfo(spot: widget.spot)),
              Text(
                '$_likesCount 点赞 · $_commentsCount 评论',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建地图坐标按钮
  Widget _buildMapCoordinateButton() {
    return FutureBuilder<bool>(
      future: _checkMapPermission(),
      builder: (context, snapshot) {
        final canViewMap = snapshot.data ?? false;
        final isLoading = snapshot.connectionState == ConnectionState.waiting;

        return GestureDetector(
          onTap:
              isLoading
                  ? null
                  : (canViewMap
                      ? _handleMapNavigation
                      : _handleMapPermissionDenied),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isLoading)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                else
                  FaIcon(
                    FontAwesomeIcons.mapLocationDot,
                    size: 20,
                    color: canViewMap ? Colors.blue : Colors.grey,
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 检查地图访问权限
  Future<bool> _checkMapPermission() async {
    try {
      final currentUser = Services.auth.currentUser;

      // 未登录用户只能查看公开钓点
      if (currentUser == null) {
        return widget.spot.visibility == SpotVisibility.public;
      }

      // 钓点创建者总是可以查看
      if (currentUser.id == widget.spot.userId) {
        return true;
      }

      // 使用SpotVisibilityService进行详细权限检查
      return await Services.spotVisibility.canUserViewSpot(
        currentUser.id,
        widget.spot,
      );
    } catch (e) {
      debugPrint('❌ [地图权限] 检查权限失败: $e');
      return false;
    }
  }

  /// 处理地图导航 ⭐ 修改为使用统一导航服务
  void _handleMapNavigation() async {
    try {
      debugPrint('🗺️ [钓点详情] 开始使用统一导航服务');
      debugPrint('🗺️ [钓点详情] 钓点ID: ${widget.spot.id}');
      debugPrint('🗺️ [钓点详情] 钓点名称: ${widget.spot.name}');
      debugPrint('🗺️ [钓点详情] 钓点位置: ${widget.spot.locationLatLng}');

      // 使用统一导航服务，强制跳转到地图页面
      final success = await Services.mapNavigation.navigateToSpot(
        spotLocation: widget.spot.locationLatLng,
        context: context,
        forceNavigateToMapPage: true, // 从钓点详情页面强制跳转到地图页面
      );

      if (!success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('导航到地图失败，请重试'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ [钓点详情] 导航失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('导航到地图失败，请重试'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 处理地图权限被拒绝
  void _handleMapPermissionDenied() {
    final currentUser = Services.auth.currentUser;
    String message;

    if (currentUser == null) {
      message = '请先登录以查看地图位置';
    } else {
      // 根据可见性条件给出具体提示
      switch (widget.spot.visibility) {
        case SpotVisibility.private:
          message = '这是私有钓点，只有创建者可以查看地图位置';
          break;
        case SpotVisibility.friendsOnly:
          message = '需要关注创建者才能查看地图位置';
          break;
        case SpotVisibility.conditional:
          if (widget.spot.visibilityConditions != null) {
            final conditions = widget.spot.visibilityConditions!;
            final conditionType = conditions['type'] as String?;
            switch (conditionType) {
              case 'POINTS_DONATED':
                final minPoints = conditions['minPoints'] as int? ?? 0;
                message = '需要向作者赠送 $minPoints 积分才能查看地图位置';
                break;
              case 'LEVEL_REQUIRED':
                final minLevel = conditions['minLevel'] as int? ?? 1;
                message = '需要达到 $minLevel 级才能查看地图位置';
                break;
              case 'PAY_TO_VIEW':
                final price = conditions['price'] as int? ?? 0;
                message = '需要支付 $price 积分才能查看地图位置';
                break;
              default:
                message = '不满足查看条件，无法查看地图位置';
            }
          } else {
            message = '不满足查看条件，无法查看地图位置';
          }
          break;
        default:
          message = '无法查看地图位置';
      }
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.orange[600],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// 滚动到评论区域
  void _scrollToCommentsSection() {
    if (_scrollController.hasClients) {
      // 计算大概的滚动位置（基于各个区域的估计高度）
      const headerHeight = 100.0; // 标题区域高度
      const basicInfoHeight = 200.0; // 基本信息卡片高度
      const locationInfoHeight = 150.0; // 位置信息卡片高度
      const photosHeight = 300.0; // 照片区域高度（估计）

      final targetOffset =
          headerHeight + basicInfoHeight + locationInfoHeight + photosHeight;

      _scrollController.animateTo(
        targetOffset,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 构建基本信息卡片
  Widget _buildBasicInfoCard() {
    return SpotInfoCard(
      title: '基本信息',
      icon: Icons.info_outline,
      iconColor: Colors.blue,
      children: [
        InfoRow(
          label: '钓点类型',
          value: _getSpotTypeName(widget.spot.spotType),
          emoji: '📍',
        ),
        InfoRow(
          label: '鱼类类型',
          value: _getFishTypeName(widget.spot.fishTypes),
          emoji: '🐟',
          showDivider: true, // 总是显示分隔线，因为我们要添加描述字段
        ),
        // 总是显示描述字段，如果为空则显示默认文本
        InfoRow(
          label: '描述',
          value:
              widget.spot.description.isNotEmpty
                  ? widget.spot.description
                  : '暂无描述',
          emoji: '📝',
        ),
      ],
    );
  }

  /// 构建照片展示区域
  Widget _buildPhotosSection() {
    if (_isLoadingPhotos) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade200, width: 0.5),
          ),
        ),
        child: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_photos.isEmpty) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade200, width: 0.5),
          ),
        ),
        child: const Center(
          child: Column(
            children: [
              Icon(Icons.photo_library_outlined, size: 48, color: Colors.grey),
              SizedBox(height: 16),
              Text('暂无照片', style: TextStyle(color: Colors.grey, fontSize: 16)),
            ],
          ),
        ),
      );
    }

    return SpotInfoCard(
      title: '照片 (${_photos.length})',
      icon: Icons.photo_library,
      iconColor: Colors.green,
      children: [
        PhotoGallery(
          photos:
              _photos
                  .map(
                    (photo) => PhotoItem(
                      id: photo.id,
                      url: photo.url,
                      thumbnailUrl: photo.thumbnailUrl,
                      description: photo.description,
                    ),
                  )
                  .toList(),
          config: PhotoGalleryConfig.grid(
            crossAxisCount: 2,
            enableViewer: true,
          ),
        ),
      ],
    );
  }

  /// 构建评论区域
  Widget _buildCommentsSection() {
    return SpotInfoCard(
      title: '评论 ($_commentsCount)',
      icon: Icons.comment,
      iconColor: Colors.orange,
      showDivider: false,
      children: [
        SizedBox(
          height: 400, // 限制评论区域高度
          child: CommentSystem(
            targetId: widget.spot.id,
            type: CommentType.spot,
            title: null, // 不显示标题，因为已经在卡片标题中显示
          ),
        ),
      ],
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey.shade200, width: 1)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 左侧按钮组：点赞、倒赞、评论
            Row(
              children: [
                // 点赞（只显示图标）
                _buildSimpleIconButton(
                  icon: FontAwesomeIcons.thumbsUp,
                  color: Colors.blue,
                  isActive: _isLiked,
                  onTap: _handleLike,
                ),

                // 不喜欢（只显示图标）
                _buildSimpleIconButton(
                  icon: FontAwesomeIcons.thumbsDown,
                  color: Colors.red,
                  isActive: _isUnliked,
                  onTap: _handleDislike,
                ),

                // 评论（只显示图标）
                _buildSimpleIconButton(
                  icon: FontAwesomeIcons.comment,
                  color: Colors.green,
                  isActive: false,
                  onTap: () {
                    // 滚动到评论区域
                    _scrollToCommentsSection();
                  },
                ),
              ],
            ),

            // 右侧按钮组：地图、导航
            Row(
              children: [
                // 地图坐标（无文字）
                _buildMapCoordinateButton(),

                // 导航（无文字，蓝色）
                _buildSimpleIconButton(
                  icon: FontAwesomeIcons.locationArrow,
                  color: Colors.blue,
                  onTap: _handleNavigation,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建简单图标按钮（只有图标，无文字）
  Widget _buildSimpleIconButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    bool isActive = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive ? color.withValues(alpha: 0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: FaIcon(
          icon,
          size: 20,
          color: isActive ? color : Colors.grey.shade600,
        ),
      ),
    );
  }

  /// 处理点赞
  Future<void> _handleLike() async {
    try {
      debugPrint('🔍 [点赞] 开始处理点赞操作');

      bool success = false;

      if (_isLiked) {
        // 如果已经点赞，则取消点赞
        success = await Services.spotInteraction.cancelLike(widget.spot.id);
        if (success && mounted) {
          setState(() {
            _isLiked = false;
            _likesCount = (_likesCount - 1).clamp(0, double.infinity).toInt();
          });
          debugPrint('✅ [点赞] 取消点赞成功');
        }
      } else {
        // 如果未点赞，则点赞
        success = await Services.spotInteraction.likeSpot(widget.spot.id);
        if (success && mounted) {
          setState(() {
            _isLiked = true;
            // 点赞时自动取消倒赞
            if (_isUnliked) {
              _isUnliked = false;
              _unlikesCount =
                  (_unlikesCount - 1).clamp(0, double.infinity).toInt();
            }
            _likesCount = _likesCount + 1;
          });
          debugPrint('✅ [点赞] 点赞成功');
        }
      }

      if (!success) {
        debugPrint('❌ [点赞] 点赞操作失败');
        if (mounted) {
          SnackBarService.showError(context, '操作失败，请稍后重试');
        }
      }
    } catch (e) {
      debugPrint('❌ [点赞] 点赞操作异常: $e');
      if (mounted) {
        SnackBarService.showError(context, '操作失败，请稍后重试');
      }
    }
  }

  /// 处理倒赞
  Future<void> _handleDislike() async {
    try {
      debugPrint('🔍 [倒赞] 开始处理倒赞操作');

      bool success = false;

      if (_isUnliked) {
        // 如果已经倒赞，则取消倒赞
        success = await Services.spotInteraction.cancelUnlike(widget.spot.id);
        if (success && mounted) {
          setState(() {
            _isUnliked = false;
            _unlikesCount =
                (_unlikesCount - 1).clamp(0, double.infinity).toInt();
          });
          debugPrint('✅ [倒赞] 取消倒赞成功');
        }
      } else {
        // 如果未倒赞，则倒赞
        success = await Services.spotInteraction.unlikeSpot(widget.spot.id);
        if (success && mounted) {
          setState(() {
            _isUnliked = true;
            // 倒赞时自动取消点赞
            if (_isLiked) {
              _isLiked = false;
              _likesCount = (_likesCount - 1).clamp(0, double.infinity).toInt();
            }
            _unlikesCount = _unlikesCount + 1;
          });
          debugPrint('✅ [倒赞] 倒赞成功');
        }
      }

      if (!success) {
        debugPrint('❌ [倒赞] 倒赞操作失败');
        if (mounted) {
          SnackBarService.showError(context, '操作失败，请稍后重试');
        }
      }
    } catch (e) {
      debugPrint('❌ [倒赞] 倒赞操作异常: $e');
      if (mounted) {
        SnackBarService.showError(context, '操作失败，请稍后重试');
      }
    }
  }

  /// 处理收藏
  Future<void> _handleFavorite() async {
    try {
      debugPrint('🔖 [收藏] 开始处理收藏操作');

      // 检查用户登录状态
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ [收藏] 用户未登录');
        if (mounted) {
          SnackBarService.showError(context, '用户未登录，无法添加收藏');
        }
        return;
      }

      final success = await Services.userFavorite.toggleFavorite(
        widget.spot.id,
      );

      if (success && mounted) {
        setState(() {
          _isFavorited = Services.userFavorite.isFavorited(widget.spot.id);
        });

        // 显示成功提示
        SnackBarService.showSuccess(context, _isFavorited ? '已添加到收藏' : '已取消收藏');

        debugPrint('✅ [收藏] 收藏操作成功: ${_isFavorited ? "已收藏" : "已取消"}');
      } else {
        debugPrint('❌ [收藏] 收藏操作失败');
        if (mounted) {
          SnackBarService.showError(context, '操作失败，请稍后重试');
        }
      }
    } catch (e) {
      debugPrint('❌ [收藏] 收藏操作异常: $e');
      if (mounted) {
        SnackBarService.showError(context, '操作失败，请稍后重试');
      }
    }
  }

  /// 处理导航
  Future<void> _handleNavigation() async {
    try {
      debugPrint('🗺️ [导航] 开始导航到钓点: ${widget.spot.name}');

      // 调用百度地图导航服务
      await BaiduMapNavigationService.navigateToSpot(widget.spot, context);
    } catch (e) {
      debugPrint('❌ [导航] 导航失败: $e');
      if (mounted) {
        SnackBarService.showError(context, '导航失败，请重试');
      }
    }
  }

  /// 处理分享
  Future<void> _handleShare() async {
    try {
      debugPrint('🔗 [分享] 开始生成分享内容');

      // 获取钓点类型和鱼类信息
      final spotTypeName = _getSpotTypeName(widget.spot.spotType);
      final fishTypeName = _getFishTypeName(widget.spot.fishTypes);

      // 使用ShareService生成分享文本
      final shareText = Services.share.generateSpotShareText(
        spotId: widget.spot.id,
        spotName: widget.spot.name,
        spotType: spotTypeName,
        fishTypes: fishTypeName,
        likesCount: _likesCount,
        commentsCount: _commentsCount,
      );

      // 复制到剪贴板
      final success = await Services.share.copyToClipboard(shareText);

      // 显示结果提示
      if (mounted) {
        if (success) {
          SnackBarService.showSuccess(context, '分享内容已复制到剪贴板！\n快去分享给朋友吧 🎣');
        } else {
          SnackBarService.showError(context, '分享失败，请稍后重试');
        }
      }

      debugPrint('✅ [分享] 分享处理完成');
    } catch (e) {
      debugPrint('❌ [分享] 分享失败: $e');
      if (mounted) {
        SnackBarService.showError(context, '分享失败，请稍后重试');
      }
    }
  }

  /// 开始自动播放
  void _startAutoPlay() {
    if (_mediaItems.length <= 1) return;

    _autoPlayTimer?.cancel();
    _autoPlayTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (!_userInteracted && _photoPageController.hasClients && mounted) {
        final nextIndex = (_currentPhotoIndex.value + 1) % _mediaItems.length;
        _photoPageController.animateToPage(
          nextIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        _currentPhotoIndex.value = nextIndex;
      }
    });
  }

  /// 停止自动播放
  void _stopAutoPlay() {
    _autoPlayTimer?.cancel();
  }

  /// 用户手动滑动时的处理
  void _onUserSwipe() {
    _userInteracted = true;
    _stopAutoPlay();

    // 10秒后恢复自动播放
    Timer(const Duration(seconds: 10), () {
      if (mounted) {
        _userInteracted = false;
        _startAutoPlay();
      }
    });
  }

  /// 页面变化时的处理
  void _onPhotoPageChanged(int index) {
    if (mounted) {
      _currentPhotoIndex.value = index;
    }
  }

  /// 获取钓点类型名称
  String _getSpotTypeName(String? spotType) {
    if (spotType == null || spotType.isEmpty) return '未知';

    switch (spotType) {
      case 'river':
        return '河流';
      case 'lake':
        return '湖泊';
      case 'sea':
        return '海洋';
      case 'pond':
        return '池塘';
      case 'reservoir':
        return '水库';
      default:
        return spotType;
    }
  }

  /// 获取鱼类类型名称
  String _getFishTypeName(String? fishTypes) {
    if (fishTypes == null || fishTypes.isEmpty) return '未知';

    // 如果是单个字符串，尝试按逗号分割
    final typeList = fishTypes.split(',').map((e) => e.trim()).toList();

    final typeNames =
        typeList.map((type) {
          switch (type) {
            case 'carp':
              return '鲤鱼';
            case 'crucian':
              return '鲫鱼';
            case 'grass_carp':
              return '草鱼';
            case 'silver_carp':
              return '鲢鱼';
            case 'bighead_carp':
              return '鳙鱼';
            case 'bass':
              return '鲈鱼';
            case 'catfish':
              return '鲶鱼';
            case 'snakehead':
              return '黑鱼';
            default:
              return type;
          }
        }).toList();

    return typeNames.join('、');
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return '未知';

    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}
