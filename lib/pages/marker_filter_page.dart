import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../config/filter_config.dart';
import '../models/unified_marker.dart';
import '../services/filter_config_service.dart';
import '../widgets/cache_status_indicator.dart';

/// 标记过滤配置页面
///
/// 提供用户友好的过滤选项界面，支持：
/// - 基础过滤条件设置
/// - 社交过滤条件设置
/// - 个人关联过滤设置
/// - 类型过滤设置
/// - 显示设置
/// - 优先级权重调整
class MarkerFilterPage extends StatefulWidget {
  /// 当前过滤配置
  final FilterConfig? initialConfig;

  /// 配置变更回调
  final Function(FilterConfig)? onConfigChanged;

  const MarkerFilterPage({super.key, this.initialConfig, this.onConfigChanged});

  @override
  State<MarkerFilterPage> createState() => _MarkerFilterPageState();
}

class _MarkerFilterPageState extends State<MarkerFilterPage>
    with SingleTickerProviderStateMixin {
  late FilterConfig _currentConfig;
  late FilterConfig _originalConfig;
  late TabController _tabController;
  late FilterConfigService _configService;

  bool _hasUnsavedChanges = false;

  @override
  void initState() {
    super.initState();

    // 初始化配置服务
    _configService = FilterConfigService.instance;

    // 初始化配置
    _currentConfig = widget.initialConfig ?? _configService.currentConfig;
    _originalConfig = _currentConfig;

    // 初始化标签页控制器
    _tabController = TabController(length: 6, vsync: this);

    // 监听配置变更
    _configService.addListener(_onConfigServiceChanged);
  }

  @override
  void dispose() {
    _configService.removeListener(_onConfigServiceChanged);
    _tabController.dispose();
    super.dispose();
  }

  /// 配置服务变更回调
  void _onConfigServiceChanged(FilterConfig config) {
    // 如果当前页面的配置与服务中的配置不同，更新页面配置
    if (mounted && _currentConfig != config) {
      setState(() {
        _currentConfig = config;
        _updateUnsavedChangesState();
      });
    }
  }

  /// 更新未保存变更状态
  void _updateUnsavedChangesState() {
    final hasChanges = _currentConfig != _originalConfig;
    if (_hasUnsavedChanges != hasChanges) {
      setState(() {
        _hasUnsavedChanges = hasChanges;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('过滤设置'),
      centerTitle: true,
      elevation: 0,
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
      actions: [
        // 保存按钮
        if (_hasUnsavedChanges)
          IconButton(
            onPressed: _saveConfig,
            icon: const FaIcon(FontAwesomeIcons.floppyDisk),
            tooltip: '保存配置',
          ),
        // 重置按钮
        IconButton(
          onPressed: _resetToDefault,
          icon: const FaIcon(FontAwesomeIcons.arrowRotateLeft),
          tooltip: '重置为默认',
        ),
        // 更多选项菜单
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          icon: const FaIcon(FontAwesomeIcons.ellipsisVertical),
          tooltip: '更多选项',
          itemBuilder:
              (context) => [
                // 预设配置
                const PopupMenuItem(
                  value: 'preset_default',
                  child: ListTile(
                    leading: FaIcon(FontAwesomeIcons.gear, size: 16),
                    title: Text('默认配置'),
                    subtitle: Text('平衡的过滤设置'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'preset_relaxed',
                  child: ListTile(
                    leading: FaIcon(FontAwesomeIcons.eye, size: 16),
                    title: Text('宽松配置'),
                    subtitle: Text('显示更多内容'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'preset_strict',
                  child: ListTile(
                    leading: FaIcon(FontAwesomeIcons.filter, size: 16),
                    title: Text('严格配置'),
                    subtitle: Text('只显示高质量内容'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'preset_personalized',
                  child: ListTile(
                    leading: FaIcon(FontAwesomeIcons.user, size: 16),
                    title: Text('个人化配置'),
                    subtitle: Text('优先显示个人相关'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuDivider(),
                // 配置管理
                const PopupMenuItem(
                  value: 'save_config',
                  child: ListTile(
                    leading: FaIcon(FontAwesomeIcons.floppyDisk, size: 16),
                    title: Text('保存配置'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'load_config',
                  child: ListTile(
                    leading: FaIcon(FontAwesomeIcons.folderOpen, size: 16),
                    title: Text('加载配置'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        tabs: const [
          Tab(icon: FaIcon(FontAwesomeIcons.sliders, size: 16), text: '基础'),
          Tab(icon: FaIcon(FontAwesomeIcons.heart, size: 16), text: '社交'),
          Tab(icon: FaIcon(FontAwesomeIcons.user, size: 16), text: '个人'),
          Tab(icon: FaIcon(FontAwesomeIcons.tags, size: 16), text: '类型'),
          Tab(icon: FaIcon(FontAwesomeIcons.display, size: 16), text: '显示'),
          Tab(icon: FaIcon(FontAwesomeIcons.chartLine, size: 16), text: '优先级'),
        ],
      ),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildBasicTab(),
        _buildSocialTab(),
        _buildPersonalTab(),
        _buildTypeTab(),
        _buildDisplayTab(),
        _buildPriorityTab(),
      ],
    );
  }

  /// 构建底部栏
  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(color: Theme.of(context).dividerColor, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // 配置摘要
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('当前配置', style: Theme.of(context).textTheme.labelSmall),
                const SizedBox(height: 4),
                Text(
                  _currentConfig.getSummary(),
                  style: Theme.of(context).textTheme.bodySmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          // 应用按钮 ⭐ 增强无障碍访问
          Semantics(
            label: '应用过滤配置',
            hint: _currentConfig.isValid ? '点击应用当前的过滤配置设置' : '配置无效，请检查权重设置',
            child: ElevatedButton.icon(
              onPressed: _currentConfig.isValid ? _applyConfig : null,
              icon: const FaIcon(FontAwesomeIcons.check, size: 16),
              label: const Text('应用'),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建基础过滤标签页
  Widget _buildBasicTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('基础过滤条件', '控制标记的基本显示条件'),

        // 操作引导提示 ⭐ 新增
        OperationGuideTooltip(
          guideText: '💡 提示：开启这些选项可以过滤掉低质量的标记，提升浏览体验',
          showGuide: _currentConfig.isDefault,
          child: const SizedBox(height: 16),
        ),

        // 过期标记
        _buildSwitchTile(
          title: '显示过期标记',
          subtitle: '包含已过期的活动和标记',
          value: _currentConfig.showExpired,
          onChanged:
              (value) =>
                  _updateConfig(_currentConfig.copyWith(showExpired: value)),
          icon: FontAwesomeIcons.clock,
        ),

        const Divider(),

        // 钓点相关条件
        _buildSectionHeader('钓点质量要求', '设置钓点的质量标准'),
        const SizedBox(height: 8),

        _buildSwitchTile(
          title: '要求实地标签',
          subtitle: '只显示有实地验证的钓点',
          value: _currentConfig.requireOnSite,
          onChanged:
              (value) =>
                  _updateConfig(_currentConfig.copyWith(requireOnSite: value)),
          icon: FontAwesomeIcons.locationDot,
        ),

        _buildSwitchTile(
          title: '要求实拍标签',
          subtitle: '只显示有实拍照片的钓点',
          value: _currentConfig.requireRealPhotos,
          onChanged:
              (value) => _updateConfig(
                _currentConfig.copyWith(requireRealPhotos: value),
              ),
          icon: FontAwesomeIcons.camera,
        ),

        _buildSwitchTile(
          title: '要求有照片',
          subtitle: '只显示包含照片的钓点',
          value: _currentConfig.requirePhotos,
          onChanged:
              (value) =>
                  _updateConfig(_currentConfig.copyWith(requirePhotos: value)),
          icon: FontAwesomeIcons.image,
        ),
      ],
    );
  }

  /// 构建社交过滤标签页
  Widget _buildSocialTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('社交过滤条件', '基于用户互动的过滤设置'),
        const SizedBox(height: 16),

        _buildSwitchTile(
          title: '要求正面评价',
          subtitle: '只显示点赞数大于不喜欢数的内容',
          value: _currentConfig.requirePositiveLikes,
          onChanged:
              (value) => _updateConfig(
                _currentConfig.copyWith(requirePositiveLikes: value),
              ),
          icon: FontAwesomeIcons.thumbsUp,
        ),

        _buildSwitchTile(
          title: '只显示收藏',
          subtitle: '只显示我收藏的标记',
          value: _currentConfig.onlyFavorites,
          onChanged:
              (value) =>
                  _updateConfig(_currentConfig.copyWith(onlyFavorites: value)),
          icon: FontAwesomeIcons.star,
        ),

        const Divider(),

        // 最小点赞数滑块
        _buildSectionHeader('点赞数要求', '设置最小点赞数阈值'),
        const SizedBox(height: 8),

        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const FaIcon(FontAwesomeIcons.heart, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      '最小点赞数: ${_currentConfig.minLikesCount}',
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Slider(
                  value: _currentConfig.minLikesCount.toDouble(),
                  min: 0,
                  max: 50,
                  divisions: 10,
                  label: _currentConfig.minLikesCount.toString(),
                  onChanged:
                      (value) => _updateConfig(
                        _currentConfig.copyWith(minLikesCount: value.round()),
                      ),
                ),
                Text(
                  '设置为0表示不限制点赞数',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建个人关联标签页
  Widget _buildPersonalTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('个人关联过滤', '显示与我相关的内容'),
        const SizedBox(height: 16),

        _buildSwitchTile(
          title: '只显示我的钓点',
          subtitle: '只显示我发布的钓点',
          value: _currentConfig.onlyMySpots,
          onChanged:
              (value) =>
                  _updateConfig(_currentConfig.copyWith(onlyMySpots: value)),
          icon: FontAwesomeIcons.locationDot,
        ),

        _buildSwitchTile(
          title: '只显示我的活动',
          subtitle: '只显示我发布的活动',
          value: _currentConfig.onlyMyActivities,
          onChanged:
              (value) => _updateConfig(
                _currentConfig.copyWith(onlyMyActivities: value),
              ),
          icon: FontAwesomeIcons.calendar,
        ),

        _buildSwitchTile(
          title: '只显示已加入活动',
          subtitle: '只显示我参与的活动',
          value: _currentConfig.onlyJoinedActivities,
          onChanged:
              (value) => _updateConfig(
                _currentConfig.copyWith(onlyJoinedActivities: value),
              ),
          icon: FontAwesomeIcons.userGroup,
        ),
      ],
    );
  }

  /// 构建类型过滤标签页
  Widget _buildTypeTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('标记类型', '选择要显示的标记类型'),
        const SizedBox(height: 16),

        // 标记类型选择
        Card(
          child: Column(
            children: [
              CheckboxListTile(
                title: const Text('钓点'),
                subtitle: const Text('显示钓点标记'),
                secondary: const FaIcon(FontAwesomeIcons.locationDot),
                value: _currentConfig.enabledTypes.contains(MarkerType.spot),
                onChanged:
                    (value) =>
                        _toggleMarkerType(MarkerType.spot, value ?? false),
              ),
              const Divider(height: 1),
              CheckboxListTile(
                title: const Text('活动'),
                subtitle: const Text('显示活动标记'),
                secondary: const FaIcon(FontAwesomeIcons.calendar),
                value: _currentConfig.enabledTypes.contains(
                  MarkerType.activity,
                ),
                onChanged:
                    (value) =>
                        _toggleMarkerType(MarkerType.activity, value ?? false),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // 钓点类型选择
        if (_currentConfig.enabledTypes.contains(MarkerType.spot)) ...[
          _buildSectionHeader('钓点类型', '选择要显示的钓点类型'),
          const SizedBox(height: 8),
          _buildSpotTypeSelection(),
          const SizedBox(height: 16),
        ],

        // 活动类型选择
        if (_currentConfig.enabledTypes.contains(MarkerType.activity)) ...[
          _buildSectionHeader('活动类型', '选择要显示的活动类型'),
          const SizedBox(height: 8),
          _buildActivityTypeSelection(),
        ],
      ],
    );
  }

  /// 构建显示设置标签页
  Widget _buildDisplayTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('显示设置', '控制标记的显示数量和排序'),
        const SizedBox(height: 16),

        // 最大显示数量
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const FaIcon(FontAwesomeIcons.eye, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      '最大显示数量: ${_currentConfig.maxDisplayCount}',
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Slider(
                  value: _currentConfig.maxDisplayCount.toDouble(),
                  min: 50,
                  max: 500,
                  divisions: 9,
                  label: _currentConfig.maxDisplayCount.toString(),
                  onChanged:
                      (value) => _updateConfig(
                        _currentConfig.copyWith(maxDisplayCount: value.round()),
                      ),
                ),
                Text(
                  '较少的数量可以提高加载速度',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        _buildSwitchTile(
          title: '启用距离排序',
          subtitle: '根据距离对标记进行排序',
          value: _currentConfig.enableDistanceSorting,
          onChanged:
              (value) => _updateConfig(
                _currentConfig.copyWith(enableDistanceSorting: value),
              ),
          icon: FontAwesomeIcons.route,
        ),
      ],
    );
  }

  /// 构建优先级权重标签页
  Widget _buildPriorityTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('优先级权重', '调整不同因素的重要性'),

        // 权重说明卡片 ⭐ 新增
        Card(
          color: Theme.of(context).colorScheme.primaryContainer,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    FaIcon(
                      FontAwesomeIcons.circleInfo,
                      color: Theme.of(context).colorScheme.primary,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '权重调整说明',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '• 权重总和必须等于100%\n'
                  '• 个人关联度：影响与你相关的内容排序\n'
                  '• 内容质量：影响高质量内容的排序\n'
                  '• 社交热度：影响热门内容的排序\n'
                  '• 时间新鲜度：影响最新内容的排序',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // 权重滑块
        _buildWeightSlider(
          title: '个人关联度',
          subtitle: '我的内容、收藏、参与的活动',
          icon: FontAwesomeIcons.user,
          value: _currentConfig.personalWeight,
          onChanged: (value) => _updateWeights(personalWeight: value),
        ),

        _buildWeightSlider(
          title: '内容质量',
          subtitle: '实地、实拍、照片等质量指标',
          icon: FontAwesomeIcons.medal,
          value: _currentConfig.qualityWeight,
          onChanged: (value) => _updateWeights(qualityWeight: value),
        ),

        _buildWeightSlider(
          title: '社交热度',
          subtitle: '点赞数、参与度等社交指标',
          icon: FontAwesomeIcons.fire,
          value: _currentConfig.socialWeight,
          onChanged: (value) => _updateWeights(socialWeight: value),
        ),

        _buildWeightSlider(
          title: '时间新鲜度',
          subtitle: '发布时间、更新时间等时间因素',
          icon: FontAwesomeIcons.clock,
          value: _currentConfig.freshnessWeight,
          onChanged: (value) => _updateWeights(freshnessWeight: value),
        ),

        const SizedBox(height: 16),

        // 权重总和提示
        Card(
          color:
              _isWeightSumValid()
                  ? Theme.of(context).colorScheme.surfaceContainerHighest
                  : Theme.of(context).colorScheme.errorContainer,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                FaIcon(
                  _isWeightSumValid()
                      ? FontAwesomeIcons.circleCheck
                      : FontAwesomeIcons.triangleExclamation,
                  size: 16,
                  color:
                      _isWeightSumValid()
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.error,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _isWeightSumValid()
                        ? '权重配置正确 (总和: ${_getWeightSum().toStringAsFixed(2)})'
                        : '权重总和必须等于1.0 (当前: ${_getWeightSum().toStringAsFixed(2)})',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color:
                          _isWeightSumValid()
                              ? Theme.of(context).colorScheme.onSurfaceVariant
                              : Theme.of(context).colorScheme.error,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建节标题
  Widget _buildSectionHeader(String title, String subtitle) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 4),
        Text(
          subtitle,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  /// 构建开关磁贴
  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
  }) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      secondary: FaIcon(icon, size: 20),
      value: value,
      onChanged: onChanged,
    );
  }

  /// 构建权重滑块
  Widget _buildWeightSlider({
    required String title,
    required String subtitle,
    required IconData icon,
    required double value,
    required ValueChanged<double> onChanged,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                FaIcon(icon, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleSmall,
                      ),
                      Text(
                        subtitle,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                Text(
                  '${(value * 100).round()}%',
                  style: Theme.of(context).textTheme.labelLarge,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Semantics(
              label: '$title权重调整',
              hint: '当前权重${(value * 100).round()}%，拖动调整权重值',
              child: Slider(
                value: value,
                min: 0.0,
                max: 1.0,
                divisions: 20,
                onChanged: onChanged,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 更新配置
  void _updateConfig(FilterConfig newConfig) {
    setState(() {
      _currentConfig = newConfig;
      _updateUnsavedChangesState();
    });
  }

  /// 切换标记类型
  void _toggleMarkerType(MarkerType type, bool enabled) {
    final newTypes = Set<MarkerType>.from(_currentConfig.enabledTypes);
    if (enabled) {
      newTypes.add(type);
    } else {
      newTypes.remove(type);
    }

    // 确保至少有一种类型被启用
    if (newTypes.isNotEmpty) {
      _updateConfig(_currentConfig.copyWith(enabledTypes: newTypes));
    }
  }

  /// 更新权重
  void _updateWeights({
    double? personalWeight,
    double? qualityWeight,
    double? socialWeight,
    double? freshnessWeight,
  }) {
    _updateConfig(
      _currentConfig.copyWith(
        personalWeight: personalWeight,
        qualityWeight: qualityWeight,
        socialWeight: socialWeight,
        freshnessWeight: freshnessWeight,
      ),
    );
  }

  /// 获取权重总和
  double _getWeightSum() {
    return _currentConfig.personalWeight +
        _currentConfig.qualityWeight +
        _currentConfig.socialWeight +
        _currentConfig.freshnessWeight;
  }

  /// 检查权重总和是否有效
  bool _isWeightSumValid() {
    return (_getWeightSum() - 1.0).abs() <= 0.01;
  }

  /// 重置为默认配置
  void _resetToDefault() {
    _updateConfig(FilterConfig.defaultConfig());
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('已重置为默认配置')));
  }

  /// 处理菜单操作
  void _handleMenuAction(String action) async {
    switch (action) {
      case 'preset_default':
        await _applyPresetConfig('default');
        break;
      case 'preset_relaxed':
        await _applyPresetConfig('relaxed');
        break;
      case 'preset_strict':
        await _applyPresetConfig('strict');
        break;
      case 'preset_personalized':
        await _applyPresetConfig('personalized');
        break;
      case 'save_config':
        await _saveConfig();
        break;
      case 'load_config':
        await _loadConfig();
        break;
    }
  }

  /// 应用预设配置
  Future<void> _applyPresetConfig(String preset) async {
    try {
      await _configService.applyPresetConfig(preset, save: false);
      _updateConfig(_configService.currentConfig);

      String message;
      switch (preset) {
        case 'default':
          message = '已应用默认配置';
          break;
        case 'relaxed':
          message = '已应用宽松配置';
          break;
        case 'strict':
          message = '已应用严格配置';
          break;
        case 'personalized':
          message = '已应用个人化配置';
          break;
        default:
          message = '已应用预设配置';
      }

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(message)));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('应用预设配置失败: $e'), backgroundColor: Colors.red),
      );
    }
  }

  /// 保存配置
  Future<void> _saveConfig() async {
    try {
      final success = await _configService.saveConfig(_currentConfig);
      if (success) {
        setState(() {
          _originalConfig = _currentConfig;
          _updateUnsavedChangesState();
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('配置已保存')));
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('配置保存失败'), backgroundColor: Colors.red),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('配置保存失败: $e'), backgroundColor: Colors.red),
      );
    }
  }

  /// 加载配置
  Future<void> _loadConfig() async {
    try {
      final config = await _configService.loadSavedConfig();
      if (config != null) {
        _updateConfig(config);
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('配置已加载')));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('配置加载失败: $e'), backgroundColor: Colors.red),
      );
    }
  }

  /// 构建钓点类型选择
  Widget _buildSpotTypeSelection() {
    final spotTypes = ['水库', '河流', '湖泊', '池塘', '海钓', '溪流', '人工湖', '其他'];

    return Card(
      child: Column(
        children:
            spotTypes.map((type) {
              final isSelected =
                  _currentConfig.enabledSpotTypes.isEmpty ||
                  _currentConfig.enabledSpotTypes.contains(type);
              return CheckboxListTile(
                title: Text(type),
                value: isSelected,
                onChanged: (value) => _toggleSpotType(type, value ?? false),
                dense: true,
              );
            }).toList(),
      ),
    );
  }

  /// 构建活动类型选择
  Widget _buildActivityTypeSelection() {
    final activityTypes = [
      '钓鱼比赛',
      '休闲垂钓',
      '夜钓活动',
      '亲子钓鱼',
      '钓鱼培训',
      '钓友聚会',
      '野钓探索',
      '其他活动',
    ];

    return Card(
      child: Column(
        children:
            activityTypes.map((type) {
              final isSelected =
                  _currentConfig.enabledActivityTypes.isEmpty ||
                  _currentConfig.enabledActivityTypes.contains(type);
              return CheckboxListTile(
                title: Text(type),
                value: isSelected,
                onChanged: (value) => _toggleActivityType(type, value ?? false),
                dense: true,
              );
            }).toList(),
      ),
    );
  }

  /// 切换钓点类型
  void _toggleSpotType(String type, bool enabled) {
    final newTypes = Set<String>.from(_currentConfig.enabledSpotTypes);
    if (enabled) {
      newTypes.add(type);
    } else {
      newTypes.remove(type);
    }

    _updateConfig(_currentConfig.copyWith(enabledSpotTypes: newTypes));
  }

  /// 切换活动类型
  void _toggleActivityType(String type, bool enabled) {
    final newTypes = Set<String>.from(_currentConfig.enabledActivityTypes);
    if (enabled) {
      newTypes.add(type);
    } else {
      newTypes.remove(type);
    }

    _updateConfig(_currentConfig.copyWith(enabledActivityTypes: newTypes));
  }

  /// 应用配置
  void _applyConfig() {
    if (!_currentConfig.isValid) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('配置无效，请检查权重设置'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // 调用回调函数
    widget.onConfigChanged?.call(_currentConfig);

    // 返回上一页
    Navigator.of(context).pop(_currentConfig);
  }
}
