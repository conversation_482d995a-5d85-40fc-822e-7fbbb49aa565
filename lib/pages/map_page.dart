import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_map_location_marker/flutter_map_location_marker.dart';
import 'package:latlong2/latlong.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:async';
import 'dart:math' as math;
import '../services/service_locator.dart';
import '../services/map_navigation_service.dart';
import '../models/fishing_spot.dart';
import '../utils/map_coordinate_utils.dart';
import '../utils/marker_alignment_utils.dart';
import '../utils/tianditu_utils.dart';

import '../widgets/dev_menu.dart';
import '../widgets/split_screen_add_spot.dart';
import '../widgets/split_screen_add_activity.dart';
import '../widgets/photo_fishing_spot_marker.dart';
import '../widgets/activity_marker.dart' as widgets;
import '../config/filter_config.dart';
import '../services/unified_marker_manager.dart';
import '../services/filter_config_service.dart';
import '../services/cache/unified_marker_cache.dart';
import '../models/unified_marker.dart';

import 'marker_filter_page.dart';
import 'spot_detail_page.dart';
import '../widgets/optimized_search_bar.dart';
import '../widgets/snackbar.dart';
import '../widgets/marker_animation_manager.dart';
import '../widgets/incremental_loading_indicator.dart';
import '../widgets/loading_error_handler.dart';
import '../widgets/cache_status_indicator.dart';
import '../widgets/bottom_filter_sheet.dart';
import '../models/fishing_activity.dart';
import '../config/app_config.dart';

class MapPage extends StatefulWidget {
  final Function(bool)? onBottomNavigationBarVisibilityChanged;

  const MapPage({super.key, this.onBottomNavigationBarVisibilityChanged});

  @override
  State<MapPage> createState() => _MapPageState();
}

class _MapPageState extends State<MapPage>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  // 天地图密钥，需要在天地图官网申请
  final MapController mapController = MapController();
  // 地图类型：true为矢量图，false为卫星图
  bool isVectorMap = false;

  // 活动数据版本号，用于追踪数据变化
  int _activitiesVersion = 0;
  // 是否显示注记层
  bool showAnnotationLayer = true;

  // 服务 - 使用新的服务架构
  // 通过Services便捷访问器访问服务

  // 统一标记管理器 ⭐ 新增
  UnifiedMarkerManager? _markerManager;
  FilterConfigService? _configService;

  // 统一标记数据 ⭐ 新增
  FilterConfig _currentFilterConfig = FilterConfig.defaultConfig();

  // 增量加载状态 ⭐ 新增
  bool _isIncrementalLoading = false;
  int _currentLoadingStep = 1;
  double _currentStepProgress = 0.0;
  String _currentStepDescription = '';
  int? _loadedMarkersCount;
  int? _totalMarkersCount;
  String? _loadingError;

  // 网络状态和错误处理 ⭐ 新增
  NetworkStatus _networkStatus = NetworkStatus.connected;
  bool _isOfflineMode = false;
  int _retryCount = 0;
  static const int _maxRetryCount = 3;

  // 缓存状态和用户体验 ⭐ 新增
  CacheHitStatus? _lastCacheStatus;
  DateTime? _lastCacheTime;
  bool _showCacheIndicator = false;

  // 浮动过滤面板 ⭐ 新增
  bool _showFloatingFilterPanel = false;

  // 动画控制器 ⭐ 新增
  late AnimationController _markerAnimationController;
  final Map<String, AnimationController> _markerUpdateControllers = {};

  // 数据 - 保留用于兼容性
  final List<FishingSpot> _spots = [];
  final List<FishingActivity> _activities = [];
  LatLng _userLocation = const LatLng(39.9042, 116.4074); // 默认北京位置
  bool _isLoading = true;

  // 标记缓存 - 避免重复创建标记组件
  final Map<String, Widget> _spotMarkerCache = {};
  final Map<String, Widget> _activityMarkerCache = {};

  // 批量更新定时器
  Timer? _batchUpdateTimer;

  final GlobalKey _mapKey = GlobalKey();

  // 分屏添加模式状态
  bool _isSplitScreenMode = false;
  bool _isAddingActivity = false; // true为添加约钓活动，false为添加钓点
  LatLng _centerMarkerLocation = const LatLng(39.9042, 116.4074);
  String? _suggestedSpotName; // 建议的钓点名称

  // 保存进入分屏模式前的地图中心位置，用于退出时恢复
  LatLng? _originalMapCenter;

  // 定时位置更新
  Timer? _locationUpdateTimer;
  final bool _enablePeriodicLocationUpdate = true;
  static const Duration _locationUpdateInterval = Duration(seconds: 10);

  // 位置重置按钮状态
  bool _isLocationResetting = false;

  // 位置监听订阅
  StreamSubscription<LatLng>? _locationSubscription;

  // 搜索栏状态
  bool _isSearching = false;

  // 分屏模式动画控制器
  late AnimationController _splitScreenAnimationController;
  late Animation<double> _splitScreenAnimation;

  // 地图准备状态和待执行的导航请求
  bool _isMapReady = false;
  String? _pendingSpotId;
  LatLng? _pendingSpotLocation;

  // 统一导航相关 ⭐ 新增
  LatLng? _temporaryMarkerLocation;
  bool _showTemporaryMarker = false;
  MapNavigationParams? _pendingNavigationParams;

  // 搜索栏组件的全局键，用于控制搜索栏状态
  final GlobalKey<OptimizedSearchBarState> _searchBarKey =
      GlobalKey<OptimizedSearchBarState>();

  @override
  bool get wantKeepAlive => true; // 保持MapPage状态，避免应用恢复时重建

  @override
  void initState() {
    super.initState();

    // 注册统一导航回调 ⭐ 新增
    MapNavigationService.instance.registerMapNavigationCallback(
      _handleUnifiedNavigation,
    );

    // 初始化分屏动画控制器
    _splitScreenAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _splitScreenAnimation = CurvedAnimation(
      parent: _splitScreenAnimationController,
      curve: Curves.easeOutCubic,
    );

    // 初始化标记动画控制器 ⭐ 新增
    _markerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // 检查位置服务
    _debugLocationServices();

    // 确保位置服务已启动
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _ensureLocationServiceStarted();
    });

    // 确保初始状态下导航栏是显示的
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_isSplitScreenMode) {
        widget.onBottomNavigationBarVisibilityChanged?.call(true);
      }
    });

    // 动画初始化已移除，如需要可重新添加

    // 初始化统一标记管理器 ⭐ 新增
    debugPrint('🔧 [MapPage] 开始调用统一标记管理器初始化');
    _initializeUnifiedMarkerManager();

    _initializeData();

    // 监听地图移动，加载范围内的标记 ⭐ 更新
    mapController.mapEventStream.listen((event) {
      if (event is MapEventMoveEnd) {
        // 优先使用统一管理器，如果未初始化则使用传统方法
        if (_markerManager != null) {
          _loadUnifiedMarkersInBounds();
        } else {
          _loadSpotsInBounds();
        }
      }
    });

    // 监听用户认证状态变化，当用户切换时重置地图数据
    Services.auth.currentUserNotifier.addListener(_onUserChanged);

    // 监听活动数据变化
    Services.fishingActivity.addListener(_onActivitiesDataChanged);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 不在这里刷新，避免过度刷新
  }

  /// 当从其他页面返回时检查是否需要刷新
  void checkForUpdatesOnReturn() {
    debugPrint('🔄 [地图页面] 从其他页面返回，检查更新');
    debugPrint('🔄 [地图页面] 当前地图上活动数量: ${_activities.length}');

    // 打印当前活动列表
    for (final activity in _activities) {
      debugPrint(
        '🔄 [地图页面] 当前活动: ${activity.id} - ${activity.title} (${activity.status})',
      );
    }

    // 强制刷新活动数据
    forceRefreshActivities();
  }

  @override
  void dispose() {
    // 注销统一导航回调 ⭐ 新增
    MapNavigationService.instance.unregisterMapNavigationCallback();

    // 清理定时器和监听器
    _locationUpdateTimer?.cancel();
    _locationSubscription?.cancel();
    _batchUpdateTimer?.cancel();

    // 清理动画控制器
    _splitScreenAnimationController.dispose();
    _markerAnimationController.dispose();

    // 清理标记更新动画控制器
    for (final controller in _markerUpdateControllers.values) {
      controller.dispose();
    }
    _markerUpdateControllers.clear();

    // 移除用户状态监听器
    Services.auth.currentUserNotifier.removeListener(_onUserChanged);

    // 移除活动数据变化监听
    Services.fishingActivity.removeListener(_onActivitiesDataChanged);

    // 清理统一管理器 ⭐ 新增
    _configService?.removeListener(_onFilterConfigChanged);
    _markerManager?.dispose();

    // 清理标记缓存
    _spotMarkerCache.clear();
    _activityMarkerCache.clear();

    super.dispose();
  }

  /// 处理活动数据变化
  void _onActivitiesDataChanged() {
    debugPrint('📢 [地图页面] 收到活动数据变化通知');
    if (mounted) {
      // 使用响应式更新而不是强制刷新
      refreshActivities();
    }
  }

  /// 公共方法：触发添加钓点模式（供MainScreen调用）
  void triggerAddSpotMode() {
    _isAddingActivity = false;
    _toggleSplitScreenModeWithLocationName();
  }

  /// 公共方法：触发添加活动模式
  void triggerAddActivityMode() {
    _isAddingActivity = true;
    _toggleSplitScreenModeWithLocationName();
  }

  /// 公共方法：导航到指定钓点（已废弃，请使用统一导航服务）
  @deprecated
  void navigateToSpot(dynamic spotLocation, String spotId) {
    try {
      debugPrint('🗺️ [MapPage] ========== 开始导航到钓点 ==========');
      debugPrint('🗺️ [MapPage] 目标钓点ID: $spotId');
      debugPrint('🗺️ [MapPage] 接收到的位置信息: $spotLocation');
      debugPrint('🗺️ [MapPage] 位置信息类型: ${spotLocation.runtimeType}');
      debugPrint('🗺️ [MapPage] 组件mounted状态: $mounted');
      debugPrint('🗺️ [MapPage] 地图准备状态: $_isMapReady');

      // 解析位置信息
      LatLng targetLocation;
      if (spotLocation is LatLng) {
        targetLocation = spotLocation;
        debugPrint('🗺️ [MapPage] 位置信息类型: LatLng对象');
      } else if (spotLocation is Map<String, dynamic>) {
        debugPrint('🗺️ [MapPage] 位置信息类型: Map对象');
        final lat = spotLocation['latitude'] as double?;
        final lng = spotLocation['longitude'] as double?;
        debugPrint('🗺️ [MapPage] 解析纬度: $lat');
        debugPrint('🗺️ [MapPage] 解析经度: $lng');

        if (lat != null && lng != null) {
          targetLocation = LatLng(lat, lng);
          debugPrint('🗺️ [MapPage] Map格式位置解析成功');
        } else {
          debugPrint('❌ [MapPage] 无效的位置信息格式: $spotLocation');
          return;
        }
      } else {
        debugPrint('❌ [MapPage] 不支持的位置信息类型: ${spotLocation.runtimeType}');
        return;
      }

      debugPrint(
        '🗺️ [MapPage] 解析后的目标位置: ${targetLocation.latitude}, ${targetLocation.longitude}',
      );

      // 检查地图是否已经准备就绪
      if (_isMapReady) {
        debugPrint('🗺️ [MapPage] 地图已准备就绪，立即执行导航');
        _executeNavigation(targetLocation, spotId);
      } else {
        debugPrint('🗺️ [MapPage] 地图未准备就绪，保存导航请求等待地图准备完成');
        _pendingSpotLocation = targetLocation;
        _pendingSpotId = spotId;

        // 设置超时机制，如果地图长时间未准备就绪，强制执行导航
        Future.delayed(const Duration(seconds: 3), () {
          if (!_isMapReady &&
              _pendingSpotLocation != null &&
              _pendingSpotId != null) {
            debugPrint('🗺️ [MapPage] 地图准备超时，强制执行导航');
            _executeNavigation(_pendingSpotLocation!, _pendingSpotId!);
            _pendingSpotLocation = null;
            _pendingSpotId = null;
          }
        });
      }

      debugPrint('🗺️ [MapPage] ========== 地图导航请求处理完成 ==========');
    } catch (e) {
      debugPrint('❌ [MapPage] 导航到钓点失败: $e');
      debugPrint('❌ [MapPage] 错误堆栈: ${StackTrace.current}');
    }
  }

  /// 执行实际的地图导航操作
  void _executeNavigation(LatLng targetLocation, String spotId) {
    try {
      debugPrint('🗺️ [MapPage] ========== 执行地图导航 ==========');
      debugPrint(
        '🗺️ [MapPage] 目标位置: ${targetLocation.latitude}, ${targetLocation.longitude}',
      );
      debugPrint('🗺️ [MapPage] 目标钓点ID: $spotId');

      // 检查MapController状态
      try {
        debugPrint('🗺️ [MapPage] MapController状态: $mapController');
        debugPrint('🗺️ [MapPage] 当前地图中心: ${mapController.camera.center}');
        debugPrint('🗺️ [MapPage] 当前缩放级别: ${mapController.camera.zoom}');
      } catch (e) {
        debugPrint('❌ [MapPage] 无法获取MapController状态: $e');
        // 继续尝试执行导航
      }

      debugPrint('🗺️ [MapPage] 准备移动地图到目标位置');

      // 移动地图到目标位置，使用较高的缩放级别以便清楚看到钓点
      mapController.move(targetLocation, 16.0);
      debugPrint('🗺️ [MapPage] 地图移动命令已执行');

      // 延迟加载钓点，确保地图移动完成
      debugPrint('🗺️ [MapPage] 设置延迟加载钓点 (800ms)');
      Future.delayed(const Duration(milliseconds: 800), () {
        debugPrint('🗺️ [MapPage] 延迟时间到，检查mounted状态: $mounted');
        if (mounted) {
          debugPrint('🗺️ [MapPage] 开始加载标记数据');
          if (_markerManager != null) {
            _loadUnifiedMarkersInBounds();
          } else {
            _loadSpotsInBounds();
          }

          // 再次延迟，确保钓点加载完成后高亮显示目标钓点
          debugPrint('🗺️ [MapPage] 设置延迟高亮显示 (500ms)');
          Future.delayed(const Duration(milliseconds: 500), () {
            debugPrint('🗺️ [MapPage] 高亮延迟时间到，检查mounted状态: $mounted');
            if (mounted) {
              debugPrint('🗺️ [MapPage] 开始高亮显示目标钓点');
              _highlightTargetSpot(spotId);
            } else {
              debugPrint('❌ [MapPage] 组件已销毁，跳过高亮显示');
            }
          });
        } else {
          debugPrint('❌ [MapPage] 组件已销毁，跳过钓点加载');
        }
      });

      debugPrint('🗺️ [MapPage] ========== 地图导航执行完成 ==========');
    } catch (e) {
      debugPrint('❌ [MapPage] 执行地图导航失败: $e');
      debugPrint('❌ [MapPage] 错误堆栈: ${StackTrace.current}');
    }
  }

  /// 高亮显示目标钓点
  void _highlightTargetSpot(String spotId) {
    try {
      debugPrint('🗺️ [MapPage] ========== 开始高亮显示钓点 ==========');
      debugPrint('🗺️ [MapPage] 目标钓点ID: $spotId');
      debugPrint('🗺️ [MapPage] 当前加载的钓点数量: ${_spots.length}');
      debugPrint('🗺️ [MapPage] 已加载的钓点列表:');

      for (int i = 0; i < _spots.length; i++) {
        final spot = _spots[i];
        debugPrint(
          '🗺️ [MapPage]   [$i] ID: ${spot.id}, 名称: ${spot.name}, 位置: ${spot.locationLatLng}',
        );
      }

      // 查找目标钓点
      final targetSpot = _spots.firstWhere(
        (spot) => spot.id == spotId,
        orElse: () => throw Exception('钓点未找到'),
      );

      debugPrint('🗺️ [MapPage] ✅ 找到目标钓点: ${targetSpot.name}');
      debugPrint('🗺️ [MapPage] 目标钓点位置: ${targetSpot.locationLatLng}');
      debugPrint('🗺️ [MapPage] 当前地图中心: ${mapController.camera.center}');
      debugPrint('🗺️ [MapPage] 当前缩放级别: ${mapController.camera.zoom}');

      // 这里可以添加高亮显示逻辑，比如：
      // 1. 显示一个临时的高亮标记
      // 2. 播放一个简短的动画
      // 3. 显示钓点信息弹窗

      // 暂时通过日志输出来确认功能正常
      debugPrint('🗺️ [MapPage] ✅ 钓点 "${targetSpot.name}" 已定位并高亮显示');
      debugPrint('🗺️ [MapPage] ========== 钓点高亮显示完成 ==========');
    } catch (e) {
      debugPrint('❌ [MapPage] 高亮显示钓点失败: $e');
      debugPrint('❌ [MapPage] 可能原因: 钓点未在当前视图范围内加载');
      debugPrint('❌ [MapPage] 当前地图边界信息:');

      final bounds = mapController.camera.visibleBounds;
      debugPrint('❌ [MapPage] 北边界: ${bounds.north}');
      debugPrint('❌ [MapPage] 南边界: ${bounds.south}');
      debugPrint('❌ [MapPage] 东边界: ${bounds.east}');
      debugPrint('❌ [MapPage] 西边界: ${bounds.west}');

      // 即使高亮失败，地图导航仍然成功
    }
  }

  /// 公共属性：获取分屏模式状态（供MainScreen访问）
  bool get isSplitScreenMode => _isSplitScreenMode;

  /// 处理统一导航请求 ⭐ 新增
  void _handleUnifiedNavigation(MapNavigationParams params) {
    debugPrint('🗺️ [MapPage] 收到统一导航请求');
    debugPrint('🗺️ [MapPage] 目标位置: ${params.targetLocation}');
    debugPrint('🗺️ [MapPage] 显示临时标记: ${params.showTemporaryMarker}');

    if (_isMapReady) {
      _executeUnifiedNavigation(params);
    } else {
      debugPrint('🗺️ [MapPage] 地图未准备就绪，保存导航请求');
      _pendingNavigationParams = params;
    }
  }

  /// 执行统一导航 ⭐ 新增
  void _executeUnifiedNavigation(MapNavigationParams params) {
    try {
      debugPrint('🗺️ [MapPage] 开始执行统一导航');

      // 处理临时标记
      setState(() {
        if (params.showTemporaryMarker) {
          _temporaryMarkerLocation = params.targetLocation;
          _showTemporaryMarker = true;
          debugPrint('🗺️ [MapPage] 显示蓝色临时标记于: $_temporaryMarkerLocation');
        } else {
          _showTemporaryMarker = false;
          debugPrint('🗺️ [MapPage] 隐藏临时标记');
        }
      });

      // 移动地图到目标位置，使用缩放级别16
      mapController.move(params.targetLocation, 16.0);
      debugPrint('🗺️ [MapPage] 地图移动到: ${params.targetLocation}，缩放级别: 16');

      // 延迟加载钓点
      Future.delayed(const Duration(milliseconds: 800), () {
        if (mounted) {
          debugPrint('🗺️ [MapPage] 开始加载标记数据');
          if (_markerManager != null) {
            _loadUnifiedMarkersInBounds();
          } else {
            _loadSpotsInBounds();
          }
        }
      });
    } catch (e) {
      debugPrint('❌ [MapPage] 执行统一导航失败: $e');
    }
  }

  /// 构建临时标记 ⭐ 新增
  Widget _buildTemporaryMarker() {
    return FaIcon(
      FontAwesomeIcons.locationDot,
      size: 30,
      color: const Color.fromARGB(201, 255, 213, 2), // 蓝色临时标记
    );
  }

  /// 公共方法：供外部调用的统一导航接口 ⭐ 新增
  void executeNavigation(MapNavigationParams params) {
    _handleUnifiedNavigation(params);
  }

  // 初始化数据
  Future<void> _initializeData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 静默尝试登录（不显示任何错误提示）
      try {
        if (!Services.auth.isLoggedIn) {
          await Services.auth.initialize();
        }
      } catch (e) {
        // 静默处理登录失败，不显示任何提示
        debugPrint('静默登录失败: $e');
      }

      // 初始化瓦片缓存服务
      await Services.cache.initialize();

      // 获取本地存储的位置或默认位置
      final initialLocation = Services.location.getCurrentLocation();

      setState(() {
        _userLocation = initialLocation;
        _isLoading = false;
      });

      // 移动地图到用户位置（使用最大缩放级别以便用户看到详细内容）
      mapController.move(_userLocation, 18.0);

      // 延迟加载标记，确保地图移动完成
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          if (_markerManager != null) {
            _loadUnifiedMarkersInBounds();
          } else {
            _loadSpotsInBounds();
          }
        }
      });

      // 异步获取最新位置（不阻塞UI）
      Services.location
          .requestLocationUpdate()
          .then((newLocation) {
            if (mounted) {
              setState(() {
                _userLocation = newLocation;
              });

              // 只有当位置变化较大时才移动地图（超过100米）
              final distance = Services.location.calculateDistance(
                _userLocation,
                newLocation,
              );
              if (distance > 0.1) {
                // 0.1公里 = 100米
                mapController.move(newLocation, mapController.camera.zoom);
                // 重新加载新位置范围内的标记
                if (_markerManager != null) {
                  _loadUnifiedMarkersInBounds();
                } else {
                  _loadSpotsInBounds();
                }
              }
            }
          })
          .catchError((e) {
            debugPrint('异步获取位置失败: $e');
          });

      // 启动定时位置更新
      _startPeriodicLocationUpdate();

      // 启动位置监听
      _startLocationListening();
    } catch (e) {
      debugPrint('初始化数据失败: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 启动定时位置更新
  void _startPeriodicLocationUpdate() {
    if (!_enablePeriodicLocationUpdate) return;

    _locationUpdateTimer = Timer.periodic(_locationUpdateInterval, (
      timer,
    ) async {
      if (!mounted) {
        timer.cancel();
        return;
      }

      try {
        final newLocation = await Services.location.requestLocationUpdate();
        if (mounted) {
          final distance = Services.location.calculateDistance(
            _userLocation,
            newLocation,
          );

          // 只有当位置变化超过50米时才更新UI
          if (distance > 0.05) {
            setState(() {
              _userLocation = newLocation;
            });

            // 如果位置变化较大（超过500米），重新加载标记
            if (distance > 0.5) {
              if (_markerManager != null) {
                _loadUnifiedMarkersInBounds();
              } else {
                _loadSpotsInBounds();
              }
            }
          }
        }
      } catch (e) {
        debugPrint('定时位置更新失败: $e');
      }
    });
  }

  // 启动位置监听
  void _startLocationListening() {
    // 启动位置服务的实时监听
    Services.location.startLocationTracking();

    // 监听位置变化流
    _locationSubscription = Services.location.locationStream.listen(
      (LatLng newLocation) {
        if (mounted) {
          final distance = Services.location.calculateDistance(
            _userLocation,
            newLocation,
          );

          // 只有当位置变化超过20米时才更新UI
          if (distance > 0.02) {
            setState(() {
              _userLocation = newLocation;
            });

            // 如果位置变化较大（超过200米），重新加载标记
            if (distance > 0.2) {
              if (_markerManager != null) {
                _loadUnifiedMarkersInBounds();
              } else {
                _loadSpotsInBounds();
              }
            }
          }
        }
      },
      onError: (error) {
        debugPrint('位置监听错误: $error');
      },
    );
  }

  // 用于追踪上次日志打印时间，避免频繁打印
  static DateTime? _lastLogTime;
  static int _lastSpotCount = -1;
  static int _lastActivityCount = -1;

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用，用于AutomaticKeepAliveClientMixin

    // 只在开发模式下打印构建日志，并且避免频繁打印相同信息
    if (AppConfig.instance.isDevelopmentMode) {
      final now = DateTime.now();
      final shouldLog =
          _lastLogTime == null ||
          now.difference(_lastLogTime!).inSeconds >= 2 ||
          _spots.length != _lastSpotCount ||
          _activities.length != _lastActivityCount;

      if (shouldLog) {
        debugPrint(
          '🔍 [地图构建] ${now.millisecondsSinceEpoch} - 钓点: ${_spots.length}, 活动: ${_activities.length}',
        );
        _lastLogTime = now;
        _lastSpotCount = _spots.length;
        _lastActivityCount = _activities.length;
      }
    }

    return PopScope(
      canPop: !_isSplitScreenMode, // 分屏模式下阻止默认返回行为
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop && _isSplitScreenMode) {
          // 在分屏模式下按返回键时，退出分屏模式
          _toggleSplitScreenMode();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          actions: const [
            DevMenu(), // 开发者菜单（仅在开发模式下显示）
          ],
        ),
        extendBodyBehindAppBar: true, // 让body延伸到AppBar后面
        body:
            _isSplitScreenMode
                ? _buildSplitScreenLayout()
                : _buildNormalLayout(),
        // [*参数调整*]右下角地图控制按钮（分屏模式下或搜索时隐藏）
        floatingActionButton:
            _isSplitScreenMode || _isSearching
                ? null
                : Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    SizedBox(
                      width: 50, // 缩小按钮宽度
                      height: 50, // 缩小按钮高度
                      child: FloatingActionButton(
                        onPressed: () {
                          // 切换地图类型
                          setState(() {
                            isVectorMap = !isVectorMap;
                          });
                        },
                        heroTag: 'switchMap',
                        tooltip: '切换地图类型',
                        backgroundColor: Colors.white,
                        child: FaIcon(
                          isVectorMap
                              ? FontAwesomeIcons.map
                              : FontAwesomeIcons.satellite,
                          size: 20, // 缩小图标尺寸
                        ),
                      ),
                    ),
                    const SizedBox(height: 12), // 缩小间距
                    SizedBox(
                      width: 50, // 缩小按钮宽度
                      height: 50, // 缩小按钮高度
                      child: FloatingActionButton(
                        onPressed: () {
                          // 切换注记层显示
                          setState(() {
                            showAnnotationLayer = !showAnnotationLayer;
                          });
                        },
                        heroTag: 'toggleAnnotation',
                        tooltip: '切换注记层',
                        backgroundColor: Colors.white,
                        child: FaIcon(
                          showAnnotationLayer
                              ? FontAwesomeIcons.layerGroup
                              : FontAwesomeIcons.square,
                          size: 20, // 缩小图标尺寸
                        ),
                      ),
                    ),
                    const SizedBox(height: 12), // 缩小间距
                    SizedBox(
                      width: 50, // 缩小按钮宽度
                      height: 50, // 缩小按钮高度
                      child: FloatingActionButton(
                        onPressed:
                            _isLocationResetting
                                ? null
                                : () {
                                  // 重置地图位置并获取最新位置
                                  _updateCurrentLocation();
                                },
                        heroTag: 'resetLocation',
                        tooltip: '重置位置',
                        backgroundColor:
                            _isLocationResetting ? Colors.grey : Colors.white,
                        child:
                            _isLocationResetting
                                ? const SizedBox(
                                  width: 16, // 缩小加载指示器
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                )
                                : const FaIcon(
                                  FontAwesomeIcons.locationCrosshairs,
                                  size: 20,
                                ), // 缩小图标尺寸
                      ),
                    ),
                    const SizedBox(height: 12), // 缩小间距
                    SizedBox(
                      width: 50, // 缩小按钮宽度
                      height: 50, // 缩小按钮高度
                      child: Semantics(
                        label: '过滤设置',
                        hint: '打开过滤配置页面，设置标记显示条件',
                        child: FloatingActionButton(
                          onPressed: () {
                            // 显示浮动过滤面板
                            _showFloatingFilter();
                          },
                          heroTag: 'openFilter',
                          tooltip: '过滤设置',
                          backgroundColor: Colors.white,
                          child: const FaIcon(
                            FontAwesomeIcons.filter,
                            size: 20, // 缩小图标尺寸
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
      ),
    );
  }

  /// 显示浮动过滤面板 ⭐ 新增
  void _showFloatingFilter() {
    // 统计当前显示的标记数量
    debugPrint('📊 [过滤面板] 打开过滤面板');
    debugPrint(
      '📊 [过滤面板] 过滤前 - 钓点: ${_spots.length}个, 活动: ${_activities.length}个',
    );
    debugPrint('📊 [过滤面板] 当前配置: ${_currentFilterConfig.getSummary()}');

    setState(() {
      _showFloatingFilterPanel = true;
    });
  }

  /// 隐藏浮动过滤面板 ⭐ 新增
  void _hideFloatingFilter() {
    // 统计关闭面板时的标记数量
    debugPrint('📊 [过滤面板] 关闭过滤面板');
    debugPrint(
      '📊 [过滤面板] 过滤后 - 钓点: ${_spots.length}个, 活动: ${_activities.length}个',
    );
    debugPrint('📊 [过滤面板] 最终配置: ${_currentFilterConfig.getSummary()}');

    setState(() {
      _showFloatingFilterPanel = false;
    });
  }

  /// 计算过滤统计数据 ⭐ 新增
  FilterStatistics _calculateFilterStatistics() {
    // 如果有统一标记管理器，使用其数据
    if (_markerManager != null) {
      return _calculateUnifiedFilterStatistics();
    }

    // 否则使用传统的钓点和活动数据
    final totalSpots = _spots.length;
    final totalActivities = _activities.length;
    final totalCount = totalSpots + totalActivities;

    // 使用简化的过滤逻辑
    final filteredCount = _applySimpleFilter(totalCount);

    // 计算高优先级数量（暂时使用模拟数据）
    final priorityCount = (totalCount * 0.2).round(); // 假设20%是高优先级

    return FilterStatistics(
      totalCount: totalCount,
      filteredCount: filteredCount,
      priorityCount: priorityCount,
    );
  }

  /// 使用统一标记管理器计算过滤统计数据 ⭐ 新增
  FilterStatistics _calculateUnifiedFilterStatistics() {
    // 获取当前缓存的所有标记
    final allMarkers = _markerManager?.getAllCachedMarkers() ?? [];
    final totalCount = allMarkers.length;

    if (totalCount == 0) {
      return const FilterStatistics(
        totalCount: 0,
        filteredCount: 0,
        priorityCount: 0,
      );
    }

    // 使用过滤服务计算过滤后的数量
    final filterService = _markerManager?.filterService;
    if (filterService != null) {
      final filterResult = filterService.filterMarkers(
        allMarkers,
        _currentFilterConfig,
      );
      final filteredCount = filterResult.filteredMarkers.length;

      // 计算高优先级数量（我的发布、收藏、高点赞）
      final priorityCount =
          filterResult.filteredMarkers.where((marker) {
            return marker.isMine ||
                marker.isFavorited ||
                (marker is SpotMarker && marker.likesCount > 5);
          }).length;

      return FilterStatistics(
        totalCount: totalCount,
        filteredCount: filteredCount,
        priorityCount: priorityCount,
      );
    }

    // 降级方案：使用简化逻辑
    final filteredCount = _applySimpleFilter(totalCount);
    final priorityCount = (totalCount * 0.2).round();

    return FilterStatistics(
      totalCount: totalCount,
      filteredCount: filteredCount,
      priorityCount: priorityCount,
    );
  }

  /// 应用简单过滤逻辑 ⭐ 新增
  int _applySimpleFilter(int totalCount) {
    // 根据当前过滤配置计算过滤后的数量
    // 这里使用简化逻辑，实际应该根据具体过滤条件计算

    int filteredCount = totalCount;

    // 如果不显示某些类型，减少相应数量
    if (!_currentFilterConfig.showSpots) {
      filteredCount = (filteredCount * 0.6).round(); // 假设钓点占40%
    }
    if (!_currentFilterConfig.showActivities) {
      filteredCount = (filteredCount * 0.8).round(); // 假设活动占20%
    }

    // 应用最大显示数量限制
    if (filteredCount > _currentFilterConfig.maxDisplayCount) {
      filteredCount = _currentFilterConfig.maxDisplayCount;
    }

    return filteredCount;
  }

  // 过滤配置变更处理已存在，无需重复定义

  /// 打开过滤配置页面 ⭐ 新增
  void _openFilterPage() async {
    // 导航到过滤配置页面
    final result = await Navigator.of(context).push<FilterConfig>(
      MaterialPageRoute(
        builder:
            (context) => MarkerFilterPage(
              initialConfig: _currentFilterConfig,
              onConfigChanged: (config) {
                // 实时处理配置变更
                _onFilterConfigChanged(config);
                debugPrint('过滤配置已更新: ${config.getSummary()}');
              },
            ),
      ),
    );

    // 处理返回的配置
    if (result != null) {
      debugPrint('应用新的过滤配置: ${result.getSummary()}');
      // 应用新配置并重新加载数据
      await _configService?.updateConfig(result, autoSave: true);
      _loadUnifiedMarkersInBounds();
    }

    debugPrint('打开过滤配置页面');
  }

  /// 初始化统一标记管理器 ⭐ 新增
  Future<void> _initializeUnifiedMarkerManager() async {
    debugPrint('🔧 [统一管理器] 开始初始化统一标记管理器');

    try {
      // 获取配置服务
      debugPrint('🔧 [统一管理器] 获取配置服务');
      _configService = FilterConfigService.instance;
      _currentFilterConfig = _configService!.currentConfig;
      debugPrint('🔧 [统一管理器] 当前配置: ${_currentFilterConfig.getSummary()}');

      // 创建缓存实例
      debugPrint('🔧 [统一管理器] 创建缓存实例');
      final cache = UnifiedMarkerCache(
        spotService: Services.fishingSpot,
        activityService: Services.fishingActivity,
      );

      // 创建统一标记管理器
      debugPrint('🔧 [统一管理器] 创建统一标记管理器');
      _markerManager = UnifiedMarkerManager(
        cache: cache,
        spotService: Services.fishingSpot,
        activityService: Services.fishingActivity,
      );

      // 初始化管理器
      debugPrint('🔧 [统一管理器] 开始初始化管理器');
      await _markerManager!.initialize();
      debugPrint('🔧 [统一管理器] 管理器初始化完成');

      // 设置用户信息
      _markerManager!.setCurrentUser(
        userId: Services.auth.currentUser?.id,
        location: _userLocation,
      );

      // 监听标记更新
      _markerManager!.markersStream.listen((markers) {
        if (mounted) {
          // 统计流更新的数据
          final streamSpots =
              markers.where((m) => m.type == MarkerType.spot).length;
          final streamActivities =
              markers.where((m) => m.type == MarkerType.activity).length;
          debugPrint(
            '🔄 [统一管理器] 收到标记更新: ${markers.length} 个标记 (${streamSpots}个钓点, ${streamActivities}个活动)',
          );

          setState(() {
            _updateLegacyMarkersFromUnified(markers);
          });

          // 统计更新后实际显示的标记
          final displayedSpots = _spots.length;
          final displayedActivities = _activities.length;
          debugPrint(
            '🔄 [流更新] 更新后显示: ${displayedSpots + displayedActivities}个标记 (${displayedSpots}个钓点, ${displayedActivities}个活动)',
          );
        }
      });

      // 监听配置变更
      _configService!.addListener(_onFilterConfigChanged);

      // 检查是否首次使用，显示操作引导 ⭐ 新增
      _checkFirstTimeUsage();

      debugPrint('✅ [统一管理器] 初始化完成');
    } catch (e) {
      debugPrint('❌ [统一管理器] 初始化失败: $e');
    }
  }

  /// 配置变更回调 ⭐ 修复配置同步问题
  void _onFilterConfigChanged(FilterConfig config) async {
    debugPrint('🎯 [配置变更] 收到配置变更请求');
    debugPrint('🎯 [配置变更] 新配置: ${config.getSummary()}');
    debugPrint(
      '🎯 [配置变更] 启用类型: ${config.enabledTypes.map((t) => t.value).toList()}',
    );

    if (mounted) {
      setState(() {
        _currentFilterConfig = config;
      });
      debugPrint('🎯 [配置变更] 本地配置已更新');

      // ✅ 关键修复1：同步配置到FilterConfigService
      // 这确保了底部过滤面板的更改能正确保存并应用到过滤逻辑中
      try {
        await _configService?.updateConfig(config, autoSave: true);
        debugPrint('✅ [配置同步] 配置已成功同步到FilterConfigService');
      } catch (e) {
        debugPrint('❌ [配置同步] 同步配置到服务失败: $e');
      }

      // ✅ 关键修复2：同步配置到UnifiedMarkerManager
      // 这确保了过滤逻辑使用最新的配置
      try {
        _markerManager?.updateFilterConfig(config);
        debugPrint('✅ [配置同步] 配置已成功同步到UnifiedMarkerManager');
      } catch (e) {
        debugPrint('❌ [配置同步] 同步配置到标记管理器失败: $e');
      }

      // 重新加载数据
      debugPrint('🎯 [配置变更] 开始重新加载标记数据');
      _loadUnifiedMarkersInBounds();
      debugPrint('🔄 [配置变更] 过滤配置已更新: ${config.getSummary()}');
    } else {
      debugPrint('⚠️ [配置变更] 组件未挂载，跳过配置更新');
    }
  }

  /// 使用统一管理器加载区域内的标记 ⭐ 新增
  Future<void> _loadUnifiedMarkersInBounds() async {
    debugPrint('🔄 [加载检查] _loadUnifiedMarkersInBounds 被调用');
    debugPrint(
      '🔄 [加载检查] _markerManager: ${_markerManager != null ? '已初始化' : 'null'}',
    );
    debugPrint('🔄 [加载检查] mounted: $mounted');

    if (_markerManager == null) {
      debugPrint('❌ [加载检查] _markerManager 为 null，跳过加载');
      return;
    }

    try {
      debugPrint('🔄 [加载检查] 开始设置加载状态');
      setState(() {
        _isLoading = true;
        _isIncrementalLoading = true;
        _currentLoadingStep = 1;
        _currentStepProgress = 0.0;
        _currentStepDescription = '正在获取标记列表...';
        _loadingError = null;
      });
      debugPrint('🔄 [增量加载] 开始增量加载');

      // 获取当前地图中心和半径
      final center = mapController.camera.center;
      final bounds = mapController.camera.visibleBounds;

      // 计算半径（简化计算）
      const Distance distance = Distance();
      final radiusKm =
          distance.as(
            LengthUnit.Kilometer,
            LatLng(bounds.south, bounds.west),
            LatLng(bounds.north, bounds.east),
          ) /
          2;

      debugPrint(
        '🔄 [统一加载] 开始加载区域标记: center=$center, radius=${radiusKm.toStringAsFixed(2)}km',
      );

      // 使用统一管理器加载标记
      final result = await _markerManager!.loadMarkersForRegion(
        center: center,
        radiusKm: radiusKm,
        config: _currentFilterConfig,
        onProgressUpdate: (progress) {
          debugPrint('📊 [加载进度] $progress');
          _updateLoadingProgress(progress);
        },
      );

      if (result.success) {
        // 统计下载的原始数据
        final downloadedSpots =
            result.markers.where((m) => m.type == MarkerType.spot).length;
        final downloadedActivities =
            result.markers.where((m) => m.type == MarkerType.activity).length;
        final totalDownloaded = result.markers.length;

        debugPrint('✅ [统一加载] 加载成功: ${result.markers.length} 个标记');
        debugPrint(
          '📊 [下载统计] 范围内总共下载: ${totalDownloaded}个标记 (${downloadedSpots}个钓点, ${downloadedActivities}个活动)',
        );
        debugPrint('📊 [加载统计] ${result.stats}');

        // 更新传统标记列表（这里会应用过滤）
        _updateLegacyMarkersFromUnified(result.markers);

        // 统计实际显示的标记
        final displayedSpots = _spots.length;
        final displayedActivities = _activities.length;
        final totalDisplayed = displayedSpots + displayedActivities;

        debugPrint(
          '📊 [显示统计] 实际显示: ${totalDisplayed}个标记 (${displayedSpots}个钓点, ${displayedActivities}个活动)',
        );

        if (totalDownloaded != totalDisplayed) {
          final filteredOut = totalDownloaded - totalDisplayed;
          debugPrint('📊 [过滤效果] 共过滤掉: ${filteredOut}个标记');
        }

        // 启动标记出现动画
        _markerAnimationController.reset();
        _markerAnimationController.forward();

        // 触发触觉反馈
        MarkerAnimationManager.triggerHapticFeedback(HapticFeedbackType.light);

        setState(() {
          _loadedMarkersCount = result.markers.length;
          _currentStepDescription = '加载完成';
          _currentStepProgress = 1.0;

          // 重置错误状态 ⭐ 新增
          _networkStatus = NetworkStatus.connected;
          _isOfflineMode = false;
          _retryCount = 0;
          _loadingError = null;

          // 更新缓存状态 ⭐ 新增
          _lastCacheStatus = _determineCacheStatus(result);
          _lastCacheTime = DateTime.now();
          _showCacheIndicator = true;
        });

        // 显示缓存状态指示器
        _showCacheStatusIndicator();
      } else {
        debugPrint('❌ [统一加载] 加载失败: ${result.error}');
        setState(() {
          _loadingError = result.error;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载标记失败: ${result.error}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ [统一加载] 加载异常: $e');

      // 分析错误类型
      final errorType = _analyzeErrorType(e);

      setState(() {
        _loadingError = e.toString();
        _networkStatus =
            errorType == LoadingErrorType.offline
                ? NetworkStatus.offline
                : NetworkStatus.error;
      });

      // 根据错误类型显示不同的处理方式
      _handleLoadingError(errorType, e.toString());
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isIncrementalLoading = false;
        });
        debugPrint('✅ [增量加载] 增量加载完成');
      }
    }
  }

  /// 更新加载进度 ⭐ 新增
  void _updateLoadingProgress(String progress) {
    if (!mounted) return;

    // 解析进度信息（假设格式为 "步骤X/Y: 描述 (进度%)"）
    try {
      final parts = progress.split(':');
      if (parts.length >= 2) {
        final stepPart = parts[0].trim();
        final descriptionPart = parts[1].trim();

        // 解析步骤
        final stepMatch = RegExp(r'步骤(\d+)/(\d+)').firstMatch(stepPart);
        if (stepMatch != null) {
          final currentStep = int.parse(stepMatch.group(1)!);

          // 解析进度百分比
          final progressMatch = RegExp(
            r'\((\d+)%\)',
          ).firstMatch(descriptionPart);
          final stepProgress =
              progressMatch != null
                  ? int.parse(progressMatch.group(1)!) / 100.0
                  : 0.0;

          setState(() {
            _currentLoadingStep = currentStep;
            _currentStepProgress = stepProgress;
            _currentStepDescription =
                descriptionPart.replaceAll(RegExp(r'\(\d+%\)'), '').trim();
          });
        }
      }
    } catch (e) {
      debugPrint('⚠️ [进度解析] 解析进度信息失败: $e');
      // 使用简单的描述
      setState(() {
        _currentStepDescription = progress;
      });
    }
  }

  /// 分析错误类型 ⭐ 新增
  LoadingErrorType _analyzeErrorType(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('socket')) {
      return LoadingErrorType.network;
    } else if (errorString.contains('timeout')) {
      return LoadingErrorType.timeout;
    } else if (errorString.contains('server') ||
        errorString.contains('http') ||
        errorString.contains('500') ||
        errorString.contains('502') ||
        errorString.contains('503')) {
      return LoadingErrorType.server;
    } else if (errorString.contains('permission') ||
        errorString.contains('unauthorized') ||
        errorString.contains('403')) {
      return LoadingErrorType.permission;
    } else if (errorString.contains('offline')) {
      return LoadingErrorType.offline;
    } else {
      return LoadingErrorType.unknown;
    }
  }

  /// 处理加载错误 ⭐ 新增
  void _handleLoadingError(LoadingErrorType errorType, String errorMessage) {
    // 增加重试计数
    _retryCount++;

    // 根据错误类型决定处理策略
    switch (errorType) {
      case LoadingErrorType.network:
      case LoadingErrorType.timeout:
        if (_retryCount <= _maxRetryCount) {
          // 自动重试
          _showRetrySnackBar('网络连接异常，正在重试...', () {
            _retryLoadingWithDelay();
          });
        } else {
          // 显示错误处理UI
          _showErrorHandler(errorType, errorMessage);
        }
        break;

      case LoadingErrorType.offline:
        setState(() {
          _isOfflineMode = true;
        });
        _showOfflineSnackBar();
        break;

      case LoadingErrorType.server:
        _showErrorHandler(errorType, errorMessage);
        break;

      case LoadingErrorType.permission:
        _showErrorHandler(errorType, errorMessage);
        break;

      case LoadingErrorType.unknown:
        if (_retryCount <= _maxRetryCount) {
          _showRetrySnackBar('加载失败，正在重试...', () {
            _retryLoadingWithDelay();
          });
        } else {
          _showErrorHandler(errorType, errorMessage);
        }
        break;
    }
  }

  /// 显示重试提示 ⭐ 新增
  void _showRetrySnackBar(String message, VoidCallback onRetry) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: '手动重试',
          textColor: Colors.white,
          onPressed: onRetry,
        ),
      ),
    );
  }

  /// 显示离线模式提示 ⭐ 新增
  void _showOfflineSnackBar() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.cloud_off, color: Colors.white, size: 16),
            SizedBox(width: 8),
            Text('当前处于离线模式，显示缓存数据'),
          ],
        ),
        backgroundColor: Colors.grey.shade700,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: '重试连接',
          textColor: Colors.white,
          onPressed: () {
            setState(() {
              _isOfflineMode = false;
              _retryCount = 0;
            });
            _loadUnifiedMarkersInBounds();
          },
        ),
      ),
    );
  }

  /// 显示错误处理器 ⭐ 新增
  void _showErrorHandler(LoadingErrorType errorType, String errorMessage) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => Dialog(
            child: LoadingErrorHandler(
              errorType: errorType,
              errorMessage: errorMessage,
              onRetry: () {
                Navigator.of(context).pop();
                setState(() {
                  _retryCount = 0;
                  _loadingError = null;
                });
                _loadUnifiedMarkersInBounds();
              },
              customActions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _isOfflineMode = true;
                    });
                  },
                  child: const Text('离线模式'),
                ),
              ],
            ),
          ),
    );
  }

  /// 延迟重试加载 ⭐ 新增
  void _retryLoadingWithDelay() {
    Future.delayed(Duration(seconds: _retryCount * 2), () {
      if (mounted) {
        _loadUnifiedMarkersInBounds();
      }
    });
  }

  /// 计算加载指示器的顶部位置 ⭐ 新增
  double _getLoadingIndicatorTop() {
    double top = 100; // 默认位置

    // 如果有网络状态指示器，向下偏移
    if (_networkStatus != NetworkStatus.connected) {
      top += 40;
    }

    // 如果有离线模式指示器，向下偏移
    if (_isOfflineMode) {
      top += 40;
    }

    return top;
  }

  /// 计算分屏模式下加载指示器的顶部位置 ⭐ 新增
  double _getSplitScreenLoadingIndicatorTop() {
    double top = 60; // 分屏模式默认位置

    // 如果有网络状态指示器，向下偏移
    if (_networkStatus != NetworkStatus.connected) {
      top += 40;
    }

    // 如果有离线模式指示器，向下偏移
    if (_isOfflineMode) {
      top += 40;
    }

    return top;
  }

  /// 确定缓存状态 ⭐ 新增
  CacheHitStatus _determineCacheStatus(dynamic result) {
    // 这里需要根据实际的result结构来判断缓存状态
    // 暂时使用简单的逻辑
    if (_isOfflineMode) {
      return CacheHitStatus.offline;
    } else if (_retryCount > 0) {
      return CacheHitStatus.miss;
    } else {
      // 假设快速加载表示缓存命中
      return CacheHitStatus.hit;
    }
  }

  /// 显示缓存状态指示器 ⭐ 新增
  void _showCacheStatusIndicator() {
    // 3秒后自动隐藏缓存指示器
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showCacheIndicator = false;
        });
      }
    });
  }

  // 操作引导提示相关方法已删除，避免布局溢出问题

  /// 检查首次使用并显示引导 ⭐ 已简化，移除操作引导功能
  Future<void> _checkFirstTimeUsage() async {
    // 操作引导功能已移除，避免布局溢出问题
    // 如需要可在后续版本中用其他方式实现
    debugPrint('ℹ️ [首次使用检查] 操作引导功能已禁用');
  }

  /// 从统一标记更新传统标记列表（用于兼容性） ⭐ 实现转换逻辑
  void _updateLegacyMarkersFromUnified(List<UnifiedMarker> unifiedMarkers) {
    debugPrint('🔄 [标记转换] 开始转换 ${unifiedMarkers.length} 个统一标记');

    // 统计输入标记类型分布
    final inputSpots =
        unifiedMarkers.where((m) => m.type == MarkerType.spot).length;
    final inputActivities =
        unifiedMarkers.where((m) => m.type == MarkerType.activity).length;
    debugPrint('🔄 [标记转换] 输入标记: ${inputSpots}个钓点, ${inputActivities}个活动');

    _spots.clear();
    _activities.clear();

    for (final marker in unifiedMarkers) {
      if (marker is SpotMarker) {
        // 转换SpotMarker到FishingSpot
        final fishingSpot = FishingSpot(
          id: marker.id,
          name: marker.name,
          description: '', // SpotMarker没有description字段
          location: {
            'lat': marker.location.latitude,
            'lon': marker.location.longitude,
          },
          userId: marker.userId,
          userName: null, // 统一标记中没有用户名信息
          spotType: marker.spotType,
          fishTypes: marker.fishTypes,
          status: marker.status,
          isOnSite: marker.isOnSite,
          likesCount: marker.likesCount,
          created: marker.created,
          updated: marker.updated,
        );
        _spots.add(fishingSpot);
        debugPrint('🔄 [标记转换] 转换钓点: ${marker.id} -> ${fishingSpot.name}');
      } else if (marker is ActivityMarker) {
        // 转换ActivityMarker到FishingActivity
        final fishingActivity = FishingActivity(
          id: marker.id,
          title: marker.name,
          description: '', // ActivityMarker没有description字段
          location: {
            'lat': marker.location.latitude,
            'lon': marker.location.longitude,
          },
          startTime: marker.startTime,
          maxParticipants: marker.maxParticipants,
          currentParticipants: marker.currentParticipants,
          creatorId: marker.userId,
          creatorName: null, // 统一标记中没有用户名信息
          status: marker.status,
          activityType: marker.activityType,
          created: marker.created,
          updated: marker.updated,
        );
        _activities.add(fishingActivity);
        debugPrint('🔄 [标记转换] 转换活动: ${marker.id} -> ${fishingActivity.title}');
      }
    }

    debugPrint(
      '🔄 [标记转换] 转换完成: ${_spots.length} 个钓点, ${_activities.length} 个活动',
    );

    // 输出过滤效果总结
    final totalFiltered = _spots.length + _activities.length;
    final totalInput = unifiedMarkers.length;
    final filteredOut = totalInput - totalFiltered;
    debugPrint(
      '📊 [过滤总结] 输入: ${totalInput}个标记, 输出: ${totalFiltered}个标记, 过滤掉: ${filteredOut}个标记',
    );

    if (filteredOut > 0) {
      final spotFiltered = inputSpots - _spots.length;
      final activityFiltered = inputActivities - _activities.length;
      debugPrint(
        '📊 [过滤详情] 钓点过滤: ${spotFiltered}个, 活动过滤: ${activityFiltered}个',
      );
    }
  }

  /// 处理搜索位置选择 ⭐ 新增
  void _handleSearchLocationSelected(LatLng location) {
    // 使用统一导航服务处理搜索位置选择
    Services.mapNavigation.navigateToSearchLocation(
      location: location,
      context: context,
    );
    debugPrint('通过统一导航服务处理搜索位置: ${location.latitude}, ${location.longitude}');
  }

  // 构建正常布局
  Widget _buildNormalLayout() {
    return Stack(
      children: [
        _isLoading
            ? const Center(child: CircularProgressIndicator())
            : FlutterMap(
              key: _mapKey,
              mapController: mapController,
              options: MapOptions(
                initialCenter: _userLocation,
                initialZoom: 18.0, // 设置为最大缩放级别
                minZoom: 1, // 设置最小缩放级别
                maxZoom: 18.0, // 设置最大缩放级别
                // 禁用旋转功能
                keepAlive: true,
                // 地图准备就绪时加载钓点
                onMapReady: () {
                  debugPrint('🗺️ [MapPage] 地图准备就绪 (正常布局)');
                  _isMapReady = true;

                  Future.delayed(const Duration(milliseconds: 100), () {
                    if (mounted) {
                      if (_markerManager != null) {
                        _loadUnifiedMarkersInBounds();
                      } else {
                        _loadSpotsInBounds();
                      }

                      // 检查是否有待执行的统一导航请求 ⭐ 新增
                      if (_pendingNavigationParams != null) {
                        debugPrint('🗺️ [MapPage] 执行待处理的统一导航请求');
                        _executeUnifiedNavigation(_pendingNavigationParams!);
                        _pendingNavigationParams = null;
                      }

                      // 检查是否有待执行的导航请求（兼容旧版本）
                      if (_pendingSpotLocation != null &&
                          _pendingSpotId != null) {
                        debugPrint('🗺️ [MapPage] 执行待处理的导航请求');
                        _executeNavigation(
                          _pendingSpotLocation!,
                          _pendingSpotId!,
                        );
                        _pendingSpotLocation = null;
                        _pendingSpotId = null;
                      }
                    }
                  });
                },
                // 地图点击事件 - 退出搜索状态
                onTap: (tapPosition, point) {
                  _handleMapTap();
                },
              ),
              children: [
                // 天地图瓦片层
                TileLayer(
                  urlTemplate: TianDiTuUtils.buildTileUrlTemplate(
                    isVector: isVectorMap,
                    isAnnotation: false,
                  ),
                  additionalOptions: {'k': TianDiTuUtils.key},
                  subdomains: TianDiTuUtils.subdomains,
                  // 使用带缓存的瓦片提供者提高性能
                  tileProvider: Services.cache.createCachedTileProvider(),
                ),
                // 注记层
                if (showAnnotationLayer)
                  TileLayer(
                    urlTemplate: TianDiTuUtils.buildTileUrlTemplate(
                      isVector: isVectorMap,
                      isAnnotation: true,
                    ),
                    additionalOptions: {'k': TianDiTuUtils.key},
                    subdomains: TianDiTuUtils.subdomains,
                    // 使用带缓存的瓦片提供者提高性能
                    tileProvider: Services.cache.createCachedTileProvider(),
                  ),
                // 位置标记层 - 可调整大小和对齐
                CurrentLocationLayer(
                  style: LocationMarkerStyle(
                    marker: DefaultLocationMarker(
                      child: Container(
                        width: 40,
                        height: 40,
                        alignment: Alignment.center, // 确保箭头居中
                        child: const Icon(
                          Icons.navigation,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                    markerSize: const Size(35, 35), // [*参数调整*]调整位置标记大小
                    markerDirection: MarkerDirection.heading,
                  ),
                ),
                // 添加钓点标记
                MarkerLayer(
                  key: ValueKey('markers_v$_activitiesVersion'), // 使用数据版本号作为key
                  markers: [
                    // 钓点标记
                    for (final spot in _spots)
                      Marker(
                        point: spot.locationLatLng,
                        width: AppConfig.instance.mapMarkerSize, // 扩大以适应新的点击区域
                        height: AppConfig.instance.mapMarkerSize,
                        // 确保标记点始终保持竖直
                        rotate: true,
                        alignment:
                            Alignment
                                .topCenter, // [*调整参数*]地图上位置标记尖端在widget中心，使用默认居中对齐
                        child: _buildCachedSpotMarker(
                          spot,
                          size: AppConfig.instance.mapMarkerSize,
                        ),
                      ),
                    // 活动标记 ⭐ 新增
                    for (final activity in _activities)
                      if (activity.location != null)
                        Marker(
                          point: LatLng(
                            activity.location!['lat'] as double,
                            activity.location!['lon'] as double,
                          ),
                          width: AppConfig.instance.mapMarkerSize,
                          height: AppConfig.instance.mapMarkerSize,
                          rotate: true,
                          alignment: Alignment.topCenter,
                          child: _buildCachedActivityMarker(
                            activity,
                            size: AppConfig.instance.mapMarkerSize,
                          ),
                        ),
                  ],
                ),

                // 添加临时标记层 ⭐ 新增
                if (_showTemporaryMarker && _temporaryMarkerLocation != null)
                  MarkerLayer(
                    markers: [
                      Marker(
                        point: _temporaryMarkerLocation!,
                        width: 30,
                        height: 40,
                        alignment: Alignment.topCenter, // 与钓点标记保持一致的对齐方式
                        rotate: true, // 随地图旋转而旋转
                        child: _buildTemporaryMarker(),
                      ),
                    ],
                  ),
              ],
            ),

        // 优化后的搜索栏
        OptimizedSearchBar(
          key: _searchBarKey,
          currentLocation: _userLocation,
          onLocationSelected: _handleSearchLocationSelected, // ⭐ 修改为使用统一导航服务
          hintText: '搜索钓点、地址',
          onSearchStarted: () {
            setState(() {
              _isSearching = true;
            });
            // 搜索时隐藏底部导航栏
            widget.onBottomNavigationBarVisibilityChanged?.call(false);
          },
          onSearchEnded: () {
            setState(() {
              _isSearching = false;
            });
            // 搜索结束时显示底部导航栏
            widget.onBottomNavigationBarVisibilityChanged?.call(true);
          },
        ),

        // 网络状态指示器 ⭐ 新增
        if (_networkStatus != NetworkStatus.connected)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: NetworkStatusIndicator(
              status: _networkStatus,
              onRetry: () {
                setState(() {
                  _networkStatus = NetworkStatus.connecting;
                  _retryCount = 0;
                });
                _loadUnifiedMarkersInBounds();
              },
            ),
          ),

        // 离线模式指示器 ⭐ 新增
        if (_isOfflineMode)
          Positioned(
            top: _networkStatus != NetworkStatus.connected ? 40 : 0,
            left: 0,
            right: 0,
            child: OfflineModeIndicator(
              isOffline: _isOfflineMode,
              onTapToRetry: () {
                setState(() {
                  _isOfflineMode = false;
                  _networkStatus = NetworkStatus.connecting;
                  _retryCount = 0;
                });
                _loadUnifiedMarkersInBounds();
              },
            ),
          ),

        // 增量加载进度指示器 ⭐ 恢复测试
        if (_isIncrementalLoading)
          Positioned(
            top: _getLoadingIndicatorTop(),
            left: 0,
            right: 0,
            child: IncrementalLoadingIndicator(
              currentStep: _currentLoadingStep,
              stepProgress: _currentStepProgress,
              stepDescription: _currentStepDescription,
              loadedCount: _loadedMarkersCount,
              totalCount: _totalMarkersCount,
              errorMessage: _loadingError,
              onRetry:
                  _loadingError != null
                      ? () {
                        setState(() {
                          _loadingError = null;
                        });
                        _loadUnifiedMarkersInBounds();
                      }
                      : null,
            ),
          ),

        // 缓存状态指示器 ⭐ 恢复测试
        if (_showCacheIndicator && _lastCacheStatus != null)
          Positioned(
            top: 16,
            right: 16,
            child: CacheStatusIndicator(
              status: _lastCacheStatus!,
              cacheTime: _lastCacheTime,
              showDetails: true,
              position: CacheIndicatorPosition.topRight,
            ),
          ),

        // 底部过滤抽屉 ⭐ 更新
        if (_showFloatingFilterPanel)
          BottomFilterSheet(
            currentConfig: _currentFilterConfig,
            onConfigChanged: _onFilterConfigChanged,
            onClose: _hideFloatingFilter,
            onMoreSettings: _openFilterPage,
            statistics: _calculateFilterStatistics(),
          ),
      ],
    );
  }

  // 构建分屏布局
  Widget _buildSplitScreenLayout() {
    return Stack(
      children: [
        // 全屏地图作为背景
        FlutterMap(
          key: _mapKey,
          mapController: mapController,
          options: MapOptions(
            initialCenter: _userLocation,
            initialZoom: 18.0, // 设置为最大缩放级别
            minZoom: 1,
            maxZoom: 18.0,
            keepAlive: true,
            // 地图准备就绪时加载钓点
            onMapReady: () {
              debugPrint('🗺️ [MapPage] 地图准备就绪 (分屏布局)');
              _isMapReady = true;

              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted) {
                  if (_markerManager != null) {
                    _loadUnifiedMarkersInBounds();
                  } else {
                    _loadSpotsInBounds();
                  }

                  // 检查是否有待执行的统一导航请求 ⭐ 新增
                  if (_pendingNavigationParams != null) {
                    debugPrint('🗺️ [MapPage] 执行待处理的统一导航请求');
                    _executeUnifiedNavigation(_pendingNavigationParams!);
                    _pendingNavigationParams = null;
                  }

                  // 检查是否有待执行的导航请求（兼容旧版本）
                  if (_pendingSpotLocation != null && _pendingSpotId != null) {
                    debugPrint('🗺️ [MapPage] 执行待处理的导航请求');
                    _executeNavigation(_pendingSpotLocation!, _pendingSpotId!);
                    _pendingSpotLocation = null;
                    _pendingSpotId = null;
                  }
                }
              });
            },
            // 地图移动时更新中心标记位置 - 基于屏幕25%高度处
            onPositionChanged: (position, hasGesture) {
              if (hasGesture) {
                setState(() {
                  // 计算屏幕25%高度处对应的地图坐标
                  _centerMarkerLocation = _calculateLocationAt25PercentHeight();
                });
              }
            },
            // 地图点击事件 - 退出搜索状态
            onTap: (tapPosition, point) {
              _handleMapTap();
            },
          ),
          children: [
            // 底图层
            TileLayer(
              urlTemplate: TianDiTuUtils.buildTileUrlTemplate(
                isVector: isVectorMap,
                isAnnotation: false,
              ),
              additionalOptions: {'k': TianDiTuUtils.key},
              subdomains: TianDiTuUtils.subdomains,
              userAgentPackageName: 'com.example.fishing_app',
            ),

            // 注记层
            if (showAnnotationLayer)
              TileLayer(
                urlTemplate: TianDiTuUtils.buildTileUrlTemplate(
                  isVector: isVectorMap,
                  isAnnotation: true,
                ),
                additionalOptions: {'k': TianDiTuUtils.key},
                subdomains: TianDiTuUtils.subdomains,
                userAgentPackageName: 'com.example.fishing_app',
              ),

            // 添加钓点标记
            // 位置标记层 - 可调整大小和对齐
            CurrentLocationLayer(
              style: LocationMarkerStyle(
                marker: DefaultLocationMarker(
                  child: Container(
                    width: 40,
                    height: 40,
                    alignment: Alignment.center, // 确保箭头居中
                    child: const Icon(
                      Icons.navigation,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
                markerSize: const Size(35, 35), // [*参数调整*]调整位置标记大小
                markerDirection: MarkerDirection.heading,
              ),
            ),
            MarkerLayer(
              key: ValueKey(
                'split_markers_v$_activitiesVersion',
              ), // 使用数据版本号作为key
              markers: [
                // 钓点标记
                for (final spot in _spots)
                  Marker(
                    point: spot.locationLatLng,
                    width: AppConfig.instance.mapMarkerSize, // 扩大以适应新的点击区域
                    height: AppConfig.instance.mapMarkerSize,
                    rotate: true,
                    alignment: Alignment.topCenter, // 尖端在widget中心，使用默认居中对齐
                    child: _buildCachedSpotMarker(
                      spot,
                      size: AppConfig.instance.mapMarkerSize,
                    ),
                  ),
                // 活动标记 ⭐ 使用新的ActivityMarker组件
                for (final activity in _activities)
                  if (activity.location != null)
                    Marker(
                      point: activity.locationLatLng,
                      width: AppConfig.instance.mapMarkerSize,
                      height: AppConfig.instance.mapMarkerSize,
                      rotate: true,
                      alignment: Alignment.topCenter, // 与钓点标记保持一致
                      child: _buildCachedActivityMarker(
                        activity,
                        size: AppConfig.instance.mapMarkerSize,
                      ),
                    ),
              ],
            ),

            // 添加临时标记层 ⭐ 新增
            if (_showTemporaryMarker && _temporaryMarkerLocation != null)
              MarkerLayer(
                markers: [
                  Marker(
                    point: _temporaryMarkerLocation!,
                    width: 30,
                    height: 40,
                    alignment: Alignment.topCenter, // 与钓点标记保持一致的对齐方式
                    rotate: true, // 随地图旋转而旋转
                    child: _buildTemporaryMarker(),
                  ),
                ],
              ),
          ],
        ),

        // 中心标记覆盖层 - 位于屏幕顶部往下25%处
        Positioned(
          left: 0,
          right: 0,
          top: MarkerAlignmentUtils.calculatePinOffset(
            screenHeight: MediaQuery.of(context).size.height,
            targetHeightPercent: 0.25,
            pinSize: 40.0,
          ), // 精确计算图钉偏移，使针尖指向坐标点
          child: const Text(
            '📍',
            style: TextStyle(fontSize: 40),
            textAlign: TextAlign.center,
          ),
        ),

        // 底部表单 - 根据模式显示不同组件，带滑入动画
        AnimatedBuilder(
          animation: _splitScreenAnimation,
          builder: (context, child) {
            final offset =
                (1 - _splitScreenAnimation.value) *
                MediaQuery.of(context).size.height *
                0.75;
            return Transform.translate(offset: Offset(0, offset), child: child);
          },
          child:
              _isAddingActivity
                  ? SplitScreenAddActivity(
                    location: _centerMarkerLocation,
                    suggestedName: _suggestedSpotName,
                    onLocationChanged: (newLocation) {
                      setState(() {
                        _centerMarkerLocation = newLocation;
                      });
                    },
                    onClose: () {
                      _toggleSplitScreenMode();
                    },
                    onSpotAdded: (activity) {
                      setState(() {
                        _activities.add(activity);
                        // 添加到已加载的活动集合中
                        _loadedActivityIds.add(activity.id);
                      });

                      // 设置新活动位置作为目标位置，这样退出分屏模式时会移动到这里
                      final activityLocation = activity.locationLatLng;
                      _originalMapCenter = activityLocation;

                      // 关闭分屏模式（会自动移动到_originalMapCenter位置）
                      _toggleSplitScreenMode();
                    },
                  )
                  : SplitScreenAddSpot(
                    location: _centerMarkerLocation,
                    suggestedName: _suggestedSpotName,
                    onLocationChanged: (newLocation) {
                      setState(() {
                        _centerMarkerLocation = newLocation;
                      });
                    },
                    onClose: () {
                      _toggleSplitScreenMode();
                    },
                    onSpotAdded: (spot) {
                      setState(() {
                        _spots.add(spot);
                        // 添加到已加载的钓点集合中
                        _loadedSpotIds.add(spot.id);
                      });

                      // 设置新钓点位置作为目标位置，这样退出分屏模式时会移动到这里
                      final spotLocation = spot.locationLatLng;
                      _originalMapCenter = spotLocation;

                      // 关闭分屏模式（会自动移动到_originalMapCenter位置）
                      _toggleSplitScreenMode();
                    },
                  ),
        ),

        // 网络状态指示器 ⭐ 新增
        if (_networkStatus != NetworkStatus.connected)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: NetworkStatusIndicator(
              status: _networkStatus,
              onRetry: () {
                setState(() {
                  _networkStatus = NetworkStatus.connecting;
                  _retryCount = 0;
                });
                _loadUnifiedMarkersInBounds();
              },
            ),
          ),

        // 离线模式指示器 ⭐ 新增
        if (_isOfflineMode)
          Positioned(
            top: _networkStatus != NetworkStatus.connected ? 40 : 0,
            left: 0,
            right: 0,
            child: OfflineModeIndicator(
              isOffline: _isOfflineMode,
              onTapToRetry: () {
                setState(() {
                  _isOfflineMode = false;
                  _networkStatus = NetworkStatus.connecting;
                  _retryCount = 0;
                });
                _loadUnifiedMarkersInBounds();
              },
            ),
          ),

        // 增量加载进度指示器 ⭐ 恢复测试
        if (_isIncrementalLoading)
          Positioned(
            top: _getSplitScreenLoadingIndicatorTop(),
            left: 0,
            right: 0,
            child: IncrementalLoadingIndicator(
              currentStep: _currentLoadingStep,
              stepProgress: _currentStepProgress,
              stepDescription: _currentStepDescription,
              loadedCount: _loadedMarkersCount,
              totalCount: _totalMarkersCount,
              errorMessage: _loadingError,
              showDetails: false, // 分屏模式下显示简化版本
              onRetry:
                  _loadingError != null
                      ? () {
                        setState(() {
                          _loadingError = null;
                        });
                        _loadUnifiedMarkersInBounds();
                      }
                      : null,
            ),
          ),

        // 缓存状态指示器 ⭐ 恢复测试
        if (_showCacheIndicator && _lastCacheStatus != null)
          Positioned(
            top: 16,
            right: 16,
            child: CacheStatusIndicator(
              status: _lastCacheStatus!,
              cacheTime: _lastCacheTime,
              showDetails: false, // 分屏模式下显示简化版本
              position: CacheIndicatorPosition.topRight,
            ),
          ),

        // 底部过滤抽屉 ⭐ 更新
        if (_showFloatingFilterPanel)
          BottomFilterSheet(
            currentConfig: _currentFilterConfig,
            onConfigChanged: _onFilterConfigChanged,
            onClose: _hideFloatingFilter,
            onMoreSettings: _openFilterPage,
            statistics: _calculateFilterStatistics(),
          ),
      ],
    );
  }

  // 切换分屏模式
  void _toggleSplitScreenMode() {
    final wasInSplitScreenMode = _isSplitScreenMode;

    setState(() {
      _isSplitScreenMode = !_isSplitScreenMode;

      if (_isSplitScreenMode) {
        // 进入分屏模式时，设置中心标记位置为屏幕25%高度处
        _centerMarkerLocation = _calculateLocationAt25PercentHeight();
        // 启动进入动画
        _splitScreenAnimationController.forward();
      } else {
        // 退出分屏模式时，启动退出动画
        _splitScreenAnimationController.reverse();
        // 恢复原来的地图中心位置
        if (_originalMapCenter != null) {
          mapController.move(_originalMapCenter!, mapController.camera.zoom);
          _originalMapCenter = null; // 清除保存的位置
        }
        // 清除建议的钓点名称
        _suggestedSpotName = null;
      }
    });

    // 使用postFrameCallback确保在正确的时机调用回调
    if (_isSplitScreenMode && !wasInSplitScreenMode) {
      // 刚进入分屏模式，隐藏底部导航栏
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          widget.onBottomNavigationBarVisibilityChanged?.call(false);
        }
      });
    } else if (!_isSplitScreenMode && wasInSplitScreenMode) {
      // 刚退出分屏模式，显示底部导航栏
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          widget.onBottomNavigationBarVisibilityChanged?.call(true);
        }
      });
    }
  }

  // 切换分屏模式并获取位置名称
  Future<void> _toggleSplitScreenModeWithLocationName() async {
    if (_isSplitScreenMode) {
      // 如果已经在分屏模式，直接退出
      _toggleSplitScreenMode();
      return;
    }

    // 检查用户是否已登录
    if (!Services.auth.isLoggedIn) {
      // 显示登录提示对话框
      _showLoginRequiredDialog();
      return;
    }

    // 1. 读取屏幕中心点的经纬度坐标
    final screenCenterCoordinate = mapController.camera.center;

    // 保存当前地图中心位置，用于退出时恢复
    _originalMapCenter = screenCenterCoordinate;

    // 3. 验证坐标转换准确性（调试用）
    final screenSize = MediaQuery.of(context).size;
    MapCoordinateUtils.validateCoordinateConversion(
      mapController.camera,
      screenCenterCoordinate,
      screenSize,
    );

    // 4. 设置分屏模式状态并打开添加钓点页面
    setState(() {
      _isSplitScreenMode = true;
      _centerMarkerLocation = screenCenterCoordinate; // 标记位置就是原来的屏幕中心
    });

    // 启动动画，并添加地图移动监听器
    // 添加动画监听器，实现地图平滑移动
    late VoidCallback animationListener;
    animationListener = () {
      // 计算当前动画进度对应的地图位置
      final progress = _splitScreenAnimation.value;

      // 计算目标位置（屏幕25%高度处对应的地图坐标）
      final targetLocation = _calculateTargetLocationForSplitScreen(
        screenCenterCoordinate,
      );

      // 在原始位置和目标位置之间插值
      final currentLat =
          screenCenterCoordinate.latitude +
          (targetLocation.latitude - screenCenterCoordinate.latitude) *
              progress;
      final currentLng =
          screenCenterCoordinate.longitude +
          (targetLocation.longitude - screenCenterCoordinate.longitude) *
              progress;

      // 移动地图到当前插值位置
      mapController.move(
        LatLng(currentLat, currentLng),
        mapController.camera.zoom,
      );

      // 当动画完成时，移除监听器
      if (progress >= 1.0) {
        _splitScreenAnimation.removeListener(animationListener);
      }
    };

    _splitScreenAnimation.addListener(animationListener);
    _splitScreenAnimationController.forward();

    // 隐藏底部导航栏
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        widget.onBottomNavigationBarVisibilityChanged?.call(false);
      }
    });

    // 异步获取位置名称
    try {
      debugPrint(
        '开始获取位置名称: ${screenCenterCoordinate.latitude}, ${screenCenterCoordinate.longitude}',
      );

      final locationName = await _getTianDiTuLocationName(
        screenCenterCoordinate.longitude,
        screenCenterCoordinate.latitude,
      );

      if (locationName != null && locationName.trim().isNotEmpty) {
        setState(() {
          _suggestedSpotName = '${locationName.trim()}钓点';
        });
        debugPrint('获取到位置名称: $_suggestedSpotName');
      } else {
        debugPrint('未获取到有效的位置名称');
        setState(() {
          _suggestedSpotName = null;
        });
      }
    } catch (e) {
      debugPrint('获取位置名称失败: $e');
      setState(() {
        _suggestedSpotName = null;
      });
    }
  }

  // 直接调用天地图逆地理编码API
  Future<String?> _getTianDiTuLocationName(
    double longitude,
    double latitude,
  ) async {
    try {
      // 使用真实的天地图API调用
      debugPrint('开始调用天地图API获取位置名称: 经度=$longitude, 纬度=$latitude');

      final locationName = await TianDiTuUtils.getBestLocationName(
        longitude,
        latitude,
      );

      if (locationName != null && locationName.trim().isNotEmpty) {
        debugPrint('天地图API返回位置名称: $locationName');
        return locationName;
      } else {
        debugPrint('天地图API未返回有效位置名称，使用模拟数据');

        // 如果API调用失败，使用模拟数据作为备选
        if (latitude > 39.9 &&
            latitude < 40.0 &&
            longitude > 116.3 &&
            longitude < 116.5) {
          return '天安门广场';
        } else if (latitude > 31.2 &&
            latitude < 31.3 &&
            longitude > 121.4 &&
            longitude < 121.5) {
          return '外滩';
        } else if (latitude > 22.5 &&
            latitude < 22.6 &&
            longitude > 114.0 &&
            longitude < 114.2) {
          return '维多利亚港';
        } else {
          return '未知地点';
        }
      }
    } catch (e) {
      debugPrint('获取位置名称失败: $e，使用模拟数据');

      // 异常时使用模拟数据作为备选
      if (latitude > 39.9 &&
          latitude < 40.0 &&
          longitude > 116.3 &&
          longitude < 116.5) {
        return '天安门广场';
      } else {
        return '未知地点';
      }
    }
  }

  // 用于防止频繁加载的变量
  bool _isLoadingSpots = false;
  DateTime _lastLoadTime = DateTime.now();

  // 已加载的区域范围（用于增量加载）
  double? _loadedMinLat;
  double? _loadedMaxLat;
  double? _loadedMinLng;
  double? _loadedMaxLng;

  // 已加载的钓点集合（避免重复）
  final Set<String> _loadedSpotIds = {};
  // 已加载的活动集合（避免重复）
  final Set<String> _loadedActivityIds = {};

  // 根据地图范围加载钓点（增量加载）
  Future<void> _loadSpotsInBounds() async {
    // 如果页面正在加载或者已经在加载钓点，则跳过
    if (_isLoading || _isLoadingSpots) return;

    // 防止频繁加载：如果距离上次加载不足500毫秒，则跳过
    final now = DateTime.now();
    if (now.difference(_lastLoadTime).inMilliseconds < 500) return;

    _isLoadingSpots = true;
    _lastLoadTime = now;

    final bounds = mapController.camera.visibleBounds;

    // 计算2倍范围的边界
    final latRange = bounds.north - bounds.south;
    final lngRange = bounds.east - bounds.west;

    final expandedMinLat = bounds.south - latRange;
    final expandedMaxLat = bounds.north + latRange;
    final expandedMinLng = bounds.west - lngRange;
    final expandedMaxLng = bounds.east + lngRange;

    try {
      List<List<double>> newRegions = [];

      if (_loadedMinLat == null) {
        // 首次加载：加载2倍范围的所有钓点
        newRegions.add([
          expandedMinLat,
          expandedMaxLat,
          expandedMinLng,
          expandedMaxLng,
        ]);

        // 更新已加载范围
        _loadedMinLat = expandedMinLat;
        _loadedMaxLat = expandedMaxLat;
        _loadedMinLng = expandedMinLng;
        _loadedMaxLng = expandedMaxLng;
      } else {
        // 增量加载：只加载新增区域
        newRegions = _calculateNewRegions(
          expandedMinLat,
          expandedMaxLat,
          expandedMinLng,
          expandedMaxLng,
          _loadedMinLat!,
          _loadedMaxLat!,
          _loadedMinLng!,
          _loadedMaxLng!,
        );

        if (newRegions.isNotEmpty) {
          // 更新已加载范围为并集
          _loadedMinLat = math.min(_loadedMinLat!, expandedMinLat);
          _loadedMaxLat = math.max(_loadedMaxLat!, expandedMaxLat);
          _loadedMinLng = math.min(_loadedMinLng!, expandedMinLng);
          _loadedMaxLng = math.max(_loadedMaxLng!, expandedMaxLng);
        } else {
          _isLoadingSpots = false;
          return;
        }
      }

      // 加载新区域的钓点和活动
      final List<FishingSpot> newSpots = [];
      final List<FishingActivity> newActivities = [];
      for (final region in newRegions) {
        // 计算区域中心点和半径
        final centerLat = (region[0] + region[1]) / 2;
        final centerLng = (region[2] + region[3]) / 2;
        final center = LatLng(centerLat, centerLng);

        // 计算半径（取较大的维度）
        const double kmPerDegree = 111.0;
        final latRange = (region[1] - region[0]) * kmPerDegree;
        final lngRange =
            (region[3] - region[2]) *
            kmPerDegree *
            math.cos(centerLat * math.pi / 180);
        final radiusKm = math.max(latRange, lngRange) / 2;

        // 并行加载钓点和活动
        debugPrint('🔍 [地图加载] 开始加载区域数据: center=$center, radius=${radiusKm}km');
        final futures = await Future.wait([
          Services.fishingSpot.getVisibleSpots(
            center: center,
            radiusKm: radiusKm,
            perPage: 100,
          ),
          Services.fishingActivity.getActivitiesInRegion(
            center: center,
            radiusKm: radiusKm,
          ),
        ]);
        debugPrint('🔍 [地图加载] 区域数据加载完成');

        final regionSpots = futures[0] as List<FishingSpot>;
        final regionActivities = futures[1] as List<FishingActivity>;

        debugPrint(
          '🔍 [地图加载] 获取到 ${regionSpots.length} 个钓点，${regionActivities.length} 个活动',
        );

        // 过滤掉已加载的钓点
        for (final spot in regionSpots) {
          if (!_loadedSpotIds.contains(spot.id)) {
            newSpots.add(spot);
            _loadedSpotIds.add(spot.id);
          }
        }

        debugPrint('🔍 [地图加载] 开始处理 ${regionActivities.length} 个区域活动');

        // 过滤掉已加载的活动，同时检查活动状态
        for (final activity in regionActivities) {
          debugPrint(
            '🔍 [地图加载] 检查活动: ${activity.id} - ${activity.title} (状态: ${activity.status}, 过期: ${activity.isExpired})',
          );

          if (!_loadedActivityIds.contains(activity.id)) {
            // 只添加活跃状态的活动
            if (activity.status == 'active' && !activity.isExpired) {
              newActivities.add(activity);
              _loadedActivityIds.add(activity.id);
              debugPrint('✅ [地图加载] 新增活动: ${activity.id} - ${activity.title}');
            } else {
              debugPrint(
                '❌ [地图加载] 跳过非活跃活动: ${activity.id} - ${activity.title} (状态: ${activity.status}, 过期: ${activity.isExpired})',
              );
            }
          } else {
            debugPrint('⏭️ [地图加载] 跳过已加载活动: ${activity.id} - ${activity.title}');
          }
        }

        debugPrint('🔍 [地图加载] 清理前活动数量: ${_activities.length}');

        // 清理已取消或过期的活动
        final removedActivities = <FishingActivity>[];
        _activities.removeWhere((activity) {
          final shouldRemove =
              activity.status != 'active' || activity.isExpired;
          if (shouldRemove) {
            removedActivities.add(activity);
          }
          return shouldRemove;
        });

        for (final removed in removedActivities) {
          debugPrint(
            '🗑️ [地图加载] 移除非活跃活动: ${removed.id} - ${removed.title} (状态: ${removed.status})',
          );
        }

        // 同时清理对应的缓存和ID记录
        final activeActivityIds = _activities.map((a) => a.id).toSet();
        final removedIds = <String>[];
        _loadedActivityIds.retainWhere((id) {
          final shouldKeep = activeActivityIds.contains(id);
          if (!shouldKeep) {
            removedIds.add(id);
          }
          return shouldKeep;
        });

        for (final removedId in removedIds) {
          debugPrint('🗑️ [地图加载] 清理已加载ID: $removedId');
        }

        // 清理活动标记缓存
        final removedMarkers = <String>[];
        _activityMarkerCache.removeWhere((key, value) {
          final activityId = key.replaceFirst('activity_', '');
          final shouldRemove = !activeActivityIds.contains(activityId);
          if (shouldRemove) {
            removedMarkers.add(key);
          }
          return shouldRemove;
        });

        for (final removedMarker in removedMarkers) {
          debugPrint('🗑️ [地图加载] 清理活动标记缓存: $removedMarker');
        }

        debugPrint('✅ [地图加载] 清理后活动数量: ${_activities.length}');

        debugPrint(
          '🔍 [地图加载] 过滤后新增 ${newSpots.length} 个钓点，${newActivities.length} 个活动',
        );
      }

      if (mounted && (newSpots.isNotEmpty || newActivities.isNotEmpty)) {
        // 使用批量更新避免频繁刷新
        _batchUpdateData(newSpots, newActivities);
      }
    } catch (e) {
      debugPrint('加载范围内钓点失败: $e');
    } finally {
      _isLoadingSpots = false;
    }
  }

  // 计算需要加载的新区域
  List<List<double>> _calculateNewRegions(
    double newMinLat,
    double newMaxLat,
    double newMinLng,
    double newMaxLng,
    double oldMinLat,
    double oldMaxLat,
    double oldMinLng,
    double oldMaxLng,
  ) {
    List<List<double>> regions = [];

    // 检查是否有重叠
    if (newMaxLat < oldMinLat ||
        newMinLat > oldMaxLat ||
        newMaxLng < oldMinLng ||
        newMinLng > oldMaxLng) {
      // 完全不重叠，加载整个新区域
      regions.add([newMinLat, newMaxLat, newMinLng, newMaxLng]);
      return regions;
    }

    // 计算新增的区域（矩形分割）

    // 上方新区域
    if (newMaxLat > oldMaxLat) {
      regions.add([oldMaxLat, newMaxLat, newMinLng, newMaxLng]);
    }

    // 下方新区域
    if (newMinLat < oldMinLat) {
      regions.add([newMinLat, oldMinLat, newMinLng, newMaxLng]);
    }

    // 左侧新区域（排除已包含在上下区域的部分）
    if (newMinLng < oldMinLng) {
      final topLat = math.min(newMaxLat, oldMaxLat);
      final bottomLat = math.max(newMinLat, oldMinLat);
      if (topLat > bottomLat) {
        regions.add([bottomLat, topLat, newMinLng, oldMinLng]);
      }
    }

    // 右侧新区域（排除已包含在上下区域的部分）
    if (newMaxLng > oldMaxLng) {
      final topLat = math.min(newMaxLat, oldMaxLat);
      final bottomLat = math.max(newMinLat, oldMinLat);
      if (topLat > bottomLat) {
        regions.add([bottomLat, topLat, oldMaxLng, newMaxLng]);
      }
    }

    return regions;
  }

  // 重置已加载的区域状态
  void _resetLoadedRegions() {
    _loadedMinLat = null;
    _loadedMaxLat = null;
    _loadedMinLng = null;
    _loadedMaxLng = null;
    _loadedSpotIds.clear();
    _loadedActivityIds.clear();
    _spots.clear();
    _activities.clear();

    // 清理标记缓存
    _spotMarkerCache.clear();
    _activityMarkerCache.clear();
  }

  /// 刷新活动数据 - 产品级实现
  Future<void> refreshActivities() async {
    debugPrint('🔄 [地图页面] 开始刷新活动数据');
    debugPrint('🔄 [地图页面] 刷新前活动数量: ${_activities.length}');

    try {
      // 1. 获取最新的活动数据
      final latestActivities = await Services.fishingActivity.getAllActivities(
        forceRefresh: true,
      );

      // 2. 比较数据变化
      final hasChanges = _hasActivitiesChanged(latestActivities);

      if (hasChanges) {
        debugPrint('🔄 [地图页面] 检测到活动数据变化，更新UI');

        // 3. 更新本地数据
        await _updateActivitiesData(latestActivities);

        // 4. 触发UI更新
        if (mounted) {
          setState(() {
            _activitiesVersion++;
            debugPrint('🔄 [地图页面] 活动数据版本更新: v$_activitiesVersion');
          });
        }
      } else {
        debugPrint('🔄 [地图页面] 活动数据无变化，跳过更新');
      }
    } catch (e) {
      debugPrint('❌ [地图页面] 刷新活动数据失败: $e');
    }
  }

  /// 检查活动数据是否发生变化
  bool _hasActivitiesChanged(List<FishingActivity> newActivities) {
    // 比较活动数量
    if (_activities.length != newActivities.length) {
      return true;
    }

    // 比较活动ID集合
    final currentIds = _activities.map((a) => a.id).toSet();
    final newIds = newActivities.map((a) => a.id).toSet();

    return !currentIds.containsAll(newIds) || !newIds.containsAll(currentIds);
  }

  /// 更新活动数据
  Future<void> _updateActivitiesData(
    List<FishingActivity> newActivities,
  ) async {
    // 清理旧数据
    _loadedActivityIds.clear();
    _activities.clear();
    _activityMarkerCache.clear();

    // 更新为新数据
    _activities.addAll(newActivities);
    _loadedActivityIds.addAll(newActivities.map((a) => a.id));

    debugPrint('🔄 [地图页面] 活动数据更新完成: ${_activities.length} 个活动');
  }

  /// 兼容旧的强制刷新方法
  void forceRefreshActivities() {
    refreshActivities();
  }

  // 用户状态变化回调
  void _onUserChanged() {
    if (!mounted) return;

    // 清理照片标记的全局缓存
    PhotoFishingSpotMarkerBuilder.clearGlobalCache();

    // 重置已加载的钓点数据
    _resetLoadedRegions();

    // 重新加载当前视图范围内的标记
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        if (_markerManager != null) {
          _loadUnifiedMarkersInBounds();
        } else {
          _loadSpotsInBounds();
        }
      }
    });

    // 更新UI
    if (mounted) {
      setState(() {
        // 触发UI重建，显示新用户的钓点数据
      });
    }
  }

  /// 更新当前位置并移动地图 ⭐ 修改为使用统一导航服务
  Future<void> _updateCurrentLocation() async {
    if (_isLocationResetting) return;

    setState(() {
      _isLocationResetting = true;
    });

    try {
      // 异步获取最新位置
      final newLocation = await Services.location.requestLocationUpdate();

      if (mounted) {
        setState(() {
          _isLocationResetting = false;
        });

        // 使用统一导航服务
        await MapNavigationService.instance.navigateToGpsLocation(
          gpsLocation: newLocation,
          context: context,
        );

        // 显示成功提示
        SnackBarService.showSuccess(context, '位置已更新');

        // 重置加载状态并重新加载标记
        _resetLoadedRegions();
        if (_markerManager != null) {
          _loadUnifiedMarkersInBounds();
        } else {
          _loadSpotsInBounds();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLocationResetting = false;
        });

        SnackBarService.showError(context, '获取位置失败: $e');
      }
    }
  }

  /// 确保位置服务已启动
  Future<void> _ensureLocationServiceStarted() async {
    try {
      // 启动位置监听
      await Services.location.startLocationTracking();

      // 请求一次位置更新以确保有初始位置
      await Services.location.requestLocationUpdate();
    } catch (e) {
      debugPrint('位置服务启动失败: $e');
    }
  }

  /// 调试位置服务
  Future<void> _debugLocationServices() async {
    try {
      // 检查位置服务是否启用
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();

      // 检查权限
      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }

      // 尝试获取位置
      if (serviceEnabled &&
          permission != LocationPermission.denied &&
          permission != LocationPermission.deniedForever) {
        try {
          await Geolocator.getCurrentPosition(
            locationSettings: const LocationSettings(
              accuracy: LocationAccuracy.medium,
              timeLimit: Duration(seconds: 10),
            ),
          );
        } catch (e) {
          debugPrint('获取位置失败: $e');
        }
      }
    } catch (e) {
      debugPrint('位置服务检查失败: $e');
    }
  }

  // 显示钓点详情
  void _showSpotDetails(FishingSpot spot) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => SpotDetailPage(spot: spot)),
    );
  }

  // 显示活动详情
  void _showActivityDetails(FishingActivity activity) {
    // 导航到活动详情页面，并处理返回结果
    Navigator.pushNamed(context, '/activity-detail', arguments: activity).then((
      result,
    ) {
      // 如果返回结果为true，表示需要刷新地图
      if (result == true) {
        debugPrint('🔄 [地图页面] 从活动详情页面返回，需要刷新');
        checkForUpdatesOnReturn();
      }
    });
  }

  // 计算屏幕25%高度处对应的地图坐标（使用工具类）
  LatLng _calculateLocationAt25PercentHeight() {
    final screenSize = MediaQuery.of(context).size;
    final camera = mapController.camera;

    return MapCoordinateUtils.calculateLocationAt25PercentHeight(
      camera,
      screenSize,
    );
  }

  // 计算分屏模式下的目标地图位置
  LatLng _calculateTargetLocationForSplitScreen(LatLng originalCenter) {
    final screenSize = MediaQuery.of(context).size;

    // 使用工具类计算目标位置
    return MapCoordinateUtils.calculateTargetLocationForScreenPosition(
      mapController.camera,
      originalCenter,
      screenHeightPercent: 0.25,
      screenWidthPercent: 0.50,
      screenSize: screenSize,
    );
  }

  /// 构建缓存的钓点标记
  Widget _buildCachedSpotMarker(FishingSpot spot, {required double size}) {
    final cacheKey = 'spot_${spot.id}';

    // 如果缓存中已存在，直接返回
    if (_spotMarkerCache.containsKey(cacheKey)) {
      return _spotMarkerCache[cacheKey]!;
    }

    // 创建新标记并缓存
    final marker = _buildSpotMarker(spot, size: size);
    _spotMarkerCache[cacheKey] = marker;
    return marker;
  }

  /// 构建缓存的活动标记
  Widget _buildCachedActivityMarker(
    FishingActivity activity, {
    required double size,
  }) {
    final cacheKey = 'activity_${activity.id}';

    debugPrint('🔍 [活动标记] 构建活动标记: ${activity.id} - ${activity.title}');
    debugPrint('🔍 [活动标记] 活动位置: ${activity.location}');
    debugPrint('🔍 [活动标记] 活动类型: ${activity.activityType}');

    // 如果缓存中已存在，直接返回
    if (_activityMarkerCache.containsKey(cacheKey)) {
      debugPrint('🔍 [活动标记] 使用缓存的活动标记: $cacheKey');
      return _activityMarkerCache[cacheKey]!;
    }

    // 创建新的活动标记并缓存
    debugPrint('🔍 [活动标记] 创建新的活动标记: $cacheKey');
    final marker = widgets.ActivityMarker(
      activity: activity,
      size: size,
      onTap: () => _showActivityDetails(activity),
    );
    _activityMarkerCache[cacheKey] = marker;
    return marker;
  }

  /// 构建钓点标记（使用照片标记）
  Widget _buildSpotMarker(
    FishingSpot spot, {
    required double size,
    bool isActivity = false,
    FishingActivity? originalActivity,
  }) {
    // 确定点击回调
    final onTap =
        isActivity && originalActivity != null
            ? () => _showActivityDetails(originalActivity)
            : () => _showSpotDetails(spot);

    // 统一使用照片标记
    return PhotoFishingSpotMarkerBuilder.buildMarker(
      spot: spot,
      onTap: onTap,
      size: size, //[*参数调整*]这个是位置标记的整体大小
      getPhotos: (spotId) => Services.fishingSpot.getSpotPhotos(spotId),
      getLikesCount: (spotId) => Services.social.getSpotLikesCount(spotId),
    );
  }

  /// 批量更新数据，避免频繁刷新
  void _batchUpdateData(
    List<FishingSpot> newSpots,
    List<FishingActivity> newActivities,
  ) {
    // 取消之前的定时器
    _batchUpdateTimer?.cancel();

    // 设置新的定时器，延迟批量更新
    _batchUpdateTimer = Timer(const Duration(milliseconds: 200), () {
      if (mounted) {
        setState(() {
          _spots.addAll(newSpots);

          // 添加新活动到列表，但要避免重复
          for (final newActivity in newActivities) {
            // 检查是否已存在相同ID的活动
            final existingIndex = _activities.indexWhere(
              (a) => a.id == newActivity.id,
            );
            if (existingIndex != -1) {
              // 如果已存在，替换为最新数据
              _activities[existingIndex] = newActivity;
              debugPrint(
                '🔄 [地图更新] 更新现有活动: ${newActivity.id} - ${newActivity.title}',
              );
            } else {
              // 如果不存在，添加新活动
              _activities.add(newActivity);
              debugPrint(
                '➕ [地图更新] 添加新活动: ${newActivity.id} - ${newActivity.title}',
              );
            }
          }
        });
      }
    });
  }

  /// 处理地图点击事件
  void _handleMapTap() {
    // 如果当前正在搜索状态，则退出搜索
    if (_isSearching) {
      // 通过搜索栏的key调用清除方法
      _searchBarKey.currentState?.clearSearchAndUnfocus();
      // 注意：_isSearching状态会通过onSearchEnded回调自动更新
    }
  }

  /// 显示登录提示对话框
  void _showLoginRequiredDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('需要登录'),
          content: const Text('添加钓点需要先登录账户。是否前往登录页面？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 导航到登录页面
                Navigator.of(context).pushNamed('/login');
              },
              child: const Text('去登录'),
            ),
          ],
        );
      },
    );
  }
}
