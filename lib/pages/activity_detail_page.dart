import 'dart:async';
import 'package:flutter/material.dart';
import '../models/fishing_activity.dart';
import '../models/spot_photo.dart';
import '../services/service_locator.dart';
import '../services/unified_image_service.dart';
import '../widgets/snackbar.dart';
import '../widgets/carousel_height_manager.dart';

/// 活动详情全屏页面
///
/// 功能特性：
/// - 参考钓点详情页面的结构和交互
/// - 支持加入活动功能
/// - 集成群聊功能
/// - 参与者列表展示
/// - 活动状态跟踪
class ActivityDetailPage extends StatefulWidget {
  final FishingActivity activity;

  const ActivityDetailPage({super.key, required this.activity});

  @override
  State<ActivityDetailPage> createState() => _ActivityDetailPageState();
}

class _ActivityDetailPageState extends State<ActivityDetailPage>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  // 照片相关
  List<SpotPhoto> _photos = [];
  bool _isLoadingPhotos = true;

  // 照片轮播控制器
  late PageController _photoPageController;
  Timer? _autoPlayTimer;
  final ValueNotifier<int> _currentPhotoIndex = ValueNotifier<int>(0);

  // 滚动控制器
  late ScrollController _scrollController;

  // 活动参与状态
  bool _isParticipating = false;
  bool _isJoining = false;

  // 实时数据
  int _participantsCount = 0;
  List<Map<String, dynamic>> _participants = [];

  // 键盘状态
  bool _isKeyboardVisible = false;

  // 轮播图高度管理器
  late CarouselHeightManager _carouselHeightManager;

  // 统一图片服务
  final UnifiedImageService _imageService = UnifiedImageService();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _scrollController = ScrollController();

    _photoPageController = PageController();
    _carouselHeightManager = CarouselHeightManager();

    _loadActivityData();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _autoPlayTimer?.cancel();
    _scrollController.dispose();
    _photoPageController.dispose();
    _currentPhotoIndex.dispose();
    _carouselHeightManager.dispose();
    super.dispose();
  }

  /// 加载活动数据
  Future<void> _loadActivityData() async {
    try {
      // 加载活动照片
      await _loadActivityPhotos();
      
      // 加载参与状态
      await _loadParticipationStatus();
      
      // 加载参与者列表
      await _loadParticipants();
      
    } catch (e) {
      debugPrint('❌ [活动详情] 加载活动数据失败: $e');
    }
  }

  /// 加载活动照片
  Future<void> _loadActivityPhotos() async {
    try {
      setState(() {
        _isLoadingPhotos = true;
      });

      // 从活动的images字段提取照片
      final photos = <SpotPhoto>[];
      if (widget.activity.images != null && 
          widget.activity.images!['images'] is List) {
        final imagesList = widget.activity.images!['images'] as List;
        for (int i = 0; i < imagesList.length; i++) {
          final imageData = imagesList[i];
          if (imageData is Map<String, dynamic>) {
            photos.add(SpotPhoto(
              id: '${widget.activity.id}_$i',
              spotId: widget.activity.id,
              userId: widget.activity.creatorId,
              filename: 'activity_image_$i.jpg',
              url: imageData['url'] ?? imageData['thumbnail_url'] ?? '',
              thumbnailUrl: imageData['thumbnail_url'] ?? imageData['url'] ?? '',
              description: imageData['description'] ?? '',
              isCameraShot: imageData['is_from_camera'] ?? false,
              created: DateTime.now(),
              updated: DateTime.now(),
            ));
          }
        }
      }

      if (mounted) {
        setState(() {
          _photos = photos;
          _isLoadingPhotos = false;
        });
      }
    } catch (e) {
      debugPrint('❌ [活动详情] 加载照片失败: $e');
      if (mounted) {
        setState(() {
          _isLoadingPhotos = false;
        });
      }
    }
  }

  /// 加载参与状态
  Future<void> _loadParticipationStatus() async {
    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        setState(() {
          _isParticipating = false;
        });
        return;
      }

      // 检查用户是否已参与活动
      final isParticipating = await Services.fishingActivity.isUserParticipating(
        widget.activity.id,
        currentUser.id,
      );

      if (mounted) {
        setState(() {
          _isParticipating = isParticipating;
        });
      }
    } catch (e) {
      debugPrint('❌ [活动详情] 加载参与状态失败: $e');

      // 如果是活动不存在的错误，显示特殊提示
      if (mounted && e.toString().contains('活动不存在')) {
        _showActivityNotFoundDialog();
      }
    }
  }

  /// 加载参与者列表
  Future<void> _loadParticipants() async {
    try {
      final participants = await Services.fishingActivity.getActivityParticipants(
        widget.activity.id,
      );

      if (mounted) {
        setState(() {
          _participants = participants;
          _participantsCount = participants.length;
        });
      }
    } catch (e) {
      debugPrint('❌ [活动详情] 加载参与者失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // 监听键盘状态
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    // 键盘状态变化处理
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_isKeyboardVisible != isKeyboardVisible) {
        setState(() {
          _isKeyboardVisible = isKeyboardVisible;
        });
        _carouselHeightManager.updateKeyboardState(isKeyboardVisible);

        if (!isKeyboardVisible && _scrollController.hasClients) {
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    });

    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: true,
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // 自定义AppBar和照片展示区域
          _buildSliverAppBar(),

          // 活动信息内容
          SliverToBoxAdapter(child: _buildActivityContent()),
        ],
      ),

      // 底部操作栏
      bottomNavigationBar: _buildBottomActionBar(),
    );
  }

  /// 构建SliverAppBar和照片展示区域
  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: _carouselHeightManager.currentHeight,
      floating: false,
      pinned: true,
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      elevation: 0,
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.95),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
          color: Colors.black87,
          iconSize: 20,
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.95),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            icon: const Icon(Icons.share),
            onPressed: _handleShare,
            color: Colors.black87,
            iconSize: 20,
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: _buildPhotoCarousel(),
      ),
    );
  }

  /// 构建照片轮播
  Widget _buildPhotoCarousel() {
    if (_isLoadingPhotos) {
      return Container(
        color: Colors.grey.shade100,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_photos.isEmpty) {
      // 使用默认照片
      return Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.group,
                size: 80,
                color: Colors.white.withValues(alpha: 0.8),
              ),
              const SizedBox(height: 16),
              Text(
                widget.activity.title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return PageView.builder(
      controller: _photoPageController,
      onPageChanged: (index) {
        _currentPhotoIndex.value = index;
      },
      itemCount: _photos.length,
      itemBuilder: (context, index) {
        final photo = _photos[index];
        return _imageService.buildCachedSignedImage(
          key: ValueKey('activity_photo_${photo.id}'),
          originalUrl: photo.url,
          fit: BoxFit.cover,
          placeholder: Container(
            color: Colors.grey.shade200,
            child: const Center(child: CircularProgressIndicator()),
          ),
          errorWidget: Container(
            color: Colors.grey.shade300,
            child: const Icon(Icons.error, size: 50),
          ),
        );
      },
    );
  }

  /// 构建活动内容
  Widget _buildActivityContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 活动标题区域
        _buildActivityHeader(),

        // 活动详细信息
        _buildActivityInfo(),

        // 参与者列表
        _buildParticipantsList(),

        // 底部间距
        const SizedBox(height: 100),
      ],
    );
  }

  /// 构建活动标题区域
  Widget _buildActivityHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 活动名称和类型标签
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.activity.title,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ),
              _buildActivityTypeChip(),
            ],
          ),
          const SizedBox(height: 8),

          // 创建者和发布时间信息行
          Row(
            children: [
              Icon(Icons.person_outline, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                widget.activity.creatorName ?? '未知',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 12),
              Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                _formatDateTime(widget.activity.created),
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // 活动状态和参与统计信息行
          Row(
            children: [
              _buildActivityStatusChip(),
              const Spacer(),
              Text(
                '$_participantsCount/${widget.activity.maxParticipants} 人参与',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          ),

          // 活动描述（如果有的话）
          if (widget.activity.description.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Text(
                widget.activity.description,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                  height: 1.4,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建活动类型标签
  Widget _buildActivityTypeChip() {
    final typeInfo = _getActivityTypeInfo(widget.activity.activityType);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: typeInfo['color'].withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: typeInfo['color'].withValues(alpha: 0.3)),
      ),
      child: Text(
        typeInfo['name'],
        style: TextStyle(
          fontSize: 12,
          color: typeInfo['color'],
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// 获取活动类型信息
  Map<String, dynamic> _getActivityTypeInfo(String activityType) {
    switch (activityType) {
      case 'lure':
        return {'name': '路亚', 'color': const Color(0xFFFF9800)};
      case 'platform':
        return {'name': '台钓', 'color': const Color(0xFF9C27B0)};
      case 'night':
        return {'name': '夜钓', 'color': const Color(0xFF4CAF50)};
      case 'pond':
        return {'name': '鱼塘', 'color': const Color(0xFF2196F3)};
      case 'sea':
        return {'name': '海钓', 'color': const Color(0xFF000000)};
      default:
        return {'name': '钓鱼', 'color': const Color(0xFF4CAF50)};
    }
  }

  /// 构建活动状态标签
  Widget _buildActivityStatusChip() {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    if (widget.activity.isExpired) {
      statusColor = Colors.grey;
      statusText = '已结束';
      statusIcon = Icons.event_busy;
    } else if (widget.activity.isOngoing) {
      statusColor = Colors.green;
      statusText = '进行中';
      statusIcon = Icons.play_circle_filled;
    } else if (widget.activity.isStartingSoon) {
      statusColor = Colors.orange;
      statusText = '即将开始';
      statusIcon = Icons.schedule;
    } else {
      statusColor = Colors.blue;
      statusText = '等待中';
      statusIcon = Icons.hourglass_empty;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusIcon, size: 14, color: statusColor),
          const SizedBox(width: 4),
          Text(
            statusText,
            style: TextStyle(
              fontSize: 12,
              color: statusColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建活动详细信息
  Widget _buildActivityInfo() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        children: [
          _buildInfoRow(
            icon: Icons.schedule,
            label: '开始时间',
            value: _formatActivityTime(widget.activity.startTime),
          ),
          const Divider(height: 24),
          _buildInfoRow(
            icon: Icons.timer,
            label: '活动时长',
            value: '${widget.activity.duration.toStringAsFixed(1)} 小时',
          ),
          const Divider(height: 24),
          _buildInfoRow(
            icon: Icons.location_on,
            label: '活动地点',
            value: '点击查看位置',
            onTap: _handleLocationTap,
          ),
          const Divider(height: 24),
          _buildInfoRow(
            icon: Icons.people,
            label: '参与人数',
            value: '$_participantsCount / ${widget.activity.maxParticipants}',
          ),
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: onTap != null ? Colors.blue : Colors.black87,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (onTap != null) ...[
            const SizedBox(width: 4),
            Icon(Icons.arrow_forward_ios, size: 14, color: Colors.grey[400]),
          ],
        ],
      ),
    );
  }

  /// 构建参与者列表
  Widget _buildParticipantsList() {
    if (_participants.isEmpty) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Center(
          child: Text(
            '暂无参与者',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '参与者 ($_participantsCount)',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              children: _participants.take(5).map((participant) {
                final isCreator = participant['id'] == widget.activity.creatorId;
                final isKicked = participant['status'] == 'kicked';

                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    children: [
                      Stack(
                        children: [
                          CircleAvatar(
                            radius: 20,
                            backgroundColor: isKicked
                                ? Colors.grey.shade200
                                : isCreator
                                    ? Colors.orange.shade100
                                    : Colors.blue.shade100,
                            child: Text(
                              (participant['username'] ?? '?')[0].toUpperCase(),
                              style: TextStyle(
                                color: isKicked
                                    ? Colors.grey.shade600
                                    : isCreator
                                        ? Colors.orange.shade700
                                        : Colors.blue.shade700,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          if (isCreator)
                            Positioned(
                              right: -2,
                              bottom: -2,
                              child: Container(
                                width: 16,
                                height: 16,
                                decoration: BoxDecoration(
                                  color: Colors.orange.shade600,
                                  shape: BoxShape.circle,
                                  border: Border.all(color: Colors.white, width: 1),
                                ),
                                child: Icon(
                                  Icons.star,
                                  size: 10,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          if (isKicked)
                            Positioned(
                              right: -2,
                              bottom: -2,
                              child: Container(
                                width: 16,
                                height: 16,
                                decoration: BoxDecoration(
                                  color: Colors.red.shade600,
                                  shape: BoxShape.circle,
                                  border: Border.all(color: Colors.white, width: 1),
                                ),
                                child: Icon(
                                  Icons.block,
                                  size: 10,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  participant['username'] ?? '未知用户',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: isKicked ? Colors.grey.shade600 : null,
                                    decoration: isKicked ? TextDecoration.lineThrough : null,
                                  ),
                                ),
                                if (isCreator) ...[
                                  const SizedBox(width: 6),
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: Colors.orange.shade100,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Text(
                                      '发起人',
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Colors.orange.shade700,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                                if (isKicked) ...[
                                  const SizedBox(width: 6),
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: Colors.red.shade100,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Text(
                                      '已被移除',
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Colors.red.shade700,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                            Text(
                              isKicked
                                  ? '移除时间：${_formatJoinTime(participant['kicked_at'])}'
                                  : '加入时间：${_formatJoinTime(participant['joined_at'])}',
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
          if (_participants.length > 5)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Center(
                child: TextButton(
                  onPressed: _showAllParticipants,
                  child: Text('查看全部 ${_participants.length} 人'),
                ),
              ),
            ),
        ],
      ),
    );
  }



  /// 构建底部操作栏
  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey.shade200, width: 1)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 群聊入口（如果已参与）
            if (_isParticipating && widget.activity.groupChatId != null) ...[
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _handleOpenGroupChat,
                  icon: const Icon(Icons.chat, size: 18),
                  label: const Text('群聊'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.blue,
                    side: const BorderSide(color: Colors.blue),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
            ],

            // 主要操作按钮
            Expanded(
              flex: 2,
              child: _buildMainActionButton(),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建主要操作按钮
  Widget _buildMainActionButton() {
    final currentUser = Services.auth.currentUser;
    final isCreator = currentUser != null && currentUser.id == widget.activity.creatorId;

    if (widget.activity.isExpired) {
      return ElevatedButton(
        onPressed: null,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.grey.shade300,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text('活动已结束'),
      );
    }

    // 如果是创建者，显示管理按钮
    if (isCreator) {
      return ElevatedButton(
        onPressed: _handleManageActivity,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.orange,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text('管理活动'),
      );
    }

    if (_isParticipating) {
      return ElevatedButton(
        onPressed: _handleLeaveActivity,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text('退出活动'),
      );
    }

    if (_participantsCount >= widget.activity.maxParticipants) {
      return ElevatedButton(
        onPressed: null,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.grey.shade300,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text('人数已满'),
      );
    }

    return ElevatedButton(
      onPressed: _isJoining ? null : _handleJoinActivity,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: _isJoining
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : const Text('加入活动'),
    );
  }

  // ========== 事件处理方法 ==========

  /// 处理加入活动
  Future<void> _handleJoinActivity() async {
    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        SnackBarService.showError(context, '请先登录');
        return;
      }

      setState(() {
        _isJoining = true;
      });

      // 加入活动
      await Services.fishingActivity.joinActivity(
        widget.activity.id,
        currentUser.id,
      );

      // 重新加载数据
      await _loadParticipationStatus();
      await _loadParticipants();

      if (mounted) {
        SnackBarService.showSuccess(context, '成功加入活动！');
      }
    } catch (e) {
      debugPrint('❌ [活动详情] 加入活动失败: $e');
      if (mounted) {
        final errorMessage = e.toString();
        if (errorMessage.contains('已被移出此活动')) {
          // 为被踢用户显示特殊的错误对话框
          _showKickedUserDialog();
        } else {
          SnackBarService.showError(context, '加入活动失败：$e');
        }
      }
    } finally {
      if (mounted) {
        setState(() {
          _isJoining = false;
        });
      }
    }
  }

  /// 处理退出活动
  Future<void> _handleLeaveActivity() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('确定要退出这个活动吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) return;

      await Services.fishingActivity.leaveActivity(
        widget.activity.id,
        currentUser.id,
      );

      // 重新加载数据
      await _loadParticipationStatus();
      await _loadParticipants();

      if (mounted) {
        SnackBarService.showSuccess(context, '已退出活动');
      }
    } catch (e) {
      debugPrint('❌ [活动详情] 退出活动失败: $e');
      if (mounted) {
        SnackBarService.showError(context, '退出活动失败：$e');
      }
    }
  }

  /// 处理打开群聊
  void _handleOpenGroupChat() {
    if (widget.activity.groupChatId == null) {
      SnackBarService.showError(context, '群聊不存在');
      return;
    }

    // TODO: 导航到群聊页面
    // Navigator.pushNamed(context, '/group_chat', arguments: {
    //   'groupId': widget.activity.groupChatId,
    //   'groupName': '${widget.activity.title} 群聊',
    // });

    SnackBarService.showInfo(context, '群聊功能开发中...');
  }

  /// 处理管理活动
  void _handleManageActivity() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '管理活动',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.people, color: Colors.blue),
              title: const Text('管理参与者'),
              subtitle: Text('当前 $_participantsCount 人参与'),
              onTap: () {
                Navigator.pop(context);
                _showParticipantManagement();
              },
            ),
            ListTile(
              leading: const Icon(Icons.edit, color: Colors.green),
              title: const Text('编辑活动'),
              subtitle: const Text('修改活动信息'),
              onTap: () {
                Navigator.pop(context);
                SnackBarService.showInfo(context, '编辑功能开发中...');
              },
            ),
            ListTile(
              leading: const Icon(Icons.cancel, color: Colors.red),
              title: const Text('取消活动'),
              subtitle: const Text('取消这个活动'),
              onTap: () {
                Navigator.pop(context);
                _handleCancelActivity();
              },
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  /// 显示参与者管理
  void _showParticipantManagement() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  const Text(
                    '参与者管理',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${_participants.length} 人',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
            Expanded(
              child: ListView.builder(
                controller: scrollController,
                itemCount: _participants.length,
                itemBuilder: (context, index) {
                  final participant = _participants[index];
                  final isCreator = participant['id'] == widget.activity.creatorId;
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.blue.shade100,
                      child: Text(
                        (participant['username'] ?? '?')[0].toUpperCase(),
                        style: TextStyle(
                          color: Colors.blue.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(participant['username'] ?? '未知用户'),
                    subtitle: isCreator
                        ? Text(
                            '活动发起人',
                            style: TextStyle(color: Colors.orange.shade600),
                          )
                        : null,
                    trailing: _buildParticipantTrailing(participant, isCreator),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建参与者操作区域
  Widget _buildParticipantTrailing(Map<String, dynamic> participant, bool isCreator) {
    final isKicked = participant['status'] == 'kicked';
    final currentUser = Services.auth.currentUser;
    final isCurrentUserCreator = currentUser?.id == widget.activity.creatorId;

    if (isCreator) {
      // 活动创建者显示管理员标识
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.orange.shade100,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          '管理员',
          style: TextStyle(
            fontSize: 10,
            color: Colors.orange.shade700,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    } else if (isKicked) {
      // 被踢出的用户显示状态标识
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.red.shade100,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          '已移除',
          style: TextStyle(
            fontSize: 10,
            color: Colors.red.shade700,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    } else if (isCurrentUserCreator) {
      // 当前用户是创建者，可以管理其他参与者
      return IconButton(
        icon: Icon(
          Icons.more_vert,
          color: Colors.grey.shade600,
          size: 20,
        ),
        onPressed: () => _showParticipantOptions(participant),
      );
    } else {
      // 普通参与者，无操作权限
      return const SizedBox.shrink();
    }
  }

  /// 显示参与者操作选项 - 参考微信群管理交互
  void _showParticipantOptions(Map<String, dynamic> participant) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 用户信息头部
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 25,
                    backgroundColor: Colors.blue.shade100,
                    child: Text(
                      (participant['username'] ?? '?')[0].toUpperCase(),
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          participant['username'] ?? '未知用户',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          '加入时间：${_formatJoinTime(participant['joined_at'])}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const Divider(height: 20),

            // 操作选项
            ListTile(
              leading: Icon(Icons.remove_circle_outline, color: Colors.red.shade600),
              title: Text(
                '移出活动',
                style: TextStyle(color: Colors.red.shade600),
              ),
              subtitle: const Text('该用户将无法重新加入'),
              onTap: () {
                Navigator.pop(context);
                _handleKickParticipant(participant);
              },
            ),

            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  /// 格式化加入时间
  String _formatJoinTime(String? joinTimeStr) {
    if (joinTimeStr == null || joinTimeStr.isEmpty) {
      return '未知';
    }

    try {
      final joinTime = DateTime.parse(joinTimeStr);
      final now = DateTime.now();
      final difference = now.difference(joinTime);

      if (difference.inDays > 0) {
        return '${difference.inDays}天前';
      } else if (difference.inHours > 0) {
        return '${difference.inHours}小时前';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes}分钟前';
      } else {
        return '刚刚';
      }
    } catch (e) {
      return '未知';
    }
  }

  /// 处理移除参与者 - 产品级实现
  void _handleKickParticipant(Map<String, dynamic> participant) async {
    final currentUser = Services.auth.currentUser;
    if (currentUser == null) {
      SnackBarService.showError(context, '请先登录');
      return;
    }

    // 显示确认对话框，参考微信群管理的交互模式
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning_amber_rounded, color: Colors.orange.shade600, size: 24),
            const SizedBox(width: 8),
            const Text('移除成员'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('确定要将 ${participant['username']} 移出活动吗？'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, size: 16, color: Colors.orange.shade700),
                      const SizedBox(width: 4),
                      Text(
                        '移除后的影响：',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.orange.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  Text(
                    '• 该用户将被移出活动和群聊\n• 无法重新加入此活动\n• 无法查看群聊消息',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.orange.shade700,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('确定移除'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      // 显示加载状态
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // 调用踢出参与者的API
      await Services.fishingActivity.kickParticipant(
        widget.activity.id,
        participant['id'],
        currentUser.id,
      );

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 重新加载参与者列表
      await _loadParticipants();

      if (mounted) {
        // 显示成功提示，使用更友好的消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('已将 ${participant['username']} 移出活动'),
                ),
              ],
            ),
            backgroundColor: Colors.green.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            duration: const Duration(seconds: 3),
          ),
        );

        // 延迟关闭参与者管理页面，让用户看到反馈
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            Navigator.of(context).pop();
          }
        });
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      debugPrint('❌ [活动详情] 移除参与者失败: $e');
      if (mounted) {
        String errorMessage = '移除失败';

        // 根据错误类型提供更友好的提示
        if (e.toString().contains('该用户未参与此活动')) {
          errorMessage = '该用户可能已经退出活动';
        } else if (e.toString().contains('只有活动创建者可以管理参与者')) {
          errorMessage = '只有活动创建者可以移除参与者';
        } else if (e.toString().contains('不能踢出自己')) {
          errorMessage = '不能移除自己';
        } else if (e.toString().contains('404')) {
          errorMessage = '用户信息不存在，可能已被移除';
        } else {
          errorMessage = '移除失败，请重试';
        }

        SnackBarService.showError(context, errorMessage);

        // 如果是用户已经不存在的情况，刷新参与者列表
        if (e.toString().contains('404') || e.toString().contains('该用户未参与此活动')) {
          await _loadParticipants();
        }
      }
    }
  }

  /// 处理取消活动
  void _handleCancelActivity() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认取消'),
        content: const Text('确定要取消这个活动吗？取消后无法恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('不取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('确定取消'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final success = await Services.fishingActivity.deleteActivity(widget.activity.id);
      if (success && mounted) {
        SnackBarService.showSuccess(context, '活动已取消');

        // 通知地图页面刷新数据
        _notifyMapRefresh();

        // 返回上一页，并传递刷新标志
        Navigator.of(context).pop(true); // 返回true表示需要刷新
      } else if (mounted) {
        SnackBarService.showError(context, '取消活动失败');
      }
    } catch (e) {
      debugPrint('❌ [活动详情] 取消活动失败: $e');
      if (mounted) {
        SnackBarService.showError(context, '取消活动失败：$e');
      }
    }
  }

  /// 通知地图页面刷新数据 - 产品级实现
  void _notifyMapRefresh() {
    try {
      debugPrint('📢 [活动详情] 通知数据变化');

      // 清理缓存会自动触发数据变化通知
      Services.fishingActivity.clearCache();

      debugPrint('✅ [活动详情] 数据变化通知完成');
    } catch (e) {
      debugPrint('⚠️ [活动详情] 通知数据变化失败: $e');
    }
  }

  /// 显示活动不存在的对话框
  void _showActivityNotFoundDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red.shade600, size: 24),
            const SizedBox(width: 8),
            const Text('活动不存在'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('很抱歉，您要查看的活动不存在或已被删除。'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.lightbulb_outline, size: 16, color: Colors.blue.shade700),
                      const SizedBox(width: 4),
                      Text(
                        '可能的原因：',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  Text(
                    '• 活动已被创建者取消\n• 活动链接已过期\n• 网络连接问题',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.shade700,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(); // 关闭对话框
              Navigator.of(context).pop(); // 返回到地图页面
            },
            child: const Text('返回地图'),
          ),
        ],
      ),
    );
  }

  /// 显示被踢用户的友好提示对话框
  void _showKickedUserDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.block, color: Colors.red.shade600, size: 24),
            const SizedBox(width: 8),
            const Text('无法加入活动'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('很抱歉，您已被移出此活动，无法重新加入。'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.lightbulb_outline, size: 16, color: Colors.blue.shade700),
                      const SizedBox(width: 4),
                      Text(
                        '建议：',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  Text(
                    '• 您可以浏览其他感兴趣的活动\n• 或者创建自己的钓鱼活动\n• 如有疑问，可联系活动发起人',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.shade700,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('我知道了'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // 返回到地图页面
            },
            child: const Text('浏览其他活动'),
          ),
        ],
      ),
    );
  }

  /// 处理分享
  void _handleShare() {
    // TODO: 实现分享功能
    SnackBarService.showInfo(context, '分享功能开发中...');
  }

  /// 处理位置点击
  void _handleLocationTap() {
    // TODO: 导航到地图页面显示活动位置
    SnackBarService.showInfo(context, '地图导航功能开发中...');
  }

  /// 显示所有参与者
  void _showAllParticipants() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  const Text(
                    '参与者列表',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${_participants.length} 人',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
            Expanded(
              child: ListView.builder(
                controller: scrollController,
                itemCount: _participants.length,
                itemBuilder: (context, index) {
                  final participant = _participants[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.blue.shade100,
                      child: Text(
                        (participant['username'] ?? '?')[0].toUpperCase(),
                        style: TextStyle(
                          color: Colors.blue.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(participant['username'] ?? '未知用户'),
                    subtitle: participant['id'] == widget.activity.creatorId
                        ? Text(
                            '活动发起人',
                            style: TextStyle(color: Colors.orange.shade600),
                          )
                        : null,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // ========== 工具方法 ==========

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = dateTime.difference(now);

    if (difference.inDays > 0) {
      return '${difference.inDays}天后';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时后';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟后';
    } else if (difference.inSeconds > 0) {
      return '即将开始';
    } else {
      return '${(-difference.inHours)}小时前';
    }
  }

  /// 格式化活动时间
  String _formatActivityTime(DateTime dateTime) {
    return '${dateTime.month}月${dateTime.day}日 ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
