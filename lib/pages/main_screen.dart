import 'package:flutter/material.dart';
import '../widgets/snackbar.dart';
import 'package:flutter/services.dart';
import 'map_page.dart';
import 'recommend_page.dart';
import 'message/message_main_page.dart';
import 'profile_page.dart';
import 'publish_dynamic_page.dart';
import '../services/service_locator.dart';
import '../services/map_navigation_service.dart';

import '../theme/app_theme_manager.dart';
import '../widgets/custom_bottom_navigation_bar.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen>
    with WidgetsBindingObserver, AutomaticKeepAliveClientMixin {
  int _currentIndex = 0;
  bool _showBottomNavigationBar = true; // 控制底部导航栏显示
  bool _hasProcessedNavigationArgs = false; // 防止重复处理导航参数

  // GlobalKey用于访问MapPage实例
  final GlobalKey<State<MapPage>> _mapPageKey = GlobalKey<State<MapPage>>();

  // 页面列表
  late final List<Widget> _pages;

  // 使用新的服务架构
  // 通过Services便捷访问器访问认证服务

  @override
  bool get wantKeepAlive => true; // 保持MainScreen状态，避免应用恢复时重建

  @override
  void initState() {
    super.initState();

    // 添加应用生命周期监听
    WidgetsBinding.instance.addObserver(this);

    // 直接创建页面列表
    _pages = [
      MapPage(
        key: _mapPageKey,
        onBottomNavigationBarVisibilityChanged: (visible) {
          // 只有当前显示的是MapPage（index=0）时才处理导航栏隐藏
          if (_currentIndex == 0) {
            setState(() {
              _showBottomNavigationBar = visible;
            });
          }
        },
      ),
      const RecommendPage(),
      const MessageMainPage(),
      const ProfilePage(),
    ];
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 处理导航参数（只在有参数且未处理过时处理，避免重复触发）
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args != null && !_hasProcessedNavigationArgs) {
      debugPrint('🗺️ [MainScreen] 首次处理导航参数');
      _hasProcessedNavigationArgs = true;
      _handleNavigationArguments();
    } else if (args == null) {
      debugPrint('🗺️ [MainScreen] 无导航参数，跳过处理');
    } else {
      debugPrint('🗺️ [MainScreen] 导航参数已处理过，跳过重复处理');
    }
  }

  /// 处理导航参数
  void _handleNavigationArguments() {
    debugPrint('🗺️ [MainScreen] ========== 开始处理导航参数 ==========');

    final args = ModalRoute.of(context)?.settings.arguments;
    debugPrint('🗺️ [MainScreen] 原始参数: $args');
    debugPrint('🗺️ [MainScreen] 参数类型: ${args.runtimeType}');

    if (args is Map<String, dynamic>) {
      debugPrint('🗺️ [MainScreen] 参数解析成功，内容: $args');

      // 处理初始页面索引
      final initialIndex = args['initialIndex'] as int?;
      debugPrint('🗺️ [MainScreen] 初始页面索引: $initialIndex');
      debugPrint('🗺️ [MainScreen] 当前页面索引: $_currentIndex');

      if (initialIndex != null && initialIndex != _currentIndex) {
        debugPrint('🗺️ [MainScreen] 切换页面索引: $_currentIndex -> $initialIndex');
        setState(() {
          _currentIndex = initialIndex;
        });
        debugPrint('🗺️ [MainScreen] 页面索引切换完成');
      }

      // 处理统一导航参数
      final navigationParamsJson = args['navigationParams'] as Map<String, dynamic>?;
      if (navigationParamsJson != null) {
        debugPrint('🗺️ [MainScreen] 发现统一导航参数');
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final params = MapNavigationParams.fromJson(navigationParamsJson);
          _executeMapNavigation(params);
        });
      }

      // 兼容旧的导航参数格式
      final spotLocation = args['spotLocation'];
      final spotId = args['spotId'] as String?;

      debugPrint('🗺️ [MainScreen] 钓点位置: $spotLocation');
      debugPrint('🗺️ [MainScreen] 钓点位置类型: ${spotLocation.runtimeType}');
      debugPrint('🗺️ [MainScreen] 钓点ID: $spotId');

      if (spotLocation != null && spotId != null && navigationParamsJson == null) {
        debugPrint('🗺️ [MainScreen] 准备调用地图导航，延迟执行');
        // 延迟调用，确保MapPage已经构建完成
        WidgetsBinding.instance.addPostFrameCallback((_) {
          debugPrint('🗺️ [MainScreen] PostFrameCallback 执行，开始调用地图导航');
          _navigateToSpotOnMap(spotLocation, spotId);
        });
      } else {
        debugPrint('🗺️ [MainScreen] 钓点位置或ID为空，跳过地图导航');
      }
    } else {
      debugPrint('🗺️ [MainScreen] 无导航参数或参数格式不正确');
    }

    debugPrint('🗺️ [MainScreen] ========== 导航参数处理完成 ==========');
  }

  /// 执行统一地图导航
  void _executeMapNavigation(MapNavigationParams params) {
    debugPrint('🗺️ [MainScreen] 执行统一地图导航');
    final mapPageState = _mapPageKey.currentState;
    if (mapPageState != null) {
      try {
        (mapPageState as dynamic).executeNavigation(params);
        debugPrint('🗺️ [MainScreen] 统一导航调用成功');
      } catch (e) {
        debugPrint('❌ [MainScreen] 执行统一导航失败: $e');
      }
    } else {
      debugPrint('❌ [MainScreen] 无法获取MapPage实例');
    }
  }

  /// 在地图上导航到指定钓点（已废弃，请使用统一导航服务）
  @deprecated
  void _navigateToSpotOnMap(dynamic spotLocation, String spotId) {
    debugPrint('🗺️ [MainScreen] ========== 开始调用地图导航 ==========');
    debugPrint('🗺️ [MainScreen] 目标钓点ID: $spotId');
    debugPrint('🗺️ [MainScreen] 目标位置: $spotLocation');
    debugPrint('🗺️ [MainScreen] 位置类型: ${spotLocation.runtimeType}');

    final mapPageState = _mapPageKey.currentState;
    debugPrint('🗺️ [MainScreen] MapPage状态: $mapPageState');
    debugPrint('🗺️ [MainScreen] MapPageKey: $_mapPageKey');

    if (mapPageState != null) {
      try {
        debugPrint('🗺️ [MainScreen] MapPage实例获取成功，准备调用navigateToSpot方法');
        debugPrint('🗺️ [MainScreen] MapPage实例类型: ${mapPageState.runtimeType}');

        // 通过dynamic调用navigateToSpot方法
        (mapPageState as dynamic).navigateToSpot(spotLocation, spotId);

        debugPrint('🗺️ [MainScreen] navigateToSpot方法调用完成');
      } catch (e) {
        debugPrint('❌ [MainScreen] 调用MapPage导航方法失败: $e');
        debugPrint('❌ [MainScreen] 错误堆栈: ${StackTrace.current}');
      }
    } else {
      debugPrint('❌ [MainScreen] 无法获取MapPage实例');
      debugPrint('❌ [MainScreen] 当前页面索引: $_currentIndex');
      debugPrint('❌ [MainScreen] 页面列表长度: ${_pages.length}');

      // 尝试延迟重试
      debugPrint('🗺️ [MainScreen] 尝试延迟重试...');
      Future.delayed(const Duration(milliseconds: 500), () {
        final retryMapPageState = _mapPageKey.currentState;
        debugPrint('🗺️ [MainScreen] 重试获取MapPage状态: $retryMapPageState');
        if (retryMapPageState != null) {
          try {
            debugPrint('🗺️ [MainScreen] 重试调用navigateToSpot方法');
            (retryMapPageState as dynamic).navigateToSpot(spotLocation, spotId);
            debugPrint('🗺️ [MainScreen] 重试调用成功');
          } catch (e) {
            debugPrint('❌ [MainScreen] 重试调用失败: $e');
          }
        } else {
          debugPrint('❌ [MainScreen] 重试仍然无法获取MapPage实例');
        }
      });
    }

    debugPrint('🗺️ [MainScreen] ========== 地图导航调用完成 ==========');
  }

  /// 处理底部导航栏点击事件
  void _handleBottomNavTap(int index) {
    if (index == -1) {
      // 点击添加按钮
      _handleAddButtonTap();
      return;
    }

    // 处理正常的页面切换
    setState(() {
      final oldIndex = _currentIndex;
      _currentIndex = index;

      // 如果从MapPage切换到其他页面，确保显示导航栏
      if (oldIndex == 0 && index != 0) {
        _showBottomNavigationBar = true;
      }

      // 如果切换到地图页面，检查是否需要刷新数据
      if (index == 0 && oldIndex != 0) {
        debugPrint('🔄 [MainScreen] 切换到地图页面，检查更新');
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final mapPageState = _mapPageKey.currentState;
          if (mapPageState != null) {
            (mapPageState as dynamic).checkForUpdatesOnReturn();
          }
        });
      }
    });
  }

  /// 处理添加按钮点击事件
  void _handleAddButtonTap() {
    switch (_currentIndex) {
      case 0: // 地图页面
        _showAddModeSelectionDialog();
        break;
      case 1: // 推荐页面
        _navigateToPublishPost();
        break;
      case 2: // 信息页面
        _showMessageAddOptions();
        break;
      case 3: // 我的页面
        _showMyPageAddOptions();
        break;
    }
  }

  /// 导航到发布动态页面
  void _navigateToPublishPost() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const PublishDynamicPage()),
    ).then((result) {
      if (result == true) {
        // 发布成功，可以刷新推荐页面
        debugPrint('动态发布成功');
      }
    });
  }

  /// 显示信息页面添加选项
  void _showMessageAddOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
            ),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 36,
                    height: 4,
                    margin: const EdgeInsets.only(top: 8, bottom: 16),
                    decoration: BoxDecoration(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  _buildAddOptionItem(
                    icon: Icons.person_add,
                    title: '添加好友',
                    subtitle: '通过搜索添加新朋友',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: 实现添加好友功能
                      SnackBarService.showInfo(context, '添加好友功能待实现');
                    },
                  ),
                  _buildAddOptionItem(
                    icon: Icons.group_add,
                    title: '添加群聊',
                    subtitle: '创建或加入群组聊天',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: 实现添加群聊功能
                      SnackBarService.showInfo(context, '添加群聊功能待实现');
                    },
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
    );
  }

  /// 显示我的页面添加选项
  void _showMyPageAddOptions() {
    SnackBarService.showInfo(context, '我的页面暂无添加功能');
  }

  /// 显示添加模式选择底部面板
  void _showAddModeSelectionDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
            ),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 顶部拖拽指示器
                  Container(
                    width: 36,
                    height: 4,
                    margin: const EdgeInsets.only(top: 8, bottom: 16),
                    decoration: BoxDecoration(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  // // 标题
                  // Padding(
                  //   padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                  //   child: Text(
                  //     '选择发布类型',
                  //     style: TextStyle(
                  //       fontSize: 18,
                  //       fontWeight: FontWeight.w600,
                  //       color: Theme.of(context).colorScheme.onSurface,
                  //     ),
                  //   ),
                  // ),
                  const SizedBox(height: 8),
                  // 添加钓点选项
                  _buildAddOptionItem(
                    icon: Icons.location_on,
                    title: '发布新钓点',
                    subtitle: '分享您发现的野钓钓点或者鱼塘',
                    onTap: () {
                      _handleAddSpotSelection(context);
                    },
                  ),
                  // 一起钓鱼活动选项
                  _buildAddOptionItem(
                    icon: Icons.group,
                    title: '一起去钓鱼',
                    subtitle: '找钓鱼搭子一起去去钓鱼吧~',
                    onTap: () {
                      _handleAddActivitySelection(context);
                    },
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
    );
  }

  /// 触发添加钓点模式
  void _triggerAddSpotMode() {
    debugPrint('🔍 [MainScreen] 触发添加钓点模式，当前页面索引: $_currentIndex');
    
    // 确保当前在主页，如果不在主页则先切换到主页
    if (_currentIndex != 0) {
      debugPrint('🔍 [MainScreen] 不在地图页面，切换到地图页面');
      setState(() {
        _currentIndex = 0;
      });
      // 等待页面切换完成后再调用添加钓点功能
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _callMapPageAddSpot();
      });
    } else {
      // 已经在主页，直接调用添加钓点功能
      debugPrint('🔍 [MainScreen] 已在地图页面，直接调用添加钓点功能');
      _callMapPageAddSpot();
    }
  }

  /// 触发添加活动模式
  void _triggerAddActivityMode() {
    debugPrint('🔍 [MainScreen] 触发添加活动模式');

    // 确保当前在主页，如果不在主页则先切换到主页
    if (_currentIndex != 0) {
      setState(() {
        _currentIndex = 0;
      });
      // 等待页面切换完成后再调用添加活动功能
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _callMapPageAddActivity();
      });
    } else {
      // 已经在主页，直接调用添加活动功能
      _callMapPageAddActivity();
    }
  }

  /// 调用MapPage的添加钓点功能
  void _callMapPageAddSpot() {
    final mapPageState = _mapPageKey.currentState;
    if (mapPageState != null) {
      try {
        // 通过dynamic调用triggerAddSpotMode方法
        (mapPageState as dynamic).triggerAddSpotMode();
      } catch (e) {
        debugPrint('调用MapPage方法时出错: $e');
      }
    }
  }

  /// 调用MapPage的添加活动功能
  void _callMapPageAddActivity() {
    final mapPageState = _mapPageKey.currentState;
    if (mapPageState != null) {
      // 通过dynamic调用triggerAddActivityMode方法
      (mapPageState as dynamic).triggerAddActivityMode();
      debugPrint('🔍 [MainScreen] 已调用MapPage的添加活动功能');
    } else {
      debugPrint('🔍 [MainScreen] 无法获取MapPage实例');
    }
  }

  @override
  void dispose() {
    // 移除应用生命周期监听
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        // 应用从后台恢复时，静默尝试刷新登录状态
        _refreshAuthStateOnResume();
        break;
      case AppLifecycleState.paused:
        // 应用切换到后台时的处理
        debugPrint('应用切换到后台');
        break;
      case AppLifecycleState.detached:
        // 应用即将终止时的处理
        debugPrint('应用即将终止');
        break;
      case AppLifecycleState.inactive:
        // 应用失去焦点时的处理
        debugPrint('应用失去焦点');
        break;
      case AppLifecycleState.hidden:
        // 应用被隐藏时的处理
        debugPrint('应用被隐藏');
        break;
    }
  }

  /// 应用恢复时刷新认证状态
  Future<void> _refreshAuthStateOnResume() async {
    try {
      // 静默尝试刷新登录状态，不显示任何错误提示
      if (!Services.auth.isLoggedIn) {
        await Services.auth.initialize();
      }
    } catch (e) {
      // 静默处理错误，不影响用户体验
      debugPrint('应用恢复时刷新认证状态失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用，用于AutomaticKeepAliveClientMixin

    // 监听主题变化，确保底部导航栏能实时更新
    return ListenableBuilder(
      listenable: AppThemeManager.instance,
      builder: (context, child) {
        return PopScope(
          canPop: false, // 阻止默认的返回行为
          onPopInvokedWithResult: (didPop, result) {
            if (!didPop) {
              // 检查当前是否在地图页面的分屏模式
              if (_currentIndex == 0) {
                final mapPageState = _mapPageKey.currentState;
                if (mapPageState != null) {
                  final isSplitScreenMode =
                      (mapPageState as dynamic).isSplitScreenMode;
                  if (isSplitScreenMode == true) {
                    return; // 让MapPage处理返回键
                  }
                }
              }
              // 按返回键时将应用切换到后台，而不是退出
              _moveAppToBackground();
            }
          },
          child: Scaffold(
            body: IndexedStack(index: _currentIndex, children: _pages),
            bottomNavigationBar:
                _showBottomNavigationBar ? _buildBottomNavigationBar() : null,
          ),
        );
      },
    );
  }

  /// 构建底部导航栏
  Widget _buildBottomNavigationBar() {
    return CustomBottomNavigationBar(
      currentIndex: _currentIndex,
      onTap: _handleBottomNavTap,
    );
  }

  /// 构建添加选项项
  Widget _buildAddOptionItem({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            // 图标容器
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                size: 24,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
            const SizedBox(width: 16),
            // 文本内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            // 右箭头
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  /// 处理添加钓点选择（带平滑过渡）
  void _handleAddSpotSelection(BuildContext context) async {
    // 先关闭 Bottom Sheet，等待动画完成
    Navigator.pop(context);

    // 等待 Bottom Sheet 关闭动画完成（通常是 300ms）
    await Future.delayed(const Duration(milliseconds: 350));

    // 然后触发添加钓点模式
    _triggerAddSpotMode();
  }

  /// 处理添加活动选择（带平滑过渡）
  void _handleAddActivitySelection(BuildContext context) async {
    // 先关闭 Bottom Sheet，等待动画完成
    Navigator.pop(context);

    // 等待 Bottom Sheet 关闭动画完成（通常是 300ms）
    await Future.delayed(const Duration(milliseconds: 350));

    // 然后触发添加活动模式
    _triggerAddActivityMode();
  }

  /// 将应用切换到后台
  void _moveAppToBackground() {
    SystemNavigator.pop();
  }
}
