import 'dart:async';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../models/fishing_activity.dart';
import '../widgets/add_spot_form/image_upload_widget.dart';
import '../widgets/add_spot_form/image_upload_manager.dart';
import '../services/service_locator.dart';

/// 添加活动页面的状态管理
class AddActivityProvider extends ChangeNotifier {
  // 表单控制器
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // 图片上传管理
  final ImageUploadManager imageUploadManager = ImageUploadManager();

  // 表单状态
  String _selectedFishType = 'carp';
  String _selectedActivityType = 'lure';
  DateTime? _selectedFishingTime;
  double _selectedDuration = 2.0;
  final List<ImageUploadItem> _selectedImages = [];

  // 位置状态
  LatLng? _activityLocation;
  Position? _publishLocation;

  // UI状态
  bool _isSubmitting = false;
  bool _isLoadingCache = false;
  String? _errorMessage;

  // 缓存相关
  static const String _cacheKey = 'add_activity_form_cache';
  Timer? _autoSaveTimer;

  // Getters
  String get selectedFishType => _selectedFishType;
  String get selectedActivityType => _selectedActivityType;
  DateTime? get selectedFishingTime => _selectedFishingTime;
  double get selectedDuration => _selectedDuration;
  List<ImageUploadItem> get selectedImages =>
      List.unmodifiable(_selectedImages);
  LatLng? get activityLocation => _activityLocation;
  Position? get publishLocation => _publishLocation;
  bool get isSubmitting => _isSubmitting;
  bool get isLoadingCache => _isLoadingCache;
  String? get errorMessage => _errorMessage;

  /// 初始化
  void initialize({required LatLng location, String? suggestedName}) {
    _activityLocation = location;

    // 设置建议名称
    if (suggestedName != null && suggestedName.trim().isNotEmpty) {
      nameController.text = suggestedName;
    }

    // 设置默认钓鱼时间（1小时后）
    _selectedFishingTime = DateTime.now().add(const Duration(hours: 1));

    // 启动自动保存
    _startAutoSave();

    // 加载缓存数据
    _loadCachedData();

    notifyListeners();
  }

  /// 更新鱼种选择
  void updateFishType(String fishType) {
    _selectedFishType = fishType;
    _saveToCache();
    notifyListeners();
  }

  /// 更新活动类型选择
  void updateActivityType(String activityType) {
    _selectedActivityType = activityType;
    _saveToCache();
    notifyListeners();
  }

  /// 更新钓鱼时间
  void updateFishingTime(DateTime time, double duration) {
    _selectedFishingTime = time;
    _selectedDuration = duration;
    _saveToCache();
    notifyListeners();
  }

  /// 更新活动位置
  void updateActivityLocation(LatLng location) {
    _activityLocation = location;
    _saveToCache();
    notifyListeners();
  }

  /// 更新发布位置
  void updatePublishLocation(Position? position) {
    _publishLocation = position;
    notifyListeners();
  }

  /// 添加图片
  void addImages(List<ImageUploadItem> newImages) {
    _selectedImages.addAll(newImages);
    _saveToCache();
    notifyListeners();

    // 开始上传图片（与钓点发布页面保持一致）
    imageUploadManager.uploadSelectedImages(newImages, (updatedItem) {
      // 找到对应的item并更新其状态
      final index = _selectedImages.indexWhere(
        (item) => item.file.path == updatedItem.file.path,
      );
      if (index != -1) {
        _selectedImages[index] = updatedItem;
      }
      // 图片状态更新时触发UI刷新
      notifyListeners();
    });
  }

  /// 移除图片
  void removeImage(int index) {
    if (index >= 0 && index < _selectedImages.length) {
      _selectedImages.removeAt(index);
      _saveToCache();
      notifyListeners();
    }
  }

  /// 取消图片上传
  void cancelImageUpload(int index) {
    imageUploadManager.cancelImageUpload(_selectedImages, index);
    notifyListeners();
  }

  /// 重试图片上传
  void retryImageUpload(int index) {
    imageUploadManager.retryImageUpload(_selectedImages, index, (updatedItem) {
      // 找到对应的item并更新其状态
      final itemIndex = _selectedImages.indexWhere(
        (item) => item.file.path == updatedItem.file.path,
      );
      if (itemIndex != -1) {
        _selectedImages[itemIndex] = updatedItem;
      }
      // 图片状态更新时触发UI刷新
      notifyListeners();
    });
  }

  /// 检查是否可以提交
  bool canSubmit() {
    return !_isSubmitting &&
        !imageUploadManager.hasUploadingImages(_selectedImages) &&
        nameController.text.trim().isNotEmpty &&
        _selectedFishingTime != null &&
        _selectedFishingTime!.isAfter(DateTime.now()) &&
        !_selectedFishingTime!.isAfter(
          DateTime.now().add(const Duration(days: 30)),
        ) &&
        _activityLocation != null &&
        (descriptionController.text.trim().isEmpty ||
            descriptionController.text.trim().length >= 15);
  }

  /// 获取无法提交的原因
  String? getSubmitDisabledReason() {
    if (_isSubmitting) {
      return '正在发布中，请稍候...';
    }

    if (imageUploadManager.hasUploadingImages(_selectedImages)) {
      final uploadingCount =
          _selectedImages.where((img) => img.isUploading).length;
      return '还有$uploadingCount张图片正在上传，请等待完成';
    }

    if (nameController.text.trim().isEmpty) {
      return '请输入活动名称';
    }

    if (_selectedFishingTime == null) {
      return '请选择钓鱼时间';
    }

    if (_selectedFishingTime != null &&
        _selectedFishingTime!.isBefore(DateTime.now())) {
      return '钓鱼时间不能是过去的时间';
    }

    if (_selectedFishingTime != null) {
      final maxFutureTime = DateTime.now().add(const Duration(days: 30));
      if (_selectedFishingTime!.isAfter(maxFutureTime)) {
        return '钓鱼时间不能超过30天后';
      }
    }

    // 检查描述长度（与钓点页面保持一致）
    final description = descriptionController.text.trim();
    if (description.isNotEmpty && description.length < 15) {
      return '活动描述至少需要15个字符';
    }

    if (_activityLocation == null) {
      return '活动位置信息缺失';
    }

    // 如果所有条件都满足，返回null表示可以提交
    return null;
  }

  /// 获取提交按钮的状态文本
  String getSubmitButtonText() {
    if (_isSubmitting) {
      return '发布中...';
    }

    if (imageUploadManager.hasUploadingImages(_selectedImages)) {
      return '图片上传中...';
    }

    return '一起去钓鱼';
  }

  /// 验证表单
  bool validateForm() {
    _clearError();

    if (!formKey.currentState!.validate()) {
      return false;
    }

    if (_selectedFishingTime == null) {
      _setError('请选择钓鱼时间');
      return false;
    }

    if (_selectedFishingTime!.isBefore(DateTime.now())) {
      _setError('钓鱼时间不能是过去的时间');
      return false;
    }

    // 检查时间不能超过30天
    final maxFutureTime = DateTime.now().add(const Duration(days: 30));
    if (_selectedFishingTime!.isAfter(maxFutureTime)) {
      _setError('钓鱼时间不能超过30天后');
      return false;
    }

    if (imageUploadManager.hasUploadingImages(_selectedImages)) {
      _setError('请等待图片上传完成');
      return false;
    }

    return true;
  }

  /// 创建活动对象
  FishingActivity createActivity() {
    final user = Services.auth.currentUser;
    if (user == null) {
      throw Exception('用户未登录');
    }

    // 处理图片数据
    Map<String, dynamic>? imagesData;
    if (_selectedImages.isNotEmpty) {
      final completedImages =
          _selectedImages
              .where((img) => img.isCompleted && img.uploadedUrl != null)
              .toList();

      if (completedImages.isNotEmpty) {
        imagesData = {
          'images':
              completedImages
                  .map(
                    (img) => {
                      'url': img.uploadedUrl,
                      'thumbnail_url': img.thumbnailUrl,
                      'is_from_camera': img.isFromCamera,
                    },
                  )
                  .toList(),
          'count': completedImages.length,
        };
      }
    }

    // 生成描述，确保至少15个字
    String description = descriptionController.text.trim();
    if (description.isEmpty || description.length < 15) {
      final activityTypeName = _getActivityTypeDisplayName(
        _selectedActivityType,
      );
      description =
          '在${nameController.text.trim()}进行${activityTypeName}活动，期待与钓友们一起享受钓鱼的乐趣！欢迎大家踊跃参与。';
    }

    return FishingActivity(
      id: '', // 将由服务器生成
      title: nameController.text.trim(),
      description: description,
      location: {
        'lat': _activityLocation!.latitude,
        'lon': _activityLocation!.longitude,
      },
      startTime: _selectedFishingTime!,
      duration: _selectedDuration,
      maxParticipants: 10, // 可以后续改为可配置
      currentParticipants: 1,
      creatorId: user.id,
      creatorName: user.username,
      created: DateTime.now(),
      updated: DateTime.now(),
      images: imagesData,
      activityType: _selectedActivityType,
    );
  }

  /// 获取活动类型显示名称
  String _getActivityTypeDisplayName(String activityType) {
    switch (activityType) {
      case 'lure':
        return '路亚';
      case 'platform':
        return '台钓';
      case 'night':
        return '夜钓';
      case 'pond':
        return '鱼塘';
      case 'sea':
        return '海钓';
      default:
        return '钓鱼';
    }
  }

  /// 设置提交状态
  void setSubmitting(bool submitting) {
    _isSubmitting = submitting;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// 重置表单
  void resetForm() {
    nameController.clear();
    descriptionController.clear();
    _selectedFishType = 'carp';
    _selectedActivityType = 'lure';
    _selectedFishingTime = DateTime.now().add(const Duration(hours: 1));
    _selectedDuration = 2.0;
    _selectedImages.clear();
    _publishLocation = null;
    _isSubmitting = false;
    _errorMessage = null;

    _clearCache();
    notifyListeners();
  }

  /// 启动自动保存
  void _startAutoSave() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _saveToCache();
    });
  }

  /// 保存到缓存
  void _saveToCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheData = {
        'name': nameController.text,
        'description': descriptionController.text,
        'fishType': _selectedFishType,
        'activityType': _selectedActivityType,
        'fishingTime': _selectedFishingTime?.toIso8601String(),
        'duration': _selectedDuration,
        'location':
            _activityLocation != null
                ? {
                  'lat': _activityLocation!.latitude,
                  'lon': _activityLocation!.longitude,
                }
                : null,
        'timestamp': DateTime.now().toIso8601String(),
      };

      await prefs.setString(_cacheKey, jsonEncode(cacheData));
    } catch (e) {
      debugPrint('保存表单缓存失败: $e');
    }
  }

  /// 加载缓存数据
  void _loadCachedData() async {
    _isLoadingCache = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheString = prefs.getString(_cacheKey);

      if (cacheString != null) {
        final cacheData = jsonDecode(cacheString) as Map<String, dynamic>;
        final timestamp = DateTime.parse(cacheData['timestamp']);

        // 只加载24小时内的缓存
        if (DateTime.now().difference(timestamp).inHours < 24) {
          if (nameController.text.isEmpty && cacheData['name'] != null) {
            nameController.text = cacheData['name'];
          }
          if (descriptionController.text.isEmpty &&
              cacheData['description'] != null) {
            descriptionController.text = cacheData['description'];
          }

          _selectedFishType = cacheData['fishType'] ?? 'carp';
          _selectedActivityType = cacheData['activityType'] ?? 'lure';
          _selectedDuration = cacheData['duration'] ?? 2.0;

          if (cacheData['fishingTime'] != null) {
            final cachedTime = DateTime.parse(cacheData['fishingTime']);
            // 只有缓存的时间在未来才使用
            if (cachedTime.isAfter(DateTime.now())) {
              _selectedFishingTime = cachedTime;
            }
          }
        }
      }
    } catch (e) {
      debugPrint('加载表单缓存失败: $e');
    } finally {
      _isLoadingCache = false;
      notifyListeners();
    }
  }

  /// 清除缓存
  void _clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
    } catch (e) {
      debugPrint('清除表单缓存失败: $e');
    }
  }

  @override
  void dispose() {
    nameController.dispose();
    descriptionController.dispose();
    _autoSaveTimer?.cancel();
    super.dispose();
  }
}
