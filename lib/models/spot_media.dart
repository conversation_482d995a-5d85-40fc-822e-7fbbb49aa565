import 'dart:io';

/// 媒体类型枚举
enum MediaType { image, video }

/// 钓点媒体项目模型
///
/// 统一管理钓点的图片和视频内容
/// 支持边下载边播放和缓存管理
class SpotMediaItem {
  /// 唯一标识符
  final String id;

  /// 钓点ID
  final String spotId;

  /// 媒体类型
  final MediaType type;

  /// 媒体URL
  final String url;

  /// 缩略图URL
  final String? thumbnailUrl;

  /// 文件名
  final String filename;

  /// 描述
  final String? description;

  /// 排序顺序
  final int sortOrder;

  /// 文件大小（字节）
  final int? fileSize;

  /// MIME类型
  final String? mimeType;

  /// 视频时长（仅视频有效）
  final Duration? duration;

  /// 创建时间
  final DateTime created;

  /// 更新时间
  final DateTime updated;

  // 运行时状态（不持久化）
  /// 本地缓存文件
  File? cachedFile;

  /// 缓存的缩略图文件
  File? cachedThumbnailFile;

  /// 预签名URL
  String? signedUrl;

  /// 预签名缩略图URL
  String? signedThumbnailUrl;

  /// 是否正在加载
  bool isLoading;

  /// 下载进度 (0.0 - 1.0)
  double downloadProgress;

  /// 是否加载失败
  bool hasError;

  /// 错误信息
  String? errorMessage;

  SpotMediaItem({
    required this.id,
    required this.spotId,
    required this.type,
    required this.url,
    this.thumbnailUrl,
    required this.filename,
    this.description,
    this.sortOrder = 0,
    this.fileSize,
    this.mimeType,
    this.duration,
    required this.created,
    required this.updated,
    // 运行时状态初始值
    this.cachedFile,
    this.cachedThumbnailFile,
    this.signedUrl,
    this.signedThumbnailUrl,
    this.isLoading = false,
    this.downloadProgress = 0.0,
    this.hasError = false,
    this.errorMessage,
  });

  /// 从SpotPhoto创建媒体项目
  factory SpotMediaItem.fromSpotPhoto(dynamic spotPhoto) {
    // 根据MIME类型和文件名判断是图片还是视频
    final mimeType = spotPhoto.mimeType ?? '';
    final filename = spotPhoto.filename ?? '';
    final type = _determineMediaType(mimeType, filename);

    return SpotMediaItem(
      id: spotPhoto.id,
      spotId: spotPhoto.spotId,
      type: type,
      url: spotPhoto.url,
      thumbnailUrl: spotPhoto.thumbnailUrl,
      filename: spotPhoto.filename,
      description: spotPhoto.description,
      sortOrder: spotPhoto.sortOrder,
      fileSize: spotPhoto.fileSize,
      mimeType: spotPhoto.mimeType,
      duration: null, // 视频时长需要单独获取
      created: spotPhoto.created,
      updated: spotPhoto.updated,
    );
  }

  /// 确定媒体类型
  static MediaType _determineMediaType(String mimeType, String filename) {
    // 优先使用MIME类型
    if (mimeType.isNotEmpty) {
      if (mimeType.startsWith('video/')) {
        print('🎬 [媒体类型] 通过MIME类型识别为视频: $filename (MIME: $mimeType)');
        return MediaType.video;
      }
      if (mimeType.startsWith('image/')) {
        print('🖼️ [媒体类型] 通过MIME类型识别为图片: $filename (MIME: $mimeType)');
        return MediaType.image;
      }
    }

    // 后备：使用文件扩展名
    if (filename.isNotEmpty) {
      final ext = filename.toLowerCase().split('.').last;
      if (['mp4', 'mov', 'avi', 'mkv', 'webm', '3gp', 'flv'].contains(ext)) {
        print('🎬 [媒体类型] 通过扩展名识别为视频: $filename (扩展名: $ext)');
        return MediaType.video;
      }
      if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].contains(ext)) {
        print('🖼️ [媒体类型] 通过扩展名识别为图片: $filename (扩展名: $ext)');
        return MediaType.image;
      }
    }

    // 默认为图片
    print('⚠️ [媒体类型] 无法识别类型，默认为图片: $filename (MIME: $mimeType)');
    return MediaType.image;
  }

  /// 从JSON创建媒体项目
  factory SpotMediaItem.fromJson(Map<String, dynamic> json) {
    final mimeType = json['mime_type'] ?? '';
    final type =
        mimeType.startsWith('video/') ? MediaType.video : MediaType.image;

    Duration? duration;
    if (json['duration'] != null) {
      duration = Duration(seconds: json['duration']);
    }

    return SpotMediaItem(
      id: json['id'] ?? '',
      spotId: json['spot_id'] ?? '',
      type: type,
      url: json['url'] ?? '',
      thumbnailUrl: json['thumbnail_url'],
      filename: json['filename'] ?? '',
      description: json['description'],
      sortOrder: json['sort_order'] ?? 0,
      fileSize: json['file_size'],
      mimeType: json['mime_type'],
      duration: duration,
      created: DateTime.parse(
        json['created'] ?? DateTime.now().toIso8601String(),
      ),
      updated: DateTime.parse(
        json['updated'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'spot_id': spotId,
      'type': type.name,
      'url': url,
      'thumbnail_url': thumbnailUrl,
      'filename': filename,
      'description': description,
      'sort_order': sortOrder,
      'file_size': fileSize,
      'mime_type': mimeType,
      'duration': duration?.inSeconds,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  /// 是否为图片
  bool get isImage => type == MediaType.image;

  /// 是否为视频
  bool get isVideo => type == MediaType.video;

  /// 是否已缓存
  bool get isCached => cachedFile != null && cachedFile!.existsSync();

  /// 是否有缩略图缓存
  bool get hasThumbnailCache =>
      cachedThumbnailFile != null && cachedThumbnailFile!.existsSync();

  /// 获取显示用的URL（优先使用缓存，然后签名URL，最后原始URL）
  String get displayUrl {
    if (isCached) return cachedFile!.path;
    return signedUrl ?? url;
  }

  /// 获取缩略图显示URL（优先使用缓存，然后签名URL，最后原始URL）
  String get displayThumbnailUrl {
    if (hasThumbnailCache) return cachedThumbnailFile!.path;
    return signedThumbnailUrl ?? thumbnailUrl ?? url;
  }

  /// 获取下载用的URL（优先使用签名URL）
  String get downloadUrl => signedUrl ?? url;

  /// 获取缩略图下载URL（优先使用签名URL）
  String get downloadThumbnailUrl => signedThumbnailUrl ?? thumbnailUrl ?? url;

  /// 重置加载状态
  void resetLoadingState() {
    isLoading = false;
    downloadProgress = 0.0;
    hasError = false;
    errorMessage = null;
  }

  /// 设置加载状态
  void setLoadingState({
    bool? loading,
    double? progress,
    bool? error,
    String? errorMsg,
  }) {
    if (loading != null) isLoading = loading;
    if (progress != null) downloadProgress = progress;
    if (error != null) hasError = error;
    if (errorMsg != null) errorMessage = errorMsg;
  }

  /// 设置缓存文件
  void setCachedFile(File file) {
    cachedFile = file;
    isLoading = false;
    downloadProgress = 1.0;
    hasError = false;
    errorMessage = null;
  }

  /// 设置缓存缩略图
  void setCachedThumbnail(File file) {
    cachedThumbnailFile = file;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SpotMediaItem &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SpotMediaItem{id: $id, type: $type, filename: $filename, isLoading: $isLoading}';
  }
}
