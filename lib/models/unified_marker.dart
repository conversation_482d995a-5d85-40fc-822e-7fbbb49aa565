import 'package:latlong2/latlong.dart';

/// 标记类型枚举
enum MarkerType {
  spot('spot'),
  activity('activity');

  const MarkerType(this.value);
  final String value;

  static MarkerType fromString(String value) {
    switch (value) {
      case 'spot':
        return MarkerType.spot;
      case 'activity':
        return MarkerType.activity;
      default:
        throw ArgumentError('Unknown marker type: $value');
    }
  }
}

/// 统一标记抽象基类
/// 
/// 为钓点和活动标记提供统一的数据结构和接口
/// 支持过滤、排序和缓存功能
abstract class UnifiedMarker {
  /// 唯一标识符
  final String id;
  
  /// 标记类型
  final MarkerType type;
  
  /// 标记名称/标题
  final String name;
  
  /// 地理位置
  final LatLng location;
  
  /// 创建者用户ID
  final String userId;
  
  /// 创建时间
  final DateTime created;
  
  /// 更新时间
  final DateTime updated;
  
  /// 状态 (active, inactive, reported, cancelled等)
  final String status;
  
  /// 是否被举报
  final bool isReported;
  
  /// 是否过期
  final bool isExpired;
  
  /// 缩略图URL
  final String? thumbnailUrl;
  
  /// 是否是我发布的
  final bool isMine;
  
  /// 是否在我的收藏中
  final bool isFavorited;

  const UnifiedMarker({
    required this.id,
    required this.type,
    required this.name,
    required this.location,
    required this.userId,
    required this.created,
    required this.updated,
    required this.status,
    this.isReported = false,
    this.isExpired = false,
    this.thumbnailUrl,
    this.isMine = false,
    this.isFavorited = false,
  });

  /// 计算优先级分数
  /// 
  /// 基于多维度权重算法计算标记的显示优先级
  /// [config] 过滤配置，包含权重设置
  /// 返回0.0-1.0之间的分数，分数越高优先级越高
  double calculatePriorityScore(dynamic config);

  /// 检查是否匹配过滤条件
  /// 
  /// [config] 过滤配置
  /// 返回true表示匹配过滤条件，应该显示
  bool matchesFilter(dynamic config);

  /// 转换为JSON
  Map<String, dynamic> toJson();

  /// 从JSON创建实例的工厂方法
  /// 
  /// 由于这是抽象类，实际的fromJson实现在具体子类中
  static UnifiedMarker fromJson(Map<String, dynamic> json) {
    final type = MarkerType.fromString(json['type']);
    switch (type) {
      case MarkerType.spot:
        return SpotMarker.fromJson(json);
      case MarkerType.activity:
        return ActivityMarker.fromJson(json);
    }
  }

  /// 获取基础JSON数据（供子类使用）
  Map<String, dynamic> getBaseJson() {
    return {
      'id': id,
      'type': type.value,
      'name': name,
      'location': {
        'lat': location.latitude,
        'lon': location.longitude,
      },
      'user_id': userId,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
      'status': status,
      'is_reported': isReported,
      'is_expired': isExpired,
      'thumbnail_url': thumbnailUrl,
      'is_mine': isMine,
      'is_favorited': isFavorited,
    };
  }

  /// 从基础JSON数据解析通用字段（供子类使用）
  static Map<String, dynamic> parseBaseFields(Map<String, dynamic> json) {
    final locationData = json['location'] as Map<String, dynamic>;
    return {
      'id': json['id'],
      'type': MarkerType.fromString(json['type']),
      'name': json['name'],
      'location': LatLng(
        locationData['lat'] as double,
        locationData['lon'] as double,
      ),
      'userId': json['user_id'],
      'created': DateTime.parse(json['created']),
      'updated': DateTime.parse(json['updated']),
      'status': json['status'],
      'isReported': json['is_reported'] ?? false,
      'isExpired': json['is_expired'] ?? false,
      'thumbnailUrl': json['thumbnail_url'],
      'isMine': json['is_mine'] ?? false,
      'isFavorited': json['is_favorited'] ?? false,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UnifiedMarker && other.id == id && other.type == type;
  }

  @override
  int get hashCode => Object.hash(id, type);

  @override
  String toString() {
    return '${type.value.toUpperCase()}Marker(id: $id, name: $name)';
  }
}

/// 钓点标记具体实现
class SpotMarker extends UnifiedMarker {
  /// 是否有实地标签
  final bool isOnSite;
  
  /// 是否有实拍标签
  final bool hasRealPhotos;
  
  /// 是否有照片
  final bool hasPhotos;
  
  /// 点赞数量
  final int likesCount;
  
  /// 钓点类型
  final String? spotType;
  
  /// 鱼类信息
  final String? fishTypes;

  const SpotMarker({
    required super.id,
    required super.name,
    required super.location,
    required super.userId,
    required super.created,
    required super.updated,
    required super.status,
    super.isReported,
    super.isExpired,
    super.thumbnailUrl,
    super.isMine,
    super.isFavorited,
    this.isOnSite = false,
    this.hasRealPhotos = false,
    this.hasPhotos = false,
    this.likesCount = 0,
    this.spotType,
    this.fishTypes,
  }) : super(type: MarkerType.spot);

  @override
  double calculatePriorityScore(dynamic config) {
    double score = 0.0;
    
    // 个人关联度 (40%)
    if (isMine) score += 0.4;
    if (isFavorited) score += 0.2;
    
    // 内容质量 (30%)
    if (isOnSite) score += 0.15;
    if (hasRealPhotos) score += 0.15;
    if (hasPhotos && !hasRealPhotos) score += 0.05;
    
    // 社交热度 (20%)
    score += (likesCount / 100.0).clamp(0.0, 0.2);
    
    // 时间新鲜度 (10%)
    final daysSinceCreated = DateTime.now().difference(created).inDays;
    score += (1.0 / (1.0 + daysSinceCreated * 0.1)) * 0.1;
    
    return score.clamp(0.0, 1.0);
  }

  @override
  bool matchesFilter(dynamic config) {
    // 基础条件检查
    if (config.showExpired == false && isExpired) return false;
    if (config.requireOnSite == true && !isOnSite) return false;
    if (config.requireRealPhotos == true && !hasRealPhotos) return false;
    
    // 社交条件检查
    if (config.requirePositiveLikes == true && likesCount <= 0) return false;
    if (config.onlyFavorites == true && !isFavorited) return false;
    
    // 个人关联检查
    if (config.onlyMySpots == true && !isMine) return false;
    
    return true;
  }

  @override
  Map<String, dynamic> toJson() {
    final baseJson = getBaseJson();
    baseJson.addAll({
      'is_on_site': isOnSite,
      'has_real_photos': hasRealPhotos,
      'has_photos': hasPhotos,
      'likes_count': likesCount,
      'spot_type': spotType,
      'fish_types': fishTypes,
    });
    return baseJson;
  }

  static SpotMarker fromJson(Map<String, dynamic> json) {
    final baseFields = UnifiedMarker.parseBaseFields(json);
    return SpotMarker(
      id: baseFields['id'],
      name: baseFields['name'],
      location: baseFields['location'],
      userId: baseFields['userId'],
      created: baseFields['created'],
      updated: baseFields['updated'],
      status: baseFields['status'],
      isReported: baseFields['isReported'],
      isExpired: baseFields['isExpired'],
      thumbnailUrl: baseFields['thumbnailUrl'],
      isMine: baseFields['isMine'],
      isFavorited: baseFields['isFavorited'],
      isOnSite: json['is_on_site'] ?? false,
      hasRealPhotos: json['has_real_photos'] ?? false,
      hasPhotos: json['has_photos'] ?? false,
      likesCount: json['likes_count'] ?? 0,
      spotType: json['spot_type'],
      fishTypes: json['fish_types'],
    );
  }
}

/// 活动标记具体实现
class ActivityMarker extends UnifiedMarker {
  /// 活动类型
  final String activityType;
  
  /// 开始时间
  final DateTime startTime;
  
  /// 当前参与人数
  final int currentParticipants;
  
  /// 最大参与人数
  final int maxParticipants;
  
  /// 是否有图片
  final bool hasImages;
  
  /// 是否已加入
  final bool isJoined;
  
  /// 是否是我创建的活动
  final bool isMyActivity;

  const ActivityMarker({
    required super.id,
    required super.name,
    required super.location,
    required super.userId,
    required super.created,
    required super.updated,
    required super.status,
    super.isReported,
    super.isExpired,
    super.thumbnailUrl,
    super.isMine,
    super.isFavorited,
    required this.activityType,
    required this.startTime,
    this.currentParticipants = 0,
    this.maxParticipants = 0,
    this.hasImages = false,
    this.isJoined = false,
    this.isMyActivity = false,
  }) : super(type: MarkerType.activity);

  @override
  double calculatePriorityScore(dynamic config) {
    double score = 0.0;
    
    // 个人关联度 (40%)
    if (isMyActivity) score += 0.4;
    if (isJoined) score += 0.2;
    if (isFavorited) score += 0.1;
    
    // 内容质量 (30%)
    if (hasImages) score += 0.15;
    // 活动参与度作为质量指标
    if (maxParticipants > 0) {
      final participationRate = currentParticipants / maxParticipants;
      score += participationRate * 0.15;
    }
    
    // 社交热度 (20%) - 基于参与人数
    score += (currentParticipants / 50.0).clamp(0.0, 0.2);
    
    // 时间新鲜度 (10%) - 基于开始时间
    final now = DateTime.now();
    if (startTime.isAfter(now)) {
      // 未来的活动，越近优先级越高
      final hoursUntilStart = startTime.difference(now).inHours;
      score += (1.0 / (1.0 + hoursUntilStart * 0.01)) * 0.1;
    } else {
      // 已开始的活动，优先级较低
      score += 0.02;
    }
    
    return score.clamp(0.0, 1.0);
  }

  @override
  bool matchesFilter(dynamic config) {
    // 基础条件检查
    if (config.showExpired == false && isExpired) return false;
    
    // 个人关联检查
    if (config.onlyMyActivities == true && !isMyActivity) return false;
    if (config.onlyJoinedActivities == true && !isJoined) return false;
    
    return true;
  }

  @override
  Map<String, dynamic> toJson() {
    final baseJson = getBaseJson();
    baseJson.addAll({
      'activity_type': activityType,
      'start_time': startTime.toIso8601String(),
      'current_participants': currentParticipants,
      'max_participants': maxParticipants,
      'has_images': hasImages,
      'is_joined': isJoined,
      'is_my_activity': isMyActivity,
    });
    return baseJson;
  }

  static ActivityMarker fromJson(Map<String, dynamic> json) {
    final baseFields = UnifiedMarker.parseBaseFields(json);
    return ActivityMarker(
      id: baseFields['id'],
      name: baseFields['name'],
      location: baseFields['location'],
      userId: baseFields['userId'],
      created: baseFields['created'],
      updated: baseFields['updated'],
      status: baseFields['status'],
      isReported: baseFields['isReported'],
      isExpired: baseFields['isExpired'],
      thumbnailUrl: baseFields['thumbnailUrl'],
      isMine: baseFields['isMine'],
      isFavorited: baseFields['isFavorited'],
      activityType: json['activity_type'],
      startTime: DateTime.parse(json['start_time']),
      currentParticipants: json['current_participants'] ?? 0,
      maxParticipants: json['max_participants'] ?? 0,
      hasImages: json['has_images'] ?? false,
      isJoined: json['is_joined'] ?? false,
      isMyActivity: json['is_my_activity'] ?? false,
    );
  }
}
