import 'unified_marker.dart';

/// 标记ID信息轻量级数据类
///
/// 用于第一步懒加载，只包含ID、更新时间和类型信息
/// 用于与缓存进行对比，判断哪些标记需要更新
class MarkerIdInfo {
  /// 标记唯一标识符
  final String id;

  /// 最后更新时间
  final DateTime updated;

  /// 标记类型
  final MarkerType type;

  const MarkerIdInfo({
    required this.id,
    required this.updated,
    required this.type,
  });

  /// 从JSON创建实例
  factory MarkerIdInfo.fromJson(Map<String, dynamic> json) {
    return MarkerIdInfo(
      id: json['id'] as String,
      updated: DateTime.parse(json['updated'] as String),
      type: MarkerType.fromString(json['type'] as String?),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {'id': id, 'updated': updated.toIso8601String(), 'type': type.value};
  }

  /// 检查是否比另一个MarkerIdInfo更新
  bool isNewerThan(MarkerIdInfo other) {
    return updated.isAfter(other.updated);
  }

  /// 检查是否与另一个MarkerIdInfo的更新时间相同
  bool isSameUpdateTime(MarkerIdInfo other) {
    return updated.isAtSameMomentAs(other.updated);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MarkerIdInfo &&
        other.id == id &&
        other.type == type &&
        other.updated.isAtSameMomentAs(updated);
  }

  @override
  int get hashCode => Object.hash(id, type, updated);

  @override
  String toString() {
    return 'MarkerIdInfo(id: $id, type: ${type.value}, updated: $updated)';
  }

  /// 创建缓存键
  ///
  /// 用于在缓存中唯一标识这个标记
  String get cacheKey => '${type.value}_$id';

  /// 从缓存键解析出ID和类型
  static MarkerIdInfo? fromCacheKey(String cacheKey, DateTime updated) {
    final parts = cacheKey.split('_');
    if (parts.length != 2) return null;

    try {
      final type = MarkerType.fromString(parts[0]);
      final id = parts[1];
      return MarkerIdInfo(id: id, updated: updated, type: type);
    } catch (e) {
      return null;
    }
  }
}

/// 标记ID信息列表的扩展方法
extension MarkerIdInfoListExtensions on List<MarkerIdInfo> {
  /// 按类型分组
  Map<MarkerType, List<MarkerIdInfo>> groupByType() {
    final Map<MarkerType, List<MarkerIdInfo>> grouped = {};
    for (final info in this) {
      grouped.putIfAbsent(info.type, () => []).add(info);
    }
    return grouped;
  }

  /// 获取所有钓点ID
  List<String> get spotIds {
    return where(
      (info) => info.type == MarkerType.spot,
    ).map((info) => info.id).toList();
  }

  /// 获取所有活动ID
  List<String> get activityIds {
    return where(
      (info) => info.type == MarkerType.activity,
    ).map((info) => info.id).toList();
  }

  /// 获取所有ID
  List<String> get allIds {
    return map((info) => info.id).toList();
  }

  /// 获取所有缓存键
  List<String> get cacheKeys {
    return map((info) => info.cacheKey).toList();
  }

  /// 按更新时间排序（最新的在前）
  List<MarkerIdInfo> sortedByUpdated() {
    final sorted = List<MarkerIdInfo>.from(this);
    sorted.sort((a, b) => b.updated.compareTo(a.updated));
    return sorted;
  }

  /// 过滤出比指定时间更新的标记
  List<MarkerIdInfo> newerThan(DateTime threshold) {
    return where((info) => info.updated.isAfter(threshold)).toList();
  }

  /// 过滤出指定类型的标记
  List<MarkerIdInfo> ofType(MarkerType type) {
    return where((info) => info.type == type).toList();
  }

  /// 转换为Map，以ID为键
  Map<String, MarkerIdInfo> toIdMap() {
    return {for (final info in this) info.id: info};
  }

  /// 转换为Map，以缓存键为键
  Map<String, MarkerIdInfo> toCacheKeyMap() {
    return {for (final info in this) info.cacheKey: info};
  }
}
