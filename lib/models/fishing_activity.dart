import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'fishing_spot.dart';

/// 钓鱼活动模型类
///
/// 专门用于"一起钓鱼"约钓活动
/// 适配 PocketBase fishing_activities 集合结构
class FishingActivity {
  /// 唯一标识符 (PocketBase 自动生成)
  final String id;

  /// 活动标题 (必填，对应数据库 title 字段)
  final String title;

  /// 描述信息 (可选)
  String description;

  /// 活动位置 (geoPoint字段)
  final Map<String, dynamic>? location;

  /// 开始时间 (对应数据库 start_time 字段)
  final DateTime startTime;

  /// 持续时长（小时，对应数据库 duration 字段）
  final double duration;

  /// 最大参与人数
  final int maxParticipants;

  /// 当前参与人数
  final int currentParticipants;

  /// 创建者用户ID (对应数据库 creator_id 字段)
  final String creatorId;

  /// 创建者用户名 (从关联的用户数据获取)
  final String? creatorName;

  /// 状态 (active, cancelled, completed)
  String status;

  /// 图片信息 (JSON格式)
  final Map<String, dynamic>? images;

  /// 活动类型 (lure, night, platform)
  final String activityType;

  /// 群聊ID (可选，暂时不使用)
  final String? groupChatId;

  /// 创建时间 (PocketBase 自动管理)
  final DateTime created;

  /// 更新时间 (PocketBase 自动管理)
  final DateTime updated;

  FishingActivity({
    required this.id,
    required this.title,
    this.description = '',
    this.location,
    required this.startTime,
    this.duration = 2.0,
    this.maxParticipants = 10,
    this.currentParticipants = 1,
    required this.creatorId,
    this.creatorName,
    this.status = 'active',
    this.images,
    this.activityType = 'lure',
    this.groupChatId,
    required this.created,
    required this.updated,
  });

  /// 安全解析images字段
  static Map<String, dynamic>? _parseImagesField(dynamic imagesData) {
    try {
      if (imagesData == null) return null;

      if (imagesData is Map<String, dynamic>) {
        // 验证images字段的结构
        if (imagesData.containsKey('images') && imagesData['images'] is List) {
          return imagesData;
        }
      }

      // 如果数据格式不正确，返回null
      debugPrint('⚠️ [活动模型] images字段格式不正确: $imagesData');
      return null;
    } catch (e) {
      debugPrint('❌ [活动模型] 解析images字段失败: $e');
      return null;
    }
  }

  /// 安全解析DateTime字段
  static DateTime _parseDateTime(dynamic dateData, {DateTime? fallback}) {
    try {
      if (dateData == null) {
        return fallback ?? DateTime.now();
      }

      if (dateData is String) {
        return DateTime.parse(dateData);
      }

      if (dateData is DateTime) {
        return dateData;
      }

      // 如果是其他类型，尝试转换为字符串再解析
      return DateTime.parse(dateData.toString());
    } catch (e) {
      debugPrint('⚠️ [活动模型] 解析DateTime失败: $e, 数据: $dateData');
      return fallback ?? DateTime.now();
    }
  }

  /// 从JSON创建钓鱼活动对象
  factory FishingActivity.fromJson(Map<String, dynamic> json) {
    try {
      debugPrint('🔍 [活动模型] 开始解析活动数据: ${json['id']}');

      // 处理location字段（geoPoint类型）
      Map<String, dynamic>? locationData;
      if (json['location'] != null) {
        locationData =
            json['location'] is Map<String, dynamic> ? json['location'] : null;
      } else if (json['latitude'] != null && json['longitude'] != null) {
        // 向后兼容：如果有旧的latitude/longitude字段，转换为location格式
        locationData = {
          'lat': (json['latitude'] as num).toDouble(),
          'lon': (json['longitude'] as num).toDouble(),
        };
      }
      debugPrint('🔍 [活动模型] location字段处理完成');

      return FishingActivity(
        id: json['id'],
        title: json['title'] ?? '',
        description: json['description'] ?? '',
        location: locationData,
        startTime: _parseDateTime(json['start_time']),
        duration: (json['duration'] as num?)?.toDouble() ?? 2.0,
        maxParticipants: json['max_participants'] ?? 10,
        currentParticipants: json['current_participants'] ?? 1,
        creatorId: json['creator_id'] ?? '',
        creatorName: json['creator_name'],
        status: json['status'] ?? 'active',
        images: _parseImagesField(json['images']),
        activityType: json['activity_type'] ?? 'lure',
        groupChatId: json['group_chat_id'],
        created: _parseDateTime(json['created']),
        updated: _parseDateTime(json['updated']),
      );
    } catch (e) {
      debugPrint('❌ [活动模型] fromJson失败: $e');
      debugPrint('❌ [活动模型] 输入数据: $json');
      rethrow;
    }
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'location': location,
      'start_time': startTime.toIso8601String(),
      'duration': duration,
      'max_participants': maxParticipants,
      'current_participants': currentParticipants,
      'creator_id': creatorId,
      'creator_name': creatorName,
      'status': status,
      'images': images,
      'activity_type': activityType,
      'group_chat_id': groupChatId,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }



  /// 获取位置的LatLng对象
  LatLng get locationLatLng {
    if (location == null) {
      throw Exception('活动位置信息不完整');
    }
    return LatLng(
      (location!['lat'] as num).toDouble(),
      (location!['lon'] as num).toDouble(),
    );
  }

  /// 获取活动结束时间
  DateTime get endTime {
    return startTime.add(
      Duration(hours: duration.toInt(), minutes: ((duration % 1) * 60).toInt()),
    );
  }

  /// 检查活动是否已过期
  bool get isExpired {
    return DateTime.now().isAfter(endTime);
  }

  /// 检查活动是否即将开始（1小时内）
  bool get isStartingSoon {
    final now = DateTime.now();
    final oneHourBefore = startTime.subtract(const Duration(hours: 1));
    return now.isAfter(oneHourBefore) && now.isBefore(startTime);
  }

  /// 检查活动是否正在进行中
  bool get isOngoing {
    final now = DateTime.now();
    return now.isAfter(startTime) && now.isBefore(endTime);
  }

  /// 获取活动状态描述
  String get statusDescription {
    if (isExpired) return '已结束';
    if (isOngoing) return '进行中';
    if (isStartingSoon) return '即将开始';
    return '等待中';
  }

  /// 转换为钓点对象（用于复用钓点标记显示）
  FishingSpot toFishingSpot() {
    return FishingSpot(
      id: id,
      name: title,
      description: description,
      location: location,
      userId: creatorId,
      userName: creatorName,
      spotEmoji: '🎣', // 约钓活动专用emoji
      fishEmoji: '🐟',
      status: status,
      created: created,
      updated: updated,
    );
  }

  /// 获取活动类型颜色
  Color getActivityTypeColor() {
    switch (activityType) {
      case 'lure':
        return const Color(0xFFFF9800); // 橙色 - 路亚
      case 'platform':
        return const Color(0xFF9C27B0); // 紫色 - 台钓
      case 'night':
        return const Color(0xFF4CAF50); // 绿色 - 夜钓
      case 'pond':
        return const Color(0xFF2196F3); // 蓝色 - 鱼塘
      case 'sea':
        return const Color(0xFF000000); // 黑色 - 海钓
      default:
        return const Color(0xFF4CAF50); // 默认绿色
    }
  }

  /// 获取活动类型显示名称
  String getActivityTypeDisplayName() {
    switch (activityType) {
      case 'lure':
        return '路亚';
      case 'platform':
        return '台钓';
      case 'night':
        return '夜钓';
      case 'pond':
        return '鱼塘';
      case 'sea':
        return '海钓';
      default:
        return '钓鱼';
    }
  }

  /// 获取默认照片路径
  String getDefaultPhoto() {
    // 根据创建者ID的奇偶性选择默认照片
    final useGirl = creatorId.hashCode % 2 == 0;
    return useGirl
        ? 'assets/images/fishingGirl6.jpg'
        : 'assets/images/fishingBoy1.jpg';
  }

  /// 获取状态指示器颜色
  Color? getStatusIndicatorColor() {
    if (isExpired) return Colors.grey;
    if (isOngoing) return Colors.green;
    if (isStartingSoon) return Colors.orange;
    return null; // 等待中不显示指示器
  }

  /// 活动是否还未开始
  bool get isUpcoming {
    final now = DateTime.now();
    return now.isBefore(startTime);
  }

  /// 活动是否已满员
  bool get isFull {
    return currentParticipants >= maxParticipants;
  }

  /// 活动是否可以加入
  bool get canJoin {
    return !isExpired && !isFull && status == 'active';
  }
}
