/// 钓点类型枚举
/// 
/// 定义了不同类型的钓点分类
enum SpotType {
  /// 野钓 - 自然水域钓鱼
  wildFishing('wild_fishing', '野钓', '🎣'),
  
  /// 海钓 - 海洋钓鱼
  seaFishing('sea_fishing', '海钓', '🌊'),
  
  /// 路亚 - 路亚钓法
  lureFishing('lure_fishing', '路亚', '🎯'),
  
  /// 收费鱼塘 - 付费钓鱼场所
  paidFishPond('paid_fish_pond', '收费鱼塘', '💰');

  const SpotType(this.value, this.displayName, this.emoji);

  /// 数据库存储值
  final String value;
  
  /// 显示名称
  final String displayName;
  
  /// 对应的emoji
  final String emoji;

  /// 从字符串值创建枚举
  static SpotType fromString(String value) {
    return SpotType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => SpotType.wildFishing, // 默认为野钓
    );
  }

  /// 获取所有钓点类型的选项列表
  static List<Map<String, String>> get options {
    return SpotType.values.map((type) => {
      'value': type.value,
      'label': type.displayName,
      'emoji': type.emoji,
    }).toList();
  }

  /// 获取钓点类型的描述
  String get description {
    switch (this) {
      case SpotType.wildFishing:
        return '自然水域，免费钓鱼';
      case SpotType.seaFishing:
        return '海洋钓鱼，体验海钓乐趣';
      case SpotType.lureFishing:
        return '路亚钓法，技巧性钓鱼';
      case SpotType.paidFishPond:
        return '收费鱼塘，设施完善';
    }
  }

  /// 获取钓点类型的颜色
  String get colorName {
    switch (this) {
      case SpotType.wildFishing:
        return 'green'; // 绿色代表自然
      case SpotType.seaFishing:
        return 'blue'; // 蓝色代表海洋
      case SpotType.lureFishing:
        return 'purple'; // 紫色代表技巧
      case SpotType.paidFishPond:
        return 'orange'; // 橙色代表商业
    }
  }
}
