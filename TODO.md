重要：钓点详情页面
重要：一起钓鱼页面
重要：钓鱼动态页面
重要：钓点编辑修改页面
重要：聊天页面待完善
重要：探索页面还无法工作
头像无法显示
图片无法放大缩小
剪贴板里有钓点无法清楚导致每次启动都提示。
程序启动时的黑屏时间太长

SpotAccessCache只在定义文件中出现，没有在任何其他地方被引用或使用

解决后端ip地址硬编码的问题，使用这两个方式获取后端ip：
curl -s "https://doh.pub/dns-query?name=app.19840112.xyz&type=A"
curl -s "https://dns.alidns.com/resolve?name=app.19840112.xyz&type=A"

优化各个页面从后端数据库加载的内容。地图页面的标签，只需要从数据库请求位置坐标，id ,图标，这三个信息。其他信息都不用。

增加一个搜索指示标记，搜索栏搜索到地址后，点击跳转，会显示这个地址标记，这个地址标记只有一个。

钓点详情页中的评论卡片，下滑评论时首先滑动评论列表，滑动到底后，应该能变成滑动评论页面。上滑也一样。

钓点类型增加手竿，路亚

bug： 实地标签一开始会亮。移动一下才会判断是否是实地钓点。

问题已经解决，下面进行下一个任务。
如前的过滤页面太复杂。帮我重新设计一个UI.以下是要求

1 过滤页面的UI是个浮动窗口，悬浮于地图页面上。
2 第一行，显示当前地图范围内钓点和活动的总数，以及过滤后的数量。格式 23/50
横线
3 第二部分是常开项目，关闭后过滤掉满足条件的标记。
    钓点分享    常开
    活动分享    常开
    路亚标点    常开
    传统钓点    常开
    收费钓点    常开
    
横线
4 第三部分是常关按钮 打开后过虑掉未满足条件的标记
    实地标签    常关
    照片标签    常关
    过期活动    常关
    较多点赞    常关
横线
第5部分，最大显示数量拖动条 默认50,最大200

更多设置连接，点击后显示现有过滤设置页面的优先级标签的内容